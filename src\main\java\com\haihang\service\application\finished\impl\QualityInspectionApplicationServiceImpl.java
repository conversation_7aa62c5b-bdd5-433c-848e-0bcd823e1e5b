package com.haihang.service.application.finished.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.haihang.factory.QualityInspectionHandlerFactory;
import com.haihang.handler.record.QualityInspectionHandler;
import com.haihang.mapper.application.QualityInspectionApplicationMapper;
import com.haihang.mapper.common.CommonCompanyMapper;
import com.haihang.mapper.common.CompanyMapper;
import com.haihang.mapper.common.ProductTypeForSortMapper;
import com.haihang.mapper.operationLog.ApplicationOperationLogMapper;
import com.haihang.mapper.outbound.OutboundReportTemplateMapper;
import com.haihang.mapper.record.BasicInformationMapper;
import com.haihang.mapper.record.BasicInformationNotSavedMapper;
import com.haihang.mapper.record.ItemPriorityForSortMapper;
import com.haihang.mapper.record.MergedUnSubmittedRecordMapper;
import com.haihang.mapper.user.UserMapper;
import com.haihang.model.DO.application.QualityInspectionApplication;
import com.haihang.model.DO.common.CommonCompany;
import com.haihang.model.DO.common.Company;
import com.haihang.model.DO.common.ProductTypeForSort;
import com.haihang.model.DO.operationLog.ApplicationOperationLog;
import com.haihang.model.DO.outbound.OutboundReportTemplate;
import com.haihang.model.DO.record.*;
import com.haihang.model.DO.user.User;
import com.haihang.model.DTO.application.ApplicationDTO;
import com.haihang.model.DTO.application.ApplicationReleaseDTO;
import com.haihang.model.DTO.application.ApplicationReturnDTO;
import com.haihang.model.DTO.record.finished.SamplingItemDTO;
import com.haihang.model.Query.application.ApplicationQuery;
import com.haihang.model.Query.application.QualityInspectionApplicationSearch;
import com.haihang.model.Query.common.ProductTypeQuery;
import com.haihang.model.Query.operationLog.OperationLogQuery;
import com.haihang.model.VO.application.QualityInspectionApplicationCount;
import com.haihang.model.VO.application.SamplingItemVO;
import com.haihang.model.VO.common.CascaderLabelVO;
import com.haihang.model.VO.operationLog.ApplicationOperationLogVO;
import com.haihang.model.VO.record.CreatAnalysisRecordFormData;
import com.haihang.model.VO.record.ProductTypeLabelVO;
import com.haihang.model.VO.report.RecordResultVO;
import com.haihang.model.VO.report.ReportVO;
import com.haihang.service.application.finished.QualityInspectionApplicationService;
import com.haihang.utils.common.MapStringUtil;
import com.spire.xls.*;
import java.io.File;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class QualityInspectionApplicationServiceImpl
		extends ServiceImpl<QualityInspectionApplicationMapper, QualityInspectionApplication>
		implements QualityInspectionApplicationService {

	private final UserMapper userMapper;
	private final QualityInspectionApplicationMapper qualityInspectionApplicationMapper;
	private final BasicInformationNotSavedMapper basicInformationNotSavedMapper;
	private final BasicInformationMapper basicInformationMapper;
	private final ProductTypeForSortMapper productTypeForSortMapper;
	private final ItemPriorityForSortMapper itemPriorityForSortMapper;
	private final MergedUnSubmittedRecordMapper mergedUnSubmittedRecordMapper;
	private final CompanyMapper companyMapper;
	private final CommonCompanyMapper commonCompanyMapper;
	private final ApplicationOperationLogMapper applicationOperationLogMapper;
	private final OutboundReportTemplateMapper outboundReportTemplateMapper;

	@Value("${path-config.report-path}")
	private String reportPath;

	@Override
	public IPage<QualityInspectionApplication> getCountPage(
			int currentPage,
			int pageSize,
			QualityInspectionApplicationSearch qualityInspectionApplicationSearch) {
		User user = userMapper.selectById(qualityInspectionApplicationSearch.getUserId());
		QueryWrapper<QualityInspectionApplication> queryWrapper = new QueryWrapper<>();
		// 统计
		queryWrapper.select(
				"cp_mc as productType, count(*) as allCount, count(is_handled = 0 or null) as notHandledCount, count(is_handled = 1 or null) as handledCount, count(result = 0 or null) as notQualifiedCount, count(result = 1 or result = 2 or null) as qualifiedCount, count(is_released = 0 or null) as notReleasedCount, count(is_released = 1 or null) as releasedCount, count(is_released is null and is_handled = 1 or null) as notCheckedCount, count(is_released is not null or null) as checkedCount");
		// 分组
		queryWrapper.groupBy("productType");
		// 条件查询
		queryWrapper
				.lambda()
				.like(
						Strings.isNotEmpty(qualityInspectionApplicationSearch.getProductType()),
						QualityInspectionApplication::getProductType,
						qualityInspectionApplicationSearch.getProductType());
		queryWrapper
				.lambda()
				.like(
						!(user.getId() == 79 || user.getId() == 610 || user.getId() == 265),
						QualityInspectionApplication::getLinkId,
						user.getLinkId());
		// 分页
		IPage<QualityInspectionApplication> page = new Page<>(currentPage, pageSize);
		qualityInspectionApplicationMapper.selectPage(page, queryWrapper);
		List<QualityInspectionApplication> qualityInspectionApplicationList = new ArrayList<>();
		if (qualityInspectionApplicationSearch.getHandleOnly() != null
				&& qualityInspectionApplicationSearch.getHandleOnly()) {
			page.getRecords()
					.forEach(
							record -> {
								if (record.getNotHandledCount() != 0) {
									qualityInspectionApplicationList.add(record);
								}
							});
			page.setRecords(qualityInspectionApplicationList);
		}
		if (qualityInspectionApplicationSearch.getCheckOnly() != null
				&& qualityInspectionApplicationSearch.getCheckOnly()) {
			page.getRecords()
					.forEach(
							record -> {
								if (record.getNotCheckedCount() != 0) {
									qualityInspectionApplicationList.add(record);
								}
							});
			page.setRecords(qualityInspectionApplicationList);
		}

		// 分页泛型转换
		page.convert(
				application -> {
					QualityInspectionApplicationCount applicationCount = new QualityInspectionApplicationCount();
					// 复制属性
					BeanUtils.copyProperties(application, applicationCount);
					return applicationCount;
				});
		return page;
	}

	@Override
	public IPage<QualityInspectionApplication> getPage(
			int currentPage,
			int pageSize,
			QualityInspectionApplicationSearch qualityInspectionApplicationSearch) {
		User user = userMapper.selectById(qualityInspectionApplicationSearch.getUserId());
		LambdaQueryWrapper<QualityInspectionApplication> lambdaQueryWrapper = new LambdaQueryWrapper<QualityInspectionApplication>()
				// 产品状态
				.eq(
						ObjectUtil.isNotNull(qualityInspectionApplicationSearch.getSemiFinished()),
						QualityInspectionApplication::getSemiFinished,
						qualityInspectionApplicationSearch.getSemiFinished())
				.eq(
						QualityInspectionApplication::getLinkId,
						qualityInspectionApplicationSearch.getLinkId())
				.likeRight(
						user.getId() == 827, QualityInspectionApplication::getProductCategoryNumber, "0105")
				.like(
						Strings.isNotEmpty(qualityInspectionApplicationSearch.getProductionBatch()),
						QualityInspectionApplication::getProductionBatch,
						qualityInspectionApplicationSearch.getProductionBatch())
				.like(
						Strings.isNotEmpty(qualityInspectionApplicationSearch.getProductType()),
						QualityInspectionApplication::getProductType,
						qualityInspectionApplicationSearch.getProductType())
				.eq(
						qualityInspectionApplicationSearch.getHandle() != null,
						QualityInspectionApplication::getIsHandled,
						qualityInspectionApplicationSearch.getHandle())
				.eq(
						qualityInspectionApplicationSearch.getQualify() != null,
						QualityInspectionApplication::getResult,
						qualityInspectionApplicationSearch.getQualify());
		if (qualityInspectionApplicationSearch.getCheck() != null
				&& qualityInspectionApplicationSearch.getCheck()) {
			lambdaQueryWrapper.isNotNull(QualityInspectionApplication::getIsReleased);
		} else if (qualityInspectionApplicationSearch.getCheck() != null) {
			lambdaQueryWrapper.isNull(QualityInspectionApplication::getIsReleased);
		}
		lambdaQueryWrapper
				.eq(
						qualityInspectionApplicationSearch.getRelease() != null,
						QualityInspectionApplication::getIsReleased,
						qualityInspectionApplicationSearch.getRelease())
				.eq(QualityInspectionApplication::getIsReturned, false)
				.eq(
						qualityInspectionApplicationSearch.getSemiFinished() != null,
						QualityInspectionApplication::getSemiFinished,
						qualityInspectionApplicationSearch.getSemiFinished())
				.ge(
						Strings.isNotEmpty(qualityInspectionApplicationSearch.getStartDate()),
						QualityInspectionApplication::getApplicationDate,
						qualityInspectionApplicationSearch.getStartDate())
				.le(
						Strings.isNotEmpty(qualityInspectionApplicationSearch.getEndDate()),
						QualityInspectionApplication::getApplicationDate,
						qualityInspectionApplicationSearch.getEndDate() + " 23:59:59")
				.orderByAsc(QualityInspectionApplication::getProductType)
				.orderByAsc(QualityInspectionApplication::getApplicationDate);
		IPage<QualityInspectionApplication> page = new Page<>(currentPage, pageSize);
		qualityInspectionApplicationMapper.selectPage(page, lambdaQueryWrapper);
		page.convert(
				qualityInspectionApplication -> {
					String productType = qualityInspectionApplication.getProductType();
					// 替换产品类型的中文括号为英文括号
					qualityInspectionApplication.setProductType(
							productType.replace("（", "(").replace("）", ")"));

					if (Strings.isEmpty(qualityInspectionApplication.getCustomerName().trim())) {
						qualityInspectionApplication.setCustomerName("预生产");
					}
					LocalDateTime localDateTime = DateUtil
							.toLocalDateTime(qualityInspectionApplication.getApplicationDate());
					int hour = localDateTime.getHour();
					if (hour >= 1 && hour < 9) {
						qualityInspectionApplication.setShift(1);
					}
					if (hour >= 9 && hour <= 17) {
						qualityInspectionApplication.setShift(2);
					}
					if (hour >= 17 || hour < 1) {
						qualityInspectionApplication.setShift(3);
					}
					return qualityInspectionApplication;
				});
		return page;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public MergedUnSubmittedRecord creatRecord(ApplicationDTO applicationDTO) {

		MergedUnSubmittedRecord mergedUnSubmittedRecord = new MergedUnSubmittedRecord();
		mergedUnSubmittedRecord.setLinkId(applicationDTO.getApplicationList().get(0).getLinkId());
		if (applicationDTO.getApplicationList().size() > 1) {
			mergedUnSubmittedRecord.setMerged(true);
		}
		mergedUnSubmittedRecord.setSubmittedStatus(false);
		mergedUnSubmittedRecordMapper.insert(mergedUnSubmittedRecord);

		List<QualityInspectionApplication> applicationList = applicationDTO.getApplicationList();
		applicationList.forEach(
				application -> {
					// 获取申请指标map（通用、集团、内控）
					Map<String, Map<String, String>> generalMap = getRequirementMap(
							application.getGeneralRequirement());
					Map<String, Map<String, String>> groupMap = getRequirementMap(application.getGroupRequirement());
					Map<String, Map<String, String>> internalControlMap = getRequirementMap(
							application.getInternalControlRequirement());

					Map<String, Map<String, String>> samplingGneralMap = ObjectUtil.clone(generalMap);
					Map<String, Map<String, String>> samplingGroupMap = ObjectUtil.clone(groupMap);
					Map<String, Map<String, String>> samplingInternalControlMap = ObjectUtil.clone(internalControlMap);

					log.info("处理基础信息");
					BasicInformationNotSaved basicInformationNotSaved = BeanUtil.copyProperties(application,
							BasicInformationNotSaved.class);
					basicInformationNotSaved.setId(null);
					basicInformationNotSaved.setMergedUnSubmittedRecordId(mergedUnSubmittedRecord.getId());
					basicInformationNotSaved.setApplicationId(application.getId());
					basicInformationNotSaved.setCoding("Q/SSHG（03）8.6-5");
					if (StrUtil.contains(application.getProductType(), "进料加工")) {
						basicInformationNotSaved.setProductType("防老剂6PPD(4020)");
						basicInformationNotSaved.setProductCategoryNumber("010304");
						basicInformationNotSaved.setProductNumber("103040001");
					}

					Map<String, String> molecularFormula = generalMap.get("分子式");
					if (MapUtil.isNotEmpty(molecularFormula)) {
						basicInformationNotSaved.setMolecularFormula(molecularFormula.get("requirement"));
					}
					Map<String, String> molecularWeight = generalMap.get("分子量");
					if (MapUtil.isNotEmpty(molecularWeight)) {
						basicInformationNotSaved.setMolecularWeight(molecularWeight.get("requirement"));
					}
					Map<String, String> specificationBasis = generalMap.get("规格依据");
					if (MapUtil.isNotEmpty(specificationBasis)) {
						basicInformationNotSaved.setSpecificationBasis(specificationBasis.get("requirement"));
					}
					Map<String, String> groupSpecificationBasis = groupMap.get("规格依据");
					if (MapUtil.isNotEmpty(groupSpecificationBasis)) {
						basicInformationNotSaved.setSpecificationBasis(
								groupSpecificationBasis.get("requirement"));
					}

					basicInformationNotSaved.setQualityInspectionBatch(
							applicationDTO.getQualityInspectionBatch());
					basicInformationNotSaved.setNumberOfSamples(applicationDTO.getSampleNumber());
					if (new BigDecimal(applicationDTO.getSampleNumber()).compareTo(application.getQuantity()) > 0) {
						basicInformationNotSaved.setNumberOfSamples(
								application.getQuantity().stripTrailingZeros().toPlainString());
					}
					basicInformationNotSaved.setSamplingDate(applicationDTO.getSamplingDate());
					basicInformationNotSaved.setStaff(applicationDTO.getStaff());
					basicInformationNotSaved.setResult(null);

					if (StrUtil.startWith(basicInformationNotSaved.getProductCategoryNumber(), "0105")) {
						basicInformationNotSaved.setPreDispersedInspection(true);
						basicInformationNotSaved.setAuditor2(
								basicInformationNotSaved.getPreDispersedInspector());
					}

					basicInformationNotSavedMapper.insert(basicInformationNotSaved);

					log.info("处理质检信息");
					// 处理内控指标
					log.info("内控指标");
					for (Map.Entry<String, Map<String, String>> entry : internalControlMap.entrySet()) {
						String itemMatchingName = entry.getKey();
						log.info("itemMatchingName:" + itemMatchingName);
						Map<String, String> requirementInformation = entry.getValue();
						QualityInspectionHandler qualityInspectionHandler = QualityInspectionHandlerFactory
								.getInvokeStrategy(itemMatchingName.split(",")[0]);
						// 填充质量指标
						if (qualityInspectionHandler != null) {
							log.info("项目:[" + itemMatchingName + "],指标信息:" + requirementInformation);
							qualityInspectionHandler.itemRecordCreat(
									false,
									0,
									mergedUnSubmittedRecord,
									basicInformationNotSaved,
									itemMatchingName,
									generalMap,
									groupMap,
									internalControlMap);
							// 集团&通用指标中去除本项目
							generalMap.remove(itemMatchingName);
							groupMap.remove(itemMatchingName);
							log.info("项目:[" + itemMatchingName + "]处理成功");
						}
					}
					log.info("内控指标处理完成");
					// 处理集团指标
					log.info("集团指标");
					for (Map.Entry<String, Map<String, String>> entry : groupMap.entrySet()) {
						String itemMatchingName = entry.getKey();
						log.info("itemMatchingName:" + itemMatchingName);
						Map<String, String> requirementInformation = entry.getValue();
						QualityInspectionHandler qualityInspectionHandler = QualityInspectionHandlerFactory
								.getInvokeStrategy(itemMatchingName.split(",")[0]);
						// 填充质量指标
						if (qualityInspectionHandler != null) {
							log.info("项目:[" + itemMatchingName + "],指标信息:" + requirementInformation);
							qualityInspectionHandler.itemRecordCreat(
									false,
									0,
									mergedUnSubmittedRecord,
									basicInformationNotSaved,
									itemMatchingName,
									generalMap,
									groupMap,
									internalControlMap);
							// 通用指标中去除本项目
							generalMap.remove(itemMatchingName);
							log.info("项目:[" + itemMatchingName + "]处理成功");
						}
					}
					log.info("集团指标处理完成");
					// 处理通用指标
					log.info("通用指标");
					for (Map.Entry<String, Map<String, String>> entry : generalMap.entrySet()) {
						String itemMatchingName = entry.getKey();
						log.info("itemMatchingName:" + itemMatchingName);
						Map<String, String> requirementInformation = entry.getValue();
						QualityInspectionHandler qualityInspectionHandler = QualityInspectionHandlerFactory
								.getInvokeStrategy(itemMatchingName.split(",")[0]);
						// 填充质量指标
						if (qualityInspectionHandler != null) {
							log.info("项目:[" + itemMatchingName + "],指标信息:" + requirementInformation);
							qualityInspectionHandler.itemRecordCreat(
									false,
									0,
									mergedUnSubmittedRecord,
									basicInformationNotSaved,
									itemMatchingName,
									generalMap,
									groupMap,
									internalControlMap);
							log.info("项目:[" + itemMatchingName + "]处理成功");
						}
					}
					log.info("通用指标处理完成");

					// 抽检
					log.info("抽检");
					List<SamplingItemDTO> samplingItems = applicationDTO.getSamplingItems();
					if (samplingItems.get(0).getItem() != null) {
						mergedUnSubmittedRecord.setSamplingInspection(true);
						for (SamplingItemDTO samplingItem : samplingItems) {
							Map<String, String> itemMap = MapStringUtil.stringToMap(samplingItem.getItem());
							String itemMatchingName = itemMap.get("matchingName");
							log.info("itemMatchingName:" + itemMatchingName);
							QualityInspectionHandler qualityInspectionHandler = QualityInspectionHandlerFactory
									.getInvokeStrategy(itemMatchingName.split(",")[0]);
							if (qualityInspectionHandler != null) {
								log.info("项目:[" + itemMatchingName + "],指标信息:" + itemMap);
								for (int i = 1; i <= samplingItem.getNumber(); i++) {
									log.info("当前处理索引：" + i);
									qualityInspectionHandler.itemRecordCreat(
											true,
											i,
											mergedUnSubmittedRecord,
											basicInformationNotSaved,
											itemMatchingName,
											samplingGneralMap,
											samplingGroupMap,
											samplingInternalControlMap);
								}
								log.info("项目:[" + itemMatchingName + "]处理成功");
							}
						}
					}

					// 处理质检申请
					if (basicInformationNotSavedMapper.updateById(basicInformationNotSaved) > 0) {
						QualityInspectionApplication byId = getById(application.getId());
						// 质检批号
						byId.setQualityInspectionBatch(applicationDTO.getQualityInspectionBatch());
						// 数量
						byId.setQuantity(application.getQuantity());
						// 包装规格
						byId.setPackagingSpecification(application.getPackagingSpecification());
						// 包装规格及数量不为0
						if (StrUtil.isNotBlank(application.getPackagingSpecification())
								&& application.getQuantity().compareTo(BigDecimal.ZERO) != 0) {
							// 计算重量
							BigDecimal packagingSpecification = new BigDecimal(application.getPackagingSpecification());
							byId.setWeight(
									packagingSpecification
											.multiply(application.getQuantity())
											.divide(new BigDecimal("1000"), 3, RoundingMode.HALF_UP));
							BigDecimal weight = byId.getWeight()
									.divide(byId.getQuantity(), MathContext.DECIMAL128)
									.multiply(application.getQuantity());
							byId.setWeight(weight);
						}
						// 处理状态
						byId.setIsHandled(true);
						qualityInspectionApplicationMapper.updateById(byId);

						ApplicationOperationLog applicationOperationLog = BeanUtil.copyProperties(application,
								ApplicationOperationLog.class, "id");
						applicationOperationLog.setApplicationId(application.getId());
						applicationOperationLog.setOperationType("处理");
						applicationOperationLog.setOperatorId(applicationDTO.getUserId());
						applicationOperationLogMapper.insert(applicationOperationLog);
					}
				});
		mergedUnSubmittedRecordMapper.updateById(mergedUnSubmittedRecord);
		return mergedUnSubmittedRecord;
	}

	@Override
	public boolean release(ApplicationReleaseDTO applicationReleaseDTO) {
		Boolean productModify = applicationReleaseDTO.getProductModify();
		if (ObjectUtil.isNull(productModify)) {
			productModify = false;
		}

		List<QualityInspectionApplication> multipleSelection = applicationReleaseDTO.getMultipleSelection();
		String inboundCompany = applicationReleaseDTO.getInboundCompany();
		String customerName = " ";
		String customerNumber = " ";
		String groupNumber = null;
		// 单独放行
		if (StrUtil.isNotBlank(inboundCompany) && multipleSelection.size() == 1) {
			LambdaQueryWrapper<Company> companyWrapper = new LambdaQueryWrapper<Company>().eq(Company::getCompanyNumber,
					inboundCompany);
			List<Company> companies = companyMapper.selectList(companyWrapper);
			if (CollUtil.isEmpty(companies)) {
				LambdaQueryWrapper<CommonCompany> commonCompanyWrapper = new LambdaQueryWrapper<CommonCompany>()
						.eq(
								StrUtil.isNotBlank(inboundCompany),
								CommonCompany::getCompanyNumber,
								inboundCompany);
				List<CommonCompany> commonCompanies = commonCompanyMapper.selectList(commonCompanyWrapper);
				if (commonCompanies.size() == 1) {
					CommonCompany commonCompany = commonCompanies.get(0);
					customerNumber = commonCompany.getCompanyNumber();
					customerName = commonCompany.getCompanyName();
				}
			} else if (companies.size() == 1) {
				Company company = companies.get(0);
				groupNumber = company.getGroupNumber();
				customerNumber = company.getCompanyNumber();
				customerName = company.getCompanyName();
			}
			QualityInspectionApplication application = multipleSelection.get(0);
			LambdaQueryWrapper<BasicInformation> wrapper = new LambdaQueryWrapper<BasicInformation>()
					.eq(BasicInformation::getApplicationId, application.getId());
			List<BasicInformation> basicInformationList = basicInformationMapper.selectList(wrapper);
			BasicInformation basicInformation = basicInformationList.get(basicInformationList.size() - 1);
			// 入库客户一致且未修改产品类型，正常放行
			if (StrUtil.equals(inboundCompany, application.getCustomerNumber()) && !productModify) {
				application.setIsReleased(true);
				application.setReleaseOperator(applicationReleaseDTO.getUser().getUsername());

				if (!StrUtil.contains(basicInformation.getQualityInspectionBatch(), "放行")) {
					basicInformation.setQualityInspectionBatch(
							"[放行]" + basicInformation.getQualityInspectionBatch());
				}
			} else { // 入库客户不一致或修改产品类型，调整客户
				// 保存调整前客户
				application.setRawGroupNumber(application.getGroupNumber());
				application.setRawCustomerNumber(application.getCustomerNumber());
				// 保存原始指标
				application.setRawGeneralRequirement(application.getGeneralRequirement());
				application.setRawGroupRequirement(application.getGroupRequirement());
				application.setRawInternalControlRequirement(application.getInternalControlRequirement());
				// 更新调整信息
				application.setGroupNumber(groupNumber);
				application.setCustomerNumber(customerNumber);
				application.setCustomerName(customerName);
				application.setAdjustment(true);
				application.setResult(null);
				// 如调整为预生产，则非预分散体客户
				if (StrUtil.isBlank(customerNumber) || StrUtil.isBlank(customerName)) {
					application.setIsPreDispersedCustomer(false);
				}
				if (productModify) {
					application.setProductCategoryNumber("010402");
					application.setProductNumber("*********");
					application.setProductType("不溶性硫磺IS-7020");
					application.setProductionPackagingSpecification("25KG");
					application.setPackagingSpecification("25");
					application.setQuantity(
							application
									.getWeight()
									.divide(
											new BigDecimal(application.getPackagingSpecification()),
											4,
											RoundingMode.HALF_EVEN)
									.multiply(new BigDecimal("1000")));
				}
				// 删除原质检记录
				basicInformation.setIsDeleted(true);

				recordCustomerAdjustment(application, inboundCompany, productModify);

				/*
				 * if (StrUtil.isBlank(customerName)) {
				 * basicInformation.setCustomerName("预生产");
				 * } else {
				 * basicInformation.setCustomerName(customerName);
				 * }
				 */
			}
			basicInformationMapper.updateById(basicInformation);
			qualityInspectionApplicationMapper.updateById(application);
		} else { // 批量放行
			for (QualityInspectionApplication qualityInspectionApplication : multipleSelection) {
				qualityInspectionApplication.setIsReleased(true);
				qualityInspectionApplication.setReleaseOperator(
						applicationReleaseDTO.getUser().getUsername());
				qualityInspectionApplicationMapper.updateById(qualityInspectionApplication);
				LambdaQueryWrapper<BasicInformation> wrapper = new LambdaQueryWrapper<BasicInformation>()
						.eq(BasicInformation::getApplicationId, qualityInspectionApplication.getId());
				List<BasicInformation> basicInformationList = basicInformationMapper.selectList(wrapper);
				basicInformationList.forEach(
						basicInformation -> {
							if (!StrUtil.contains(basicInformation.getQualityInspectionBatch(), "放行")) {
								basicInformation.setQualityInspectionBatch(
										"[放行]" + basicInformation.getQualityInspectionBatch());
							}
							basicInformationMapper.updateById(basicInformation);
						});
			}
		}
		return true;
	}

	private void recordCustomerAdjustment(
			QualityInspectionApplication sourceApplication,
			String inboundCompanyNumber,
			boolean productModify) {
		// 查询申请对应未提交记录
		LambdaQueryWrapper<BasicInformationNotSaved> unSubmittedRecordWrapper = new LambdaQueryWrapper<BasicInformationNotSaved>()
				.eq(BasicInformationNotSaved::getApplicationId, sourceApplication.getId())
				.orderByAsc(BasicInformationNotSaved::getId);
		List<BasicInformationNotSaved> sourceBasicInformationNotSavedList = basicInformationNotSavedMapper
				.selectList(unSubmittedRecordWrapper);
		BasicInformationNotSaved sourceBasicInformationNotSaved = sourceBasicInformationNotSavedList
				.get(sourceBasicInformationNotSavedList.size() - 1);
		// 查询申请对应处理记录
		LambdaQueryWrapper<MergedUnSubmittedRecord> sourceMergedRecordWrapper = new LambdaQueryWrapper<MergedUnSubmittedRecord>()
				.eq(
						MergedUnSubmittedRecord::getId,
						sourceBasicInformationNotSaved.getMergedUnSubmittedRecordId());
		MergedUnSubmittedRecord sourceMergedRecord = mergedUnSubmittedRecordMapper.selectOne(sourceMergedRecordWrapper);
		// 新增处理记录
		MergedUnSubmittedRecord reInspectionMergedRecord = BeanUtil.copyProperties(
				sourceMergedRecord, MergedUnSubmittedRecord.class, "id", "submittedStatus");
		mergedUnSubmittedRecordMapper.insert(reInspectionMergedRecord);
		// 新增未提交记录
		BasicInformationNotSaved reInspectionRecord = BeanUtil.copyProperties(
				sourceBasicInformationNotSaved, BasicInformationNotSaved.class, "id", "commitStatus");
		reInspectionRecord.setMergedUnSubmittedRecordId(reInspectionMergedRecord.getId());
		if (!StrUtil.contains(sourceBasicInformationNotSaved.getQualityInspectionBatch(), "调整")) {
			reInspectionRecord.setQualityInspectionBatch(
					"[调整]" + sourceBasicInformationNotSaved.getQualityInspectionBatch());
		}
		if (productModify) {
			reInspectionRecord.setProductCategoryNumber("010402");
			reInspectionRecord.setProductNumber("*********");
			reInspectionRecord.setProductType("不溶性硫磺IS-7020");
		}
		// 处理调整客户信息
		String customerName = " ";
		String customerNumber = " ";
		String groupNumber = null;
		LambdaQueryWrapper<Company> companyWrapper = new LambdaQueryWrapper<Company>().eq(Company::getCompanyNumber,
				inboundCompanyNumber);
		List<Company> companies = companyMapper.selectList(companyWrapper);
		if (CollUtil.isEmpty(companies)) {
			LambdaQueryWrapper<CommonCompany> commonCompanyWrapper = new LambdaQueryWrapper<CommonCompany>()
					.eq(
							StrUtil.isNotBlank(inboundCompanyNumber),
							CommonCompany::getCompanyNumber,
							inboundCompanyNumber);
			List<CommonCompany> commonCompanies = commonCompanyMapper.selectList(commonCompanyWrapper);
			if (commonCompanies.size() == 1) {
				CommonCompany commonCompany = commonCompanies.get(0);
				customerNumber = commonCompany.getCompanyNumber();
				customerName = commonCompany.getCompanyName();
			}
		} else if (companies.size() == 1) {
			Company company = companies.get(0);
			groupNumber = company.getGroupNumber();
			customerNumber = company.getCompanyNumber();
			customerName = company.getCompanyName();
		}
		reInspectionRecord.setGroupNumber(groupNumber);
		reInspectionRecord.setResult(null);
		basicInformationNotSavedMapper.insert(reInspectionRecord);
		// 查询调整客户历史质检申请
		LambdaQueryWrapper<QualityInspectionApplication> applicationWrapper = new LambdaQueryWrapper<>();
		applicationWrapper
				.eq(
						ObjectUtil.isNotNull(groupNumber),
						QualityInspectionApplication::getGroupNumber,
						groupNumber)
				.eq(QualityInspectionApplication::getAdjustment, false)
				.isNull(ObjectUtil.isNull(groupNumber), QualityInspectionApplication::getGroupNumber)
				.eq(
						!productModify,
						QualityInspectionApplication::getProductType,
						sourceApplication.getProductType())
				.like(productModify, QualityInspectionApplication::getProductType, "7020");
		List<QualityInspectionApplication> qualityInspectionApplicationList = qualityInspectionApplicationMapper
				.selectList(applicationWrapper);
		QualityInspectionApplication requirementApplication = null;
		if (CollUtil.isNotEmpty(qualityInspectionApplicationList)) {
			requirementApplication = qualityInspectionApplicationList.get(qualityInspectionApplicationList.size() - 1);
		} else {
			LambdaQueryWrapper<QualityInspectionApplication> commonApplicationWrapper = new LambdaQueryWrapper<>();
			commonApplicationWrapper
					.isNull(QualityInspectionApplication::getGroupNumber)
					.eq(QualityInspectionApplication::getAdjustment, false)
					.eq(
							!productModify,
							QualityInspectionApplication::getProductType,
							sourceApplication.getProductType())
					.like(productModify, QualityInspectionApplication::getProductType, "7020");
			List<QualityInspectionApplication> commonQualityInspectionApplicationList = qualityInspectionApplicationMapper
					.selectList(commonApplicationWrapper);
			if (CollUtil.isNotEmpty(commonQualityInspectionApplicationList)) {
				requirementApplication = commonQualityInspectionApplicationList.get(
						commonQualityInspectionApplicationList.size() - 1);
			}
		}
		if (ObjectUtil.isNotNull(requirementApplication)) {
			sourceApplication.setGeneralRequirement(requirementApplication.getGeneralRequirement());
			sourceApplication.setGroupRequirement(requirementApplication.getGroupRequirement());
			sourceApplication.setInternalControlRequirement(
					requirementApplication.getInternalControlRequirement());
		}

		if (ObjectUtil.isNotNull(requirementApplication)) {
			Map<String, Map<String, String>> generalMap = getRequirementMap(
					requirementApplication.getGeneralRequirement());
			Map<String, Map<String, String>> groupMap = getRequirementMap(requirementApplication.getGroupRequirement());
			Map<String, Map<String, String>> internalControlMap = getRequirementMap(
					requirementApplication.getInternalControlRequirement());

			Map<String, Map<String, String>> rawGeneralMap = getRequirementMap(
					sourceApplication.getRawGeneralRequirement());
			Map<String, Map<String, String>> rawGroupMap = getRequirementMap(
					sourceApplication.getRawGroupRequirement());
			Map<String, Map<String, String>> rawInternalControlMap = getRequirementMap(
					sourceApplication.getRawInternalControlRequirement());

			/*
			 * generalMap.putAll(getRequirementMap(sourceApplication.
			 * getRawGeneralRequirement()));
			 * groupMap.putAll(getRequirementMap(sourceApplication.getRawGroupRequirement())
			 * );
			 * internalControlMap.putAll(getRequirementMap(sourceApplication.
			 * getRawInternalControlRequirement()));
			 */

			log.info("处理质检信息");
			// 处理内控指标
			log.info("内控指标");
			for (Map.Entry<String, Map<String, String>> entry : internalControlMap.entrySet()) {
				String itemMatchingName = entry.getKey();
				Map<String, String> requirementInformation = entry.getValue();
				QualityInspectionHandler qualityInspectionHandler = QualityInspectionHandlerFactory
						.getInvokeStrategy(itemMatchingName.split(",")[0]);
				// 填充质量指标
				if (qualityInspectionHandler != null) {
					log.info("项目:[" + itemMatchingName + "],指标信息:" + requirementInformation);
					qualityInspectionHandler.customerAdjustmentItemRecordCopy(
							requirementApplication,
							sourceMergedRecord.getId(),
							sourceBasicInformationNotSaved.getId(),
							reInspectionMergedRecord.getId(),
							reInspectionRecord.getId(),
							itemMatchingName,
							generalMap,
							groupMap,
							internalControlMap);
					// 集团&通用指标中去除本项目
					generalMap.remove(itemMatchingName);
					groupMap.remove(itemMatchingName);
					// 原始指标中去除本项目
					rawGeneralMap.remove(itemMatchingName);
					rawGroupMap.remove(itemMatchingName);
					rawInternalControlMap.remove(itemMatchingName);
					log.info("项目:[" + itemMatchingName + "]处理成功");
				}
			}
			log.info("内控指标处理完成");
			// 处理集团指标
			log.info("集团指标");
			for (Map.Entry<String, Map<String, String>> entry : groupMap.entrySet()) {
				String itemMatchingName = entry.getKey();
				Map<String, String> requirementInformation = entry.getValue();
				QualityInspectionHandler qualityInspectionHandler = QualityInspectionHandlerFactory
						.getInvokeStrategy(itemMatchingName.split(",")[0]);
				// 填充质量指标
				if (qualityInspectionHandler != null) {
					log.info("项目:[" + itemMatchingName + "],指标信息:" + requirementInformation);
					qualityInspectionHandler.customerAdjustmentItemRecordCopy(
							requirementApplication,
							sourceMergedRecord.getId(),
							sourceBasicInformationNotSaved.getId(),
							reInspectionMergedRecord.getId(),
							reInspectionRecord.getId(),
							itemMatchingName,
							generalMap,
							groupMap,
							internalControlMap);
					// 通用指标中去除本项目
					generalMap.remove(itemMatchingName);
					// 原始指标中去除本项目
					rawGeneralMap.remove(itemMatchingName);
					rawGroupMap.remove(itemMatchingName);
					rawInternalControlMap.remove(itemMatchingName);
					log.info("项目:[" + itemMatchingName + "]处理成功");
				}
			}
			log.info("集团指标处理完成");
			// 处理通用指标
			log.info("通用指标");
			for (Map.Entry<String, Map<String, String>> entry : generalMap.entrySet()) {
				String itemMatchingName = entry.getKey();
				Map<String, String> requirementInformation = entry.getValue();
				QualityInspectionHandler qualityInspectionHandler = QualityInspectionHandlerFactory
						.getInvokeStrategy(itemMatchingName.split(",")[0]);
				// 填充质量指标
				if (qualityInspectionHandler != null) {
					log.info("项目:[" + itemMatchingName + "],指标信息:" + requirementInformation);
					qualityInspectionHandler.customerAdjustmentItemRecordCopy(
							requirementApplication,
							sourceMergedRecord.getId(),
							sourceBasicInformationNotSaved.getId(),
							reInspectionMergedRecord.getId(),
							reInspectionRecord.getId(),
							itemMatchingName,
							generalMap,
							groupMap,
							internalControlMap);
					// 原始指标中去除本项目
					rawGeneralMap.remove(itemMatchingName);
					rawGroupMap.remove(itemMatchingName);
					rawInternalControlMap.remove(itemMatchingName);
					log.info("项目:[" + itemMatchingName + "]处理成功");
				}
			}
			log.info("通用指标处理完成");
			// 处理原始质检信息
			log.info("处理原始质检信息");
			// 处理内控指标
			log.info("内控指标");
			for (Map.Entry<String, Map<String, String>> entry : rawInternalControlMap.entrySet()) {
				String itemMatchingName = entry.getKey();
				Map<String, String> requirementInformation = entry.getValue();
				QualityInspectionHandler qualityInspectionHandler = QualityInspectionHandlerFactory
						.getInvokeStrategy(itemMatchingName.split(",")[0]);
				// 填充质量指标
				if (qualityInspectionHandler != null) {
					log.info("项目:[" + itemMatchingName + "],指标信息:" + requirementInformation);
					qualityInspectionHandler.customerAdjustmentItemRecordCopy(
							requirementApplication,
							sourceMergedRecord.getId(),
							sourceBasicInformationNotSaved.getId(),
							reInspectionMergedRecord.getId(),
							reInspectionRecord.getId(),
							itemMatchingName,
							rawGeneralMap,
							rawGroupMap,
							rawInternalControlMap);
					// 集团&通用指标中去除本项目
					rawGeneralMap.remove(itemMatchingName);
					rawGroupMap.remove(itemMatchingName);
					log.info("项目:[" + itemMatchingName + "]处理成功");
				}
			}
			log.info("内控指标处理完成");
			// 处理集团指标
			log.info("集团指标");
			for (Map.Entry<String, Map<String, String>> entry : rawGroupMap.entrySet()) {
				String itemMatchingName = entry.getKey();
				Map<String, String> requirementInformation = entry.getValue();
				QualityInspectionHandler qualityInspectionHandler = QualityInspectionHandlerFactory
						.getInvokeStrategy(itemMatchingName.split(",")[0]);
				// 填充质量指标
				if (qualityInspectionHandler != null) {
					log.info("项目:[" + itemMatchingName + "],指标信息:" + requirementInformation);
					qualityInspectionHandler.customerAdjustmentItemRecordCopy(
							requirementApplication,
							sourceMergedRecord.getId(),
							sourceBasicInformationNotSaved.getId(),
							reInspectionMergedRecord.getId(),
							reInspectionRecord.getId(),
							itemMatchingName,
							rawGeneralMap,
							rawGroupMap,
							rawInternalControlMap);
					// 通用指标中去除本项目
					rawGeneralMap.remove(itemMatchingName);
					log.info("项目:[" + itemMatchingName + "]处理成功");
				}
			}
			log.info("集团指标处理完成");
			// 处理通用指标
			log.info("通用指标");
			for (Map.Entry<String, Map<String, String>> entry : rawGeneralMap.entrySet()) {
				String itemMatchingName = entry.getKey();
				Map<String, String> requirementInformation = entry.getValue();
				QualityInspectionHandler qualityInspectionHandler = QualityInspectionHandlerFactory
						.getInvokeStrategy(itemMatchingName.split(",")[0]);
				// 填充质量指标
				if (qualityInspectionHandler != null) {
					log.info("项目:[" + itemMatchingName + "],指标信息:" + requirementInformation);
					qualityInspectionHandler.customerAdjustmentItemRecordCopy(
							requirementApplication,
							sourceMergedRecord.getId(),
							sourceBasicInformationNotSaved.getId(),
							reInspectionMergedRecord.getId(),
							reInspectionRecord.getId(),
							itemMatchingName,
							rawGeneralMap,
							rawGroupMap,
							rawInternalControlMap);
					log.info("项目:[" + itemMatchingName + "]处理成功");
				}
			}
			log.info("通用指标处理完成");
		}
	}

	@Override
	public boolean notRelease(CreatAnalysisRecordFormData creatAnalysisRecordFormData) {
		ProductionInformation[] multipleSelection = creatAnalysisRecordFormData.getMultipleSelection();
		final boolean[] flag = { true };
		Arrays.asList(multipleSelection)
				.forEach(
						productionInformation -> {
							Integer id = productionInformation.getId();
							QualityInspectionApplication qualityInspectionApplication = getById(id);
							qualityInspectionApplication.setIsReleased(false);
							qualityInspectionApplication.setReleaseOperator(
									creatAnalysisRecordFormData.getUser().getUsername());
							LambdaQueryWrapper<BasicInformation> basicInformationWrapper = new LambdaQueryWrapper<>();
							basicInformationWrapper.eq(BasicInformation::getApplicationId, id);
							List<BasicInformation> basicInformationList = basicInformationMapper
									.selectList(basicInformationWrapper);
							BasicInformation basicInformation = basicInformationList
									.get(basicInformationList.size() - 1);
							basicInformation.setQualityInspectionBatch(
									"[否决]" + basicInformation.getQualityInspectionBatch());
							basicInformationMapper.updateById(basicInformation);
							if (!updateById(qualityInspectionApplication)) {
								flag[0] = false;
							}
						});
		return flag[0];
	}

	@Override
	public List<SamplingItemVO> getSamplingItems(
			List<QualityInspectionApplication> qualityInspectionApplicationList) {

		// 获取全部申请的指标汇总map（通用、集团、内控）
		Map<String, Map<String, String>> generalMap = new HashMap<>();
		Map<String, Map<String, String>> groupMap = new HashMap<>();
		Map<String, Map<String, String>> internalControlMap = new HashMap<>();

		qualityInspectionApplicationList.forEach(
				qualityInspectionApplication -> {
					String generalRequirement = qualityInspectionApplication.getGeneralRequirement();
					String groupRequirement = qualityInspectionApplication.getGroupRequirement();
					String internalControlRequirement = qualityInspectionApplication.getInternalControlRequirement();

					generalMap.putAll(getRequirementMap(generalRequirement));
					groupMap.putAll(getRequirementMap(groupRequirement));
					internalControlMap.putAll(getRequirementMap(internalControlRequirement));
				});

		Map<String, Map<String, String>> requirementMap = new HashMap<>();
		requirementMap.putAll(internalControlMap);
		requirementMap.putAll(groupMap);
		requirementMap.putAll(generalMap);

		requirementMap.remove("备注");
		requirementMap.remove("客户产品代码");
		requirementMap.remove("分子式");
		requirementMap.remove("分子量");
		requirementMap.remove("规格依据");
		requirementMap.remove("外观");
		requirementMap.remove("保质期");
		requirementMap.remove("有效期");

		// 获取质检项目VO集合
		List<String> itemList = new ArrayList<>(requirementMap.keySet());
		List<SamplingItemVO> samplingItemVOList = new ArrayList<>();
		itemList.forEach(
				item -> {
					if (StrUtil.contains(requirementMap.get(item).get("matchingName"), "颗粒强度")) {
						if (StrUtil.contains(requirementMap.get(item).get("matchingName"), "平均")) {
							requirementMap.get(item).remove("itemName");
							SamplingItemVO samplingItemVO = new SamplingItemVO();
							samplingItemVO.setLabel("颗粒强度");
							samplingItemVO.setValue(requirementMap.get(item).toString());
							samplingItemVOList.add(samplingItemVO);
						}
					} else if (StrUtil.contains(requirementMap.get(item).get("matchingName"), "最大平均")) {

					} else {
						requirementMap.get(item).remove("itemName");
						SamplingItemVO samplingItemVO = new SamplingItemVO();
						samplingItemVO.setLabel(requirementMap.get(item).get("matchingName"));
						samplingItemVO.setValue(requirementMap.get(item).toString());
						samplingItemVOList.add(samplingItemVO);
					}
				});

		// 质检项目VO集合排序
		List<String> orderList = new ArrayList<>();
		LambdaQueryWrapper<ProductTypeForSort> productTypeForSortLambdaQueryWrapper = new LambdaQueryWrapper<>();
		productTypeForSortLambdaQueryWrapper.eq(
				ProductTypeForSort::getProductCategoryNumber,
				qualityInspectionApplicationList.get(0).getProductCategoryNumber());
		List<ProductTypeForSort> productTypeForSorts = productTypeForSortMapper
				.selectList(productTypeForSortLambdaQueryWrapper);
		if (!productTypeForSorts.isEmpty()) {
			LambdaQueryWrapper<ItemPriorityForSort> itemPriorityForSortLambdaQueryWrapper = new LambdaQueryWrapper<>();
			itemPriorityForSortLambdaQueryWrapper.eq(
					ItemPriorityForSort::getProductTypeId, productTypeForSorts.get(0).getId());
			List<ItemPriorityForSort> itemPriorityForSorts = itemPriorityForSortMapper
					.selectList(itemPriorityForSortLambdaQueryWrapper);
			List<ItemPriorityForSort> sortedList = itemPriorityForSorts.stream()
					.sorted(
							Comparator.comparingInt(ItemPriorityForSort::getPriority)
									.reversed()
									.thenComparing(ItemPriorityForSort::getItem))
					.collect(Collectors.toList());
			sortedList.forEach(itemPriorityForSort -> orderList.add(itemPriorityForSort.getItem()));
		}
		log.info(orderList.toString());
		// 自定义比较器，优先按照orderList排序，orderList中不包括的项目置底
		Comparator<SamplingItemVO> itemComparator = (o1, o2) -> {
			String item1 = o1.getLabel();
			String item2 = o2.getLabel();
			// 通过正则表达式提取括号前的内容
			String regex = "^(.*?)\\(";
			Pattern pattern = Pattern.compile(regex);
			Matcher matcher1 = pattern.matcher(item1);
			Matcher matcher2 = pattern.matcher(item2);
			String itemCategory1 = matcher1.find() ? matcher1.group(1) : item1;
			String itemCategory2 = matcher2.find() ? matcher2.group(1) : item2;
			// 如果都在 orderList 中，按照 orderList 排序；否则，未在 orderList 中的排在后面
			if (orderList.contains(itemCategory1) && orderList.contains(itemCategory2)) {
				return Integer.compare(
						orderList.indexOf(itemCategory1), orderList.indexOf(itemCategory2));
			} else if (orderList.contains(itemCategory1)) {
				return -1;
			} else if (orderList.contains(itemCategory2)) {
				return 1;
			} else {
				return item1.compareTo(item2);
			}
		};
		samplingItemVOList.sort(itemComparator);

		// 返回排序后的质检项目VO集合
		return samplingItemVOList;
	}

	@Override
	public List<ProductTypeLabelVO> getProductTypeLabelList(ProductTypeQuery productTypeQuery) {
		// 条件查询
		LambdaQueryWrapper<QualityInspectionApplication> queryWrapper = new LambdaQueryWrapper<>();
		// 所属厂区
		queryWrapper.eq(
				!productTypeQuery.getHandleStatus(),
				QualityInspectionApplication::getLinkId,
				productTypeQuery.getLinkId());
		if (productTypeQuery.getHandleStatus()) {
			// 已处理
			queryWrapper.eq(QualityInspectionApplication::getIsHandled, true);
			// 未退回
			queryWrapper.eq(QualityInspectionApplication::getIsReturned, false);
			// 结果不符合或不合格
			queryWrapper.ne(QualityInspectionApplication::getResult, 1);
			queryWrapper.isNotNull(QualityInspectionApplication::getResult);
			// 未放行
			queryWrapper.isNull(QualityInspectionApplication::getIsReleased);
		} else {
			// 未处理
			queryWrapper.eq(QualityInspectionApplication::getIsHandled, false);
			// 未退回
			queryWrapper.eq(QualityInspectionApplication::getIsReturned, false);
		}
		// 是否为半成品
		queryWrapper
				.eq(
						ObjectUtil.isNotNull(productTypeQuery.getSemiFinished()),
						QualityInspectionApplication::getSemiFinished,
						productTypeQuery.getSemiFinished())
				.eq(
						ObjectUtil.isNull(productTypeQuery.getSemiFinished()),
						QualityInspectionApplication::getSemiFinished,
						false);
		List<QualityInspectionApplication> qualityInspectionApplications = qualityInspectionApplicationMapper
				.selectList(queryWrapper);
		// 根据产品类型分类去重
		Set<String> productTypesSet = new HashSet<>();
		for (QualityInspectionApplication qualityInspectionApplication : qualityInspectionApplications) {
			String productType = qualityInspectionApplication.getProductType();
			// 替换中文括号为英文括号
			String processedProductType = productType.replace("（", "(").replace("）", ")");
			productTypesSet.add(processedProductType);
		}
		List<String> productTypesList = new ArrayList<>(productTypesSet);
		// 产品类型排序
		Collections.sort(productTypesList);
		// 产品类型选项VO集合
		List<ProductTypeLabelVO> productTypes = new ArrayList<>();
		productTypesList.forEach(
				productType -> {
					ProductTypeLabelVO typeLabelVO = new ProductTypeLabelVO();
					typeLabelVO.setLabel(productType);
					typeLabelVO.setValue(productType);
					productTypes.add(typeLabelVO);
				});
		return productTypes;
	}

	@Override
	public boolean applicationReturn(ApplicationReturnDTO applicationReturnDTO) {
		QualityInspectionApplication qualityInspectionApplication = qualityInspectionApplicationMapper
				.selectById(applicationReturnDTO.getId());
		User user = userMapper.selectById(applicationReturnDTO.getUserId());
		qualityInspectionApplication.setIsReturned(true);
		qualityInspectionApplication.setReturnOperator(user.getUsername());
		qualityInspectionApplication.setReturnReason(applicationReturnDTO.getReturnReason());

		ApplicationOperationLog applicationOperationLog = BeanUtil.copyProperties(qualityInspectionApplication,
				ApplicationOperationLog.class, "id");
		applicationOperationLog.setApplicationId(applicationReturnDTO.getId());
		applicationOperationLog.setOperationType("退回");
		applicationOperationLog.setOperatorId(applicationReturnDTO.getUserId());
		applicationOperationLogMapper.insert(applicationOperationLog);
		return qualityInspectionApplicationMapper.updateById(qualityInspectionApplication) > 0;
	}

	@Override
	public IPage<QualityInspectionApplication> getPendingApprovalApplications(
			int current, int size, ApplicationQuery applicationQuery) {
		IPage<QualityInspectionApplication> applicationPage = qualityInspectionApplicationMapper.selectPage(
				new Page<>(current, size),
				new LambdaQueryWrapper<QualityInspectionApplication>()
						.eq(
								ObjUtil.isNotNull(applicationQuery.getLinkId()),
								QualityInspectionApplication::getLinkId,
								applicationQuery.getLinkId())
						.like(
								StrUtil.isNotBlank(applicationQuery.getCustomerName()),
								QualityInspectionApplication::getCustomerName,
								applicationQuery.getCustomerName())
						.like(
								StrUtil.isNotBlank(applicationQuery.getProductType()),
								QualityInspectionApplication::getProductType,
								applicationQuery.getProductType())
						.like(
								StrUtil.isNotBlank(applicationQuery.getProductionBatch()),
								QualityInspectionApplication::getProductionBatch,
								applicationQuery.getProductionBatch())
						.like(
								StrUtil.isNotBlank(applicationQuery.getQualityInspectionBatch()),
								QualityInspectionApplication::getQualityInspectionBatch,
								applicationQuery.getQualityInspectionBatch())
						.eq(QualityInspectionApplication::getIsHandled, true)
						.isNotNull(QualityInspectionApplication::getResult)
						.isNull(QualityInspectionApplication::getIsReleased)
						.eq(QualityInspectionApplication::getIsReturned, false)
						.orderByDesc(QualityInspectionApplication::getId));
		applicationPage
				.getRecords()
				.forEach(
						application -> {
							// 客户名称为空
							if (StrUtil.isBlank(application.getCustomerName())) {
								// 客户名称修改为预生产
								application.setCustomerName("预生产");
							}
						});
		return applicationPage;
		/*
		 * // 用户
		 * User user = userMapper.selectById(applicationQuery.getUserId());
		 * // 条件查询
		 * MPJLambdaWrapper<QualityInspectionApplication> lambdaQueryWrapper =
		 * new MPJLambdaWrapper<QualityInspectionApplication>()
		 * // 所属厂区
		 * .eq(
		 * !(user.getId() == 79 || user.getId() == 610 || user.getId() == 265),
		 * QualityInspectionApplication::getLinkId,
		 * user.getLinkId())
		 * // 产品类型
		 * .eq(
		 * StrUtil.isNotBlank(applicationQuery.getProductType()),
		 * QualityInspectionApplication::getProductType,
		 * applicationQuery.getProductType())
		 * // 已处理
		 * .eq(QualityInspectionApplication::getIsHandled, true)
		 * // 未退回
		 * .eq(QualityInspectionApplication::getIsReturned, false)
		 * // 结果不符合或不合格
		 * // .ne(QualityInspectionApplication::getResult, 1)
		 * // 结果不为空
		 * .isNotNull(QualityInspectionApplication::getResult)
		 * // 未放行
		 * .isNull(QualityInspectionApplication::getIsReleased);
		 * // 质检申请集合
		 * List<QualityInspectionApplication> qualityInspectionApplicationList =
		 * qualityInspectionApplicationMapper.selectJoinList(
		 * QualityInspectionApplication.class, lambdaQueryWrapper);
		 * // 遍历集合
		 * for (QualityInspectionApplication qualityInspectionApplication :
		 * qualityInspectionApplicationList) {
		 * // 客户名称为空
		 * if (StrUtil.isBlank(qualityInspectionApplication.getCustomerName())) {
		 * // 客户名称修改为预生产
		 * qualityInspectionApplication.setCustomerName("预生产");
		 * }
		 * }
		 * return qualityInspectionApplicationList;
		 */
	}

	@Override
	public Map<String, Object> getApplicationRequirement(int mergedUnSubmittedRecordId) {
		Map<String, Object> applicationRequirement = new HashMap<>();

		MPJLambdaWrapper<QualityInspectionApplication> lambdaWrapper = new MPJLambdaWrapper<QualityInspectionApplication>()
				.selectAll(QualityInspectionApplication.class)
				.leftJoin(
						BasicInformationNotSaved.class,
						BasicInformationNotSaved::getApplicationId,
						QualityInspectionApplication::getId)
				.leftJoin(
						MergedUnSubmittedRecord.class,
						MergedUnSubmittedRecord::getId,
						BasicInformationNotSaved::getMergedUnSubmittedRecordId)
				.eq(BasicInformationNotSaved::getMergedUnSubmittedRecordId, mergedUnSubmittedRecordId);
		List<QualityInspectionApplication> qualityInspectionApplicationList = qualityInspectionApplicationMapper
				.selectJoinList(
						QualityInspectionApplication.class, lambdaWrapper);

		qualityInspectionApplicationList.forEach(
				application -> {
					if (StrUtil.isBlank(application.getCustomerName())) {
						application.setCustomerName("预生产");
					}
				});

		Set<String> uniqueCustomerNames = new HashSet<>();
		List<QualityInspectionApplication> deduplicatedList = new ArrayList<>();

		for (QualityInspectionApplication app : qualityInspectionApplicationList) {
			String name = app.getCustomerName();
			if (!uniqueCustomerNames.contains(name)) {
				uniqueCustomerNames.add(name);
				deduplicatedList.add(app);
			}
		}

		List<Map<String, Object>> columnMapList = new ArrayList<>();
		List<Map<String, Object>> dataMapList = new ArrayList<>();

		Map<String, Object> column = new HashMap<>();
		for (QualityInspectionApplication application : deduplicatedList) {

			Map<String, Object> data = new HashMap<>();
			data.put("客户名称", application.getCustomerName());

			Map<String, Map<String, String>> generalMap = getRequirementMap(application.getGeneralRequirement());
			Map<String, Map<String, String>> groupMap = getRequirementMap(application.getGroupRequirement());
			Map<String, Map<String, String>> internalControlMap = getRequirementMap(
					application.getInternalControlRequirement());

			generalMap.remove("分子式");
			generalMap.remove("分子量");
			generalMap.remove("规格依据");

			groupMap.remove("规格依据");
			groupMap.remove("客户产品代码");
			groupMap.remove("备注");

			internalControlMap.remove("规格依据");
			internalControlMap.remove("客户产品代码");
			internalControlMap.remove("备注");

			generalMap.forEach(
					(key, value) -> {
						String itemName = value.get("matchingName");
						String matchingName;
						String[] split = itemName.split(",");
						if (split.length > 1) {
							matchingName = split[0] + "(" + split[1] + ")";
						} else {
							matchingName = itemName;
						}
						column.put(matchingName, matchingName);
						data.put(matchingName, value.get("requirement"));
					});

			groupMap.forEach(
					(key, value) -> {
						String itemName = value.get("matchingName");
						String matchingName;
						String[] split = itemName.split(",");
						if (split.length > 1) {
							matchingName = split[0] + "(" + split[1] + ")";
						} else {
							matchingName = itemName;
						}
						column.put(matchingName, matchingName);
						data.put(matchingName, value.get("requirement"));
					});

			internalControlMap.forEach(
					(key, value) -> {
						String itemName = value.get("matchingName");
						String matchingName;
						String[] split = itemName.split(",");
						if (split.length > 1) {
							matchingName = split[0] + "(" + split[1] + ")";
						} else {
							matchingName = itemName;
						}
						column.put(matchingName, matchingName);
						data.put(matchingName, value.get("requirement"));
					});
			dataMapList.add(data);
		}

		column.forEach(
				(key, value) -> {
					Map<String, Object> renameColumnMap = new HashMap<>();
					renameColumnMap.put("label", value);
					columnMapList.add(renameColumnMap);
				});

		// 排序
		List<String> orderList = new ArrayList<>();
		LambdaQueryWrapper<ProductTypeForSort> productTypeForSortLambdaQueryWrapper = new LambdaQueryWrapper<>();
		// productTypeForSortLambdaQueryWrapper.eq(ProductTypeForSort::getProductCategoryNumber,
		// application.getProductCategoryNumber());
		List<ProductTypeForSort> productTypeForSorts = productTypeForSortMapper
				.selectList(productTypeForSortLambdaQueryWrapper);
		if (!productTypeForSorts.isEmpty()) {
			LambdaQueryWrapper<ItemPriorityForSort> itemPriorityForSortLambdaQueryWrapper = new LambdaQueryWrapper<>();
			itemPriorityForSortLambdaQueryWrapper.eq(
					ItemPriorityForSort::getProductTypeId, productTypeForSorts.get(0).getId());
			List<ItemPriorityForSort> itemPriorityForSorts = itemPriorityForSortMapper
					.selectList(itemPriorityForSortLambdaQueryWrapper);
			List<ItemPriorityForSort> sortedList = itemPriorityForSorts.stream()
					.sorted(
							Comparator.comparingInt(ItemPriorityForSort::getPriority)
									.reversed()
									.thenComparing(ItemPriorityForSort::getItem))
					.collect(Collectors.toList());
			sortedList.forEach(itemPriorityForSort -> orderList.add(itemPriorityForSort.getItem()));
		}
		// 自定义比较器，优先按照orderList排序，orderList中不包括的项目置底
		Comparator<Map<String, Object>> itemComparator = (o1, o2) -> {
			String item1 = o1.get("label").toString();
			String item2 = o2.get("label").toString();
			// 通过正则表达式提取括号前的内容
			String regex = "^(.*?)\\(";
			Pattern pattern = Pattern.compile(regex);
			Matcher matcher1 = pattern.matcher(item1);
			Matcher matcher2 = pattern.matcher(item2);
			String itemCategory1 = matcher1.find() ? matcher1.group(1) : item1;
			String itemCategory2 = matcher2.find() ? matcher2.group(1) : item2;
			// 如果都在 orderList 中，按照 orderList 排序；否则，未在 orderList 中的排在后面
			if (orderList.contains(itemCategory1) && orderList.contains(itemCategory2)) {
				return Integer.compare(
						orderList.indexOf(itemCategory1), orderList.indexOf(itemCategory2));
			} else if (orderList.contains(itemCategory1)) {
				return -1;
			} else if (orderList.contains(itemCategory2)) {
				return 1;
			} else {
				return item1.compareTo(item2);
			}
		};
		columnMapList.sort(itemComparator);

		applicationRequirement.put("column", columnMapList);
		applicationRequirement.put("data", dataMapList);

		return applicationRequirement;
	}

	@Override
	public List<CascaderLabelVO> getCompanyLabel(String companyQuery) {
		List<?> queryList = new ArrayList<>();
		if (StrUtil.isNotBlank(companyQuery) && StrUtil.contains(companyQuery, " ")) {
			queryList = Convert.toList(companyQuery.split(" "));
		}
		LambdaQueryWrapper<CommonCompany> companyWrapper = new LambdaQueryWrapper<>();
		if (CollUtil.isNotEmpty(queryList)) {
			queryList.forEach(query -> companyWrapper.like(CommonCompany::getCompanyName, query));
		} else {
			companyWrapper.like(
					StrUtil.isNotBlank(companyQuery), CommonCompany::getCompanyName, companyQuery);
		}
		List<CommonCompany> commonCompanyList = commonCompanyMapper.selectList(companyWrapper);

		List<CascaderLabelVO> cascaderLabelVOList = new ArrayList<>();
		commonCompanyList.forEach(
				commonCompany -> {
					CascaderLabelVO cascaderLabelVO = new CascaderLabelVO();
					cascaderLabelVO.setLabel(commonCompany.getCompanyName());
					cascaderLabelVO.setValue(commonCompany.getCompanyNumber());
					cascaderLabelVOList.add(cascaderLabelVO);
				});
		return cascaderLabelVOList;
	}

	@Override
	public ReportVO getCustomerReport(ApplicationReleaseDTO applicationReleaseDTO) {
		Boolean productModify = applicationReleaseDTO.getProductModify();
		if (ObjectUtil.isNull(productModify)) {
			productModify = false;
		}
		QualityInspectionApplication inboundApplication = applicationReleaseDTO.getMultipleSelection().get(0);
		if (StrUtil.equals(applicationReleaseDTO.getInboundCompany(), "0")) {
			inboundApplication.setIsPreDispersedCustomer(false);
		}
		// 查询入库客户指标
		String inboundCompanyNumber = applicationReleaseDTO.getInboundCompany();
		String groupNumber = null;
		LambdaQueryWrapper<Company> companyWrapper = new LambdaQueryWrapper<Company>().eq(Company::getCompanyNumber,
				inboundCompanyNumber);
		List<Company> companies = companyMapper.selectList(companyWrapper);
		if (CollUtil.isNotEmpty(companies)) {
			groupNumber = companies.get(0).getGroupNumber();
		}
		// 查询集团质检申请
		LambdaQueryWrapper<QualityInspectionApplication> applicationWrapper = new LambdaQueryWrapper<>();
		// 构建查询条件
		// 集团编号（集团编号非空）
		applicationWrapper
				.eq(
						ObjectUtil.isNotNull(groupNumber),
						QualityInspectionApplication::getGroupNumber,
						groupNumber)
				// 当前客户为预分散体
				.eq(
						inboundApplication.getIsPreDispersedCustomer(),
						QualityInspectionApplication::getIsPreDispersedCustomer,
						true)
				// 当前客户为预生产
				.eq(
						StrUtil.equals(applicationReleaseDTO.getInboundCompany(), "0"),
						QualityInspectionApplication::getIsPreDispersedCustomer,
						false)
				// 调整状态（未调整）
				.eq(QualityInspectionApplication::getAdjustment, false)
				// 集团编号为空
				.isNull(ObjectUtil.isNull(groupNumber), QualityInspectionApplication::getGroupNumber)
				// 产品类型（未调整产品）
				.eq(
						!productModify,
						QualityInspectionApplication::getProductType,
						inboundApplication.getProductType())
				// 产品类型（调整产品为硫磺7020）
				.like(productModify, QualityInspectionApplication::getProductType, "7020");
		List<QualityInspectionApplication> qualityInspectionApplicationList = qualityInspectionApplicationMapper
				.selectList(applicationWrapper);
		QualityInspectionApplication requirementApplication;
		// 查询到质检申请
		if (CollUtil.isNotEmpty(qualityInspectionApplicationList)) {
			// 最近一次质检申请作为指标依据
			requirementApplication = qualityInspectionApplicationList.get(qualityInspectionApplicationList.size() - 1);
			// 未查询到质检申请
		} else {
			// 查询产品质检申请
			LambdaQueryWrapper<QualityInspectionApplication> commonApplicationWrapper = new LambdaQueryWrapper<>();
			// 调整状态（未调整）
			commonApplicationWrapper
					.eq(QualityInspectionApplication::getAdjustment, false)
					// 产品类型（未调整产品）
					.eq(
							!productModify,
							QualityInspectionApplication::getProductType,
							inboundApplication.getProductType())
					// 产品类型（调整产品为7020）
					.like(productModify, QualityInspectionApplication::getProductType, "7020");
			List<QualityInspectionApplication> commonQualityInspectionApplicationList = qualityInspectionApplicationMapper
					.selectList(commonApplicationWrapper);
			if (CollUtil.isNotEmpty(commonQualityInspectionApplicationList)) {
				// 最近一次质检申请作为指标依据
				requirementApplication = commonQualityInspectionApplicationList.get(
						commonQualityInspectionApplicationList.size() - 1);
				requirementApplication.setGroupNumber(null);
				requirementApplication.setGroupRequirement(null);
				requirementApplication.setInternalControlRequirement(null);
			} else {
				return null;
			}
		}
		Map<String, Map<String, String>> generalMap = getRequirementMap(requirementApplication.getGeneralRequirement());
		Map<String, Map<String, String>> groupMap = getRequirementMap(requirementApplication.getGroupRequirement());
		Map<String, Map<String, String>> internalControlMap = getRequirementMap(
				requirementApplication.getInternalControlRequirement());

		Map<String, RecordResultVO> recordResultMap = new HashMap<>();

		generalMap.remove("分子式");
		generalMap.remove("分子量");
		generalMap.remove("备注");
		generalMap.remove("有效期");
		generalMap.remove("客户产品代码");
		generalMap.remove("规格依据");

		groupMap.remove("分子式");
		groupMap.remove("分子量");
		groupMap.remove("备注");
		groupMap.remove("有效期");
		groupMap.remove("客户产品代码");
		groupMap.remove("规格依据");

		internalControlMap.remove("分子式");
		internalControlMap.remove("分子量");
		internalControlMap.remove("备注");
		internalControlMap.remove("有效期");
		internalControlMap.remove("客户产品代码");
		internalControlMap.remove("规格依据");

		generalMap.forEach(
				(key, value) -> {
					RecordResultVO recordResultVO = new RecordResultVO();
					recordResultVO.setItem(value.get("itemName"));
					recordResultVO.setMatchingName(value.get("matchingName"));
					recordResultVO.setGeneralRequirement(value.get("requirement"));
					recordResultVO.setGroupRequirement("暂无客户指标");
					recordResultMap.put(value.get("matchingName"), recordResultVO);
				});
		groupMap.forEach(
				(key, value) -> {
					RecordResultVO recordResultVO = recordResultMap.get(value.get("matchingName"));
					if (ObjectUtil.isNotNull(recordResultVO)) {
						recordResultVO.setGroupRequirement(value.get("requirement"));
					} else {
						recordResultVO = new RecordResultVO();
						recordResultVO.setItem(value.get("itemName"));
						recordResultVO.setMatchingName(value.get("matchingName"));
						recordResultVO.setGeneralRequirement("暂无通用指标");
						recordResultVO.setGroupRequirement(value.get("requirement"));
						recordResultMap.put(value.get("matchingName"), recordResultVO);
					}
				});
		internalControlMap.forEach(
				(key, value) -> {
					RecordResultVO recordResultVO = recordResultMap.get(value.get("matchingName"));
					if (ObjectUtil.isNotNull(recordResultVO)) {
						recordResultVO.setGroupRequirement(value.get("requirement"));
					} else {
						recordResultVO = new RecordResultVO();
						recordResultVO.setItem(value.get("itemName"));
						recordResultVO.setMatchingName(value.get("matchingName"));
						recordResultVO.setGeneralRequirement("暂无通用指标");
						recordResultVO.setGroupRequirement(value.get("requirement"));
						recordResultMap.put(value.get("matchingName"), recordResultVO);
					}
				});
		ReportVO report = applicationReleaseDTO.getReport();
		if (productModify) {
			report.setProductType("不溶性硫磺IS-7020");
		}
		report.setGroupNumber(groupNumber);
		List<RecordResultVO> sourceRecordResultList = report.getRecordResultList();
		List<RecordResultVO> sourceSamplingResultList = report.getSamplingResultList();

		List<RecordResultVO> targetRecordResultList = new ArrayList<>();
		List<RecordResultVO> targetSamplingResultList = new ArrayList<>();

		Map<String, RecordResultVO> itemResultMap = MapUtil.newHashMap();
		itemResultMap.putAll(recordResultMap);

		recordResultMap.forEach(
				(key, value) -> sourceRecordResultList.forEach(
						recordResultVO -> {
							if (StrUtil.equals(key, recordResultVO.getMatchingName())) {
								RecordResultVO recordResult = BeanUtil.copyProperties(value, RecordResultVO.class);
								recordResult.setData(recordResultVO.getData());
								// 计算系数
								recordResult.setCalculateCoefficient(recordResultVO.getCalculateCoefficient());
								if (StrUtil.contains(recordResultVO.getData(), "暂无")) {
									recordResult.setResult(null);
									recordResult.setGroupResult(null);
									recordResult.setFinalResult(null);
								} else {
									if (ObjectUtil.isNotNull(Convert.toFloat(recordResultVO.getData()))) {
										// 处理颗粒强度
										if (StrUtil.contains(recordResult.getItem(), "颗粒强度")) {
											float data = NumberUtil.parseFloat(recordResult.getData());
											if (StrUtil.contains(recordResult.getGroupRequirement(), "CN")) {
												if (data < 10) {
													recordResult.setData(NumberUtil.roundStr(data * 98, 0));
												}
											} else if (StrUtil.contains(recordResult.getGroupRequirement(), "N")
													|| StrUtil.contains(recordResult.getGroupRequirement(), "暂无")) {
												if (data > 10) {
													if (StrUtil.contains(recordResultVO.getGroupRequirement(), "CN")) {
														recordResult.setData(NumberUtil.roundStr(data / 98, 2));
													}
													if (StrUtil.contains(recordResultVO.getGroupRequirement(), "g")) {
														recordResult.setData(NumberUtil.roundStr(data / 100, 2));
													}
												}
											}
											if (StrUtil.contains(recordResult.getGroupRequirement(), "g")) {
												if (data < 10) {
													recordResult.setData(NumberUtil.roundStr(data * 100, 0));
												}
											}
										}
										// 处理不溶性硫磺
										if (StrUtil.contains(recordResult.getItem(), "溶性硫磺")) {
											if (ObjUtil.isNotNull(recordResultVO.getShouldProcessCoefficient())) {
												// 原记录占总硫，新记录不占总硫
												if (recordResultVO.getShouldProcessCoefficient()
														&& !StrUtil.contains(recordResult.getGroupRequirement(),
																"占总硫")) {
													BigDecimal targetData = NumberUtil
															.toBigDecimal(recordResultVO.getData())
															.multiply(
																	NumberUtil.toBigDecimal(
																			recordResultVO.getCalculateCoefficient()))
															.divide(
																	NumberUtil.toBigDecimal(100), 2,
																	RoundingMode.HALF_EVEN);
													recordResult.setData(targetData.toPlainString());
													recordResult.setShouldProcessCoefficient(false);
												}
												// 原记录不占总硫，新记录占总硫
												if (!recordResultVO.getShouldProcessCoefficient()
														&& StrUtil.contains(recordResult.getGroupRequirement(),
																"占总硫")) {
													BigDecimal targetData = NumberUtil
															.toBigDecimal(recordResultVO.getData())
															.multiply(
																	NumberUtil.toBigDecimal(100))
															.divide(
																	NumberUtil.toBigDecimal(
																			recordResultVO.getCalculateCoefficient()),
																	2,
																	RoundingMode.HALF_EVEN);
													recordResult.setData(targetData.toPlainString());
													recordResult.setShouldProcessCoefficient(true);
												}
											}
										}
										// 判断检测项目结果
										Integer general = isQualified(
												recordResult.getGeneralRequirement(),
												Convert.toFloat(recordResult.getData()));
										Integer group = isQualified(
												recordResult.getGroupRequirement(),
												Convert.toFloat(recordResult.getData()));
										// 判断颗粒强度检测结果
										if (StrUtil.contains(recordResult.getItem(), "颗粒强度")) {
											float data = NumberUtil.parseFloat(recordResult.getData());
											if (StrUtil.contains(recordResult.getGroupRequirement(), "CN")) {
												if (data > 10) {
													general = isQualified(recordResult.getGeneralRequirement(),
															data / 98);
													group = isQualified(recordResult.getGroupRequirement(), data);
												} else {
													general = isQualified(recordResult.getGeneralRequirement(), data);
													group = isQualified(recordResult.getGroupRequirement(), data);
												}
											} else if (StrUtil.contains(recordResult.getGroupRequirement(), "N")
													|| StrUtil.contains(recordResult.getGroupRequirement(), "暂无")) {
												if (data > 10) {
													if (StrUtil.contains(recordResultVO.getGroupRequirement(), "CN")) {
														general = isQualified(recordResult.getGeneralRequirement(),
																data / 98);
														group = isQualified(recordResult.getGroupRequirement(), data);
													}
													if (StrUtil.contains(recordResultVO.getGroupRequirement(), "g")) {
														general = isQualified(recordResult.getGeneralRequirement(),
																data / 100);
														group = isQualified(recordResult.getGroupRequirement(), data);
													}
												} else {
													general = isQualified(recordResult.getGeneralRequirement(), data);
													group = isQualified(recordResult.getGroupRequirement(), data);
												}
											}
											if (StrUtil.contains(recordResult.getGroupRequirement(), "g")) {
												if (data > 10) {
													general = isQualified(recordResult.getGeneralRequirement(),
															data / 100);
													group = isQualified(recordResult.getGroupRequirement(), data);
												} else {
													general = isQualified(recordResult.getGeneralRequirement(), data);
													group = isQualified(recordResult.getGroupRequirement(), data);
												}
											}
										}
										recordResult.setResult(general);
										recordResult.setGroupResult(group);
										recordResult.setFinalResult(
												generateFinalResult(
														general,
														group,
														requirementApplication.getGroupRequirement() != null
																|| requirementApplication
																		.getInternalControlRequirement() != null));
									}
								}
								targetRecordResultList.add(recordResult);
								itemResultMap.remove(key);
							}
						}));
		itemResultMap.forEach(
				(key, value) -> {
					RecordResultVO recordResult = BeanUtil.copyProperties(value, RecordResultVO.class);
					recordResult.setData("暂无数据");
					recordResult.setResult(null);
					recordResult.setGroupResult(null);
					recordResult.setFinalResult(null);
					recordResult.setShouldProcessCoefficient(false);
					recordResult.setCalculateCoefficient(StrUtil.toString(1));
					targetRecordResultList.add(recordResult);
				});
		report.setRecordResultList(targetRecordResultList);
		sourceSamplingResultList.forEach(
				recordResultVO -> {
					RecordResultVO recordResult = BeanUtil.copyProperties(
							recordResultMap.get(recordResultVO.getMatchingName()), RecordResultVO.class);
					if (ObjectUtil.isNotNull(recordResult)) {
						recordResult.setData(recordResultVO.getData());
						// 计算系数
						recordResult.setCalculateCoefficient(recordResultVO.getCalculateCoefficient());
						if (StrUtil.contains(recordResultVO.getData(), "暂无")) {
							recordResult.setResult(null);
							recordResult.setGroupResult(null);
							recordResult.setFinalResult(null);
						} else {
							if (ObjectUtil.isNotNull(Convert.toFloat(recordResultVO.getData()))) {
								// 处理颗粒强度
								if (StrUtil.contains(recordResult.getItem(), "颗粒强度")) {
									float data = NumberUtil.parseFloat(recordResult.getData());
									if (StrUtil.contains(recordResult.getGroupRequirement(), "CN")) {
										if (data < 10) {
											recordResult.setData(NumberUtil.roundStr(data * 98, 0));
										}
									} else if (StrUtil.contains(recordResult.getGroupRequirement(), "N")
											|| StrUtil.contains(recordResult.getGroupRequirement(), "暂无")) {
										if (data > 10) {
											if (StrUtil.contains(recordResultVO.getGroupRequirement(), "CN")) {
												recordResult.setData(NumberUtil.roundStr(data / 98, 2));
											}
											if (StrUtil.contains(recordResultVO.getGroupRequirement(), "g")) {
												recordResult.setData(NumberUtil.roundStr(data / 100, 2));
											}
										}
									}
									if (StrUtil.contains(recordResult.getGroupRequirement(), "g")) {
										if (data < 10) {
											recordResult.setData(NumberUtil.roundStr(data * 100, 0));
										}
									}
								}
								// 处理不溶性硫磺
								if (StrUtil.contains(recordResult.getItem(), "溶性硫磺")) {
									if (ObjUtil.isNotNull(recordResultVO.getShouldProcessCoefficient())) {
										// 原记录占总硫，新记录不占总硫
										if (recordResultVO.getShouldProcessCoefficient()
												&& !StrUtil.contains(recordResult.getGroupRequirement(), "占总硫")) {
											BigDecimal targetData = NumberUtil.toBigDecimal(recordResultVO.getData())
													.multiply(
															NumberUtil.toBigDecimal(
																	recordResultVO.getCalculateCoefficient()))
													.divide(NumberUtil.toBigDecimal(100), 2, RoundingMode.HALF_EVEN);
											recordResult.setData(targetData.toPlainString());
											recordResult.setShouldProcessCoefficient(false);
										}
										// 原记录不占总硫，新记录占总硫
										if (!recordResultVO.getShouldProcessCoefficient()
												&& StrUtil.contains(recordResult.getGroupRequirement(), "占总硫")) {
											BigDecimal targetData = NumberUtil.toBigDecimal(recordResultVO.getData())
													.divide(
															NumberUtil.toBigDecimal(
																	recordResultVO.getCalculateCoefficient()),
															2,
															RoundingMode.HALF_EVEN);
											recordResult.setData(targetData.toPlainString());
											recordResult.setShouldProcessCoefficient(true);
										}
									}
								}
								// 判断检测项目结果
								Integer general = isQualified(
										recordResult.getGeneralRequirement(),
										Convert.toFloat(recordResult.getData()));
								Integer group = isQualified(
										recordResult.getGroupRequirement(),
										Convert.toFloat(recordResult.getData()));
								// 判断颗粒强度检测结果
								if (StrUtil.contains(recordResult.getItem(), "颗粒强度")) {
									float data = NumberUtil.parseFloat(recordResult.getData());
									if (StrUtil.contains(recordResult.getGroupRequirement(), "CN")) {
										if (data > 10) {
											general = isQualified(recordResult.getGeneralRequirement(), data / 98);
											group = isQualified(recordResult.getGroupRequirement(), data);
										} else {
											general = isQualified(recordResult.getGeneralRequirement(), data);
											group = isQualified(recordResult.getGroupRequirement(), data);
										}
									} else if (StrUtil.contains(recordResult.getGroupRequirement(), "N")
											|| StrUtil.contains(recordResult.getGroupRequirement(), "暂无")) {
										if (data > 10) {
											if (StrUtil.contains(recordResultVO.getGroupRequirement(), "CN")) {
												general = isQualified(recordResult.getGeneralRequirement(), data / 98);
												group = isQualified(recordResult.getGroupRequirement(), data);
											}
											if (StrUtil.contains(recordResultVO.getGroupRequirement(), "g")) {
												general = isQualified(recordResult.getGeneralRequirement(), data / 100);
												group = isQualified(recordResult.getGroupRequirement(), data);
											}
										} else {
											general = isQualified(recordResult.getGeneralRequirement(), data);
											group = isQualified(recordResult.getGroupRequirement(), data);
										}
									}
									if (StrUtil.contains(recordResult.getGroupRequirement(), "g")) {
										if (data > 10) {
											general = isQualified(recordResult.getGeneralRequirement(), data / 100);
											group = isQualified(recordResult.getGroupRequirement(), data);
										} else {
											general = isQualified(recordResult.getGeneralRequirement(), data);
											group = isQualified(recordResult.getGroupRequirement(), data);
										}
									}
								}
								recordResult.setResult(general);
								recordResult.setGroupResult(group);
								recordResult.setFinalResult(
										generateFinalResult(
												general,
												group,
												requirementApplication.getGroupRequirement() != null
														|| requirementApplication
																.getInternalControlRequirement() != null));
							}
						}
						targetSamplingResultList.add(recordResult);
					}
				});
		report.setSamplingResultList(targetSamplingResultList);

		// 排序相关
		List<String> orderList = new ArrayList<>();
		LambdaQueryWrapper<ProductTypeForSort> productTypeForSortLambdaQueryWrapper = new LambdaQueryWrapper<>();
		productTypeForSortLambdaQueryWrapper.eq(
				ProductTypeForSort::getProductCategoryNumber,
				requirementApplication.getProductCategoryNumber());
		List<ProductTypeForSort> productTypeForSorts = productTypeForSortMapper
				.selectList(productTypeForSortLambdaQueryWrapper);
		if (!productTypeForSorts.isEmpty()) {
			LambdaQueryWrapper<ItemPriorityForSort> itemPriorityForSortLambdaQueryWrapper = new LambdaQueryWrapper<>();
			itemPriorityForSortLambdaQueryWrapper.eq(
					ItemPriorityForSort::getProductTypeId, productTypeForSorts.get(0).getId());
			List<ItemPriorityForSort> itemPriorityForSorts = itemPriorityForSortMapper
					.selectList(itemPriorityForSortLambdaQueryWrapper);
			List<ItemPriorityForSort> sortedList = itemPriorityForSorts.stream()
					.sorted(
							Comparator.comparingInt(ItemPriorityForSort::getPriority)
									.reversed()
									.thenComparing(ItemPriorityForSort::getItem))
					.collect(Collectors.toList());
			sortedList.forEach(itemPriorityForSort -> orderList.add(itemPriorityForSort.getItem()));
		}
		log.info(orderList.toString());
		// 自定义比较器，优先按照orderList排序，orderList中不包括的项目置底
		Comparator<RecordResultVO> itemComparator = (o1, o2) -> {
			String item1 = o1.getMatchingName().split(",")[0];
			String item2 = o2.getMatchingName().split(",")[0];
			// 通过正则表达式提取括号前的内容
			String regex = "^(.*?)\\(";
			Pattern pattern = Pattern.compile(regex);
			Matcher matcher1 = pattern.matcher(item1);
			Matcher matcher2 = pattern.matcher(item2);
			String itemCategory1 = matcher1.find() ? matcher1.group(1) : item1;
			String itemCategory2 = matcher2.find() ? matcher2.group(1) : item2;
			// 如果都在 orderList 中，按照 orderList 排序；否则，未在 orderList 中的排在后面
			if (orderList.contains(itemCategory1) && orderList.contains(itemCategory2)) {
				return Integer.compare(
						orderList.indexOf(itemCategory1), orderList.indexOf(itemCategory2));
			} else if (orderList.contains(itemCategory1)) {
				return -1;
			} else if (orderList.contains(itemCategory2)) {
				return 1;
			} else {
				return item1.compareTo(item2);
			}
		};
		report.getRecordResultList().sort(itemComparator);
		report.getSamplingResultList().sort(itemComparator);

		if (StrUtil.equals(applicationReleaseDTO.getInboundCompany(), "0")) {
			report.setIsPreDispersedCustomer(false);
		}
		return report;
	}

	@Override
	public IPage<ApplicationOperationLogVO> getApplicationOperationLog(
			int currentPage, int pageSize, OperationLogQuery operationLogQuery) {
		MPJLambdaWrapper<ApplicationOperationLog> logWrapper = new MPJLambdaWrapper<ApplicationOperationLog>()
				.selectAll(ApplicationOperationLog.class)
				.selectAs(User::getUsername, ApplicationOperationLogVO::getOperator)
				.selectAs(
						ApplicationOperationLog::getTimestamp, ApplicationOperationLogVO::getOperationTime)
				.leftJoin(User.class, User::getId, ApplicationOperationLog::getOperatorId)
				.eq(ApplicationOperationLog::getLinkId, operationLogQuery.getLinkId())
				.like(ApplicationOperationLog::getProductType, operationLogQuery.getProductType())
				.like(
						ApplicationOperationLog::getProductionBatch, operationLogQuery.getProductionBatch())
				.like(
						StrUtil.equals(operationLogQuery.getCustomerName(), "预生产"),
						ApplicationOperationLog::getCustomerName,
						" ")
				.like(
						!StrUtil.equals(operationLogQuery.getCustomerName(), "预生产"),
						ApplicationOperationLog::getCustomerName,
						operationLogQuery.getCustomerName())
				.ge(
						Strings.isNotEmpty(operationLogQuery.getStartDate()),
						ApplicationOperationLog::getTimestamp,
						operationLogQuery.getStartDate())
				.le(
						Strings.isNotEmpty(operationLogQuery.getEndDate()),
						ApplicationOperationLog::getTimestamp,
						operationLogQuery.getEndDate() + " 23:59:59")
				.orderByDesc(ApplicationOperationLog::getTimestamp);
		Page<ApplicationOperationLogVO> applicationOperationLogVOPage = applicationOperationLogMapper.selectJoinPage(
				new Page<>(currentPage, pageSize), ApplicationOperationLogVO.class, logWrapper);
		applicationOperationLogVOPage
				.getRecords()
				.forEach(
						applicationOperationLogVO -> {
							if (StrUtil.isBlank(applicationOperationLogVO.getCustomerName())) {
								applicationOperationLogVO.setCustomerName("预生产");
							}
						});
		return applicationOperationLogVOPage;
	}

	@Override
	public void productionDateAdjustment() {
		MPJLambdaWrapper<QualityInspectionApplication> wrapper = new MPJLambdaWrapper<QualityInspectionApplication>()
				.isNull(QualityInspectionApplication::getProductionDate)
				.isNull(QualityInspectionApplication::getProductionShift)
				.ge(QualityInspectionApplication::getApplicationDate, "2023-10-19");
		List<QualityInspectionApplication> qualityInspectionApplicationList = qualityInspectionApplicationMapper
				.selectJoinList(
						QualityInspectionApplication.class, wrapper);
		qualityInspectionApplicationList.forEach(
				application -> {
					log.info(application.toString());
					LocalDateTime localDateTime = DateUtil.toLocalDateTime(application.getApplicationDate());
					int hour = localDateTime.getHour();
					if (hour >= 1 && hour < 9) {
						application.setProductionShift("夜班");
						application.setProductionDate(application.getApplicationDate());
					}
					if (hour >= 9 && hour <= 17) {
						application.setProductionShift("白班");
						application.setProductionDate(application.getApplicationDate());
					}
					if (hour >= 17 || hour < 1) {
						application.setProductionShift("中班");
						Calendar calendar = Calendar.getInstance();
						calendar.setTime(application.getApplicationDate());
						int applicationHour = calendar.get(Calendar.HOUR_OF_DAY);

						if (applicationHour != 0) {
							application.setProductionDate(application.getApplicationDate());

						} else {
							calendar.add(Calendar.DAY_OF_MONTH, -1);
							application.setProductionDate(calendar.getTime());
						}
					}
					log.info(application.getProductionShift() + "-" + application.getProductionDate());
					qualityInspectionApplicationMapper.updateById(application);
				});
	}

	@Override
	public boolean reportPictureCreat(ReportVO report, int applicationId) {
		Workbook workbook = new Workbook();
		OutboundReportTemplate outboundReportTemplate = outboundReportTemplateMapper.selectById(2);
		workbook.loadFromFile(outboundReportTemplate.getPath());
		Worksheet sheet = workbook.getWorksheets().get(0);

		CellRange title = sheet.getCellRange(1, 1);
		title.setText(report.getProductType() + "质量报告单");

		CellRange coding = sheet.getCellRange(2, 5);
		coding.setText(report.getCoding());

		CellRange productType = sheet.getCellRange(3, 2);
		productType.setText(report.getProductType());

		CellRange molecularFormula = sheet.getCellRange(3, 4);
		molecularFormula.setText(report.getMolecularFormula());

		CellRange molecularWeight = sheet.getCellRange(3, 6);
		molecularWeight.setText(report.getMolecularWeight());

		CellRange productionBatch = sheet.getCellRange(4, 2);
		productionBatch.setText(report.getProductionBatch());

		CellRange tankBatch = sheet.getCellRange(4, 4);
		tankBatch.setText(report.getTankBatch());

		CellRange packagingSpecification = sheet.getCellRange(4, 6);
		packagingSpecification.setText(report.getPackagingSpecification() + "KG/袋");

		CellRange qualityInspectionBatch = sheet.getCellRange(5, 2);
		qualityInspectionBatch.setText(report.getQualityInspectionBatch());

		CellRange specificationBasis = sheet.getCellRange(5, 5);
		specificationBasis.setText(report.getSpecificationBasis());

		CellRange customerName = sheet.getCellRange(6, 2);
		customerName.setText(report.getCustomerName());

		CellRange quantity = sheet.getCellRange(7, 2);
		quantity.setText(report.getQuantity());

		CellRange numberOfSamples = sheet.getCellRange(7, 4);
		numberOfSamples.setText(report.getNumberOfSamples());

		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
		String formattedDate = simpleDateFormat.format(report.getSamplingDate());
		CellRange samplingDate = sheet.getCellRange(7, 6);
		samplingDate.setText(formattedDate);

		List<RecordResultVO> recordResultList = report.getRecordResultList();
		for (int i = 0; i < recordResultList.size(); i++) {
			RecordResultVO recordResult = recordResultList.get(i);
			if (i > 0) {
				sheet.insertRow(10 + i, 1, InsertOptionsType.FormatAsBefore);
			}
			CellRange itemName = sheet.getCellRange(10 + i, 1);
			itemName.setText(recordResult.getItem());
			CellRange generalRequirement = sheet.getCellRange(10 + i, 2);
			if (!StrUtil.contains(recordResult.getGeneralRequirement(), "暂无")) {
				generalRequirement.setText(
						StrUtil.subBefore(recordResult.getGeneralRequirement(), ",", true));
			} else {
				generalRequirement.setText("暂无");
			}
			CellRange groupRequirement = sheet.getCellRange(10 + i, 3);
			if (!StrUtil.contains(recordResult.getGroupRequirement(), "暂无")) {
				groupRequirement.setText(StrUtil.subBefore(recordResult.getGroupRequirement(), ",", true));
			} else {
				groupRequirement.setText("暂无");
			}
			CellRange data = sheet.getCellRange(10 + i, 4);
			data.setText(recordResult.getData());
			CellRange finalResult = sheet.getCellRange(10 + i, 5);
			if (ObjUtil.isNotNull(recordResult.getFinalResult())) {
				switch (recordResult.getFinalResult()) {
					case 0:
						finalResult.setText("不合格");
						break;
					case 2:
						finalResult.setText("不符合");
						break;
					default:
						finalResult.setText("合格");
						break;
				}
			} else {
				finalResult.setText("/");
			}
			CellRange inspector = sheet.getCellRange(10 + i, 6);
			inspector.setText(recordResult.getInspector());
		}

		CellRange result = sheet.getCellRange(10 + recordResultList.size(), 2);
		result.setText(report.getResult());
		CellRange inspector1 = sheet.getCellRange(10 + recordResultList.size(), 5);
		inspector1.setText(report.getInspector1());
		CellRange inspector2 = sheet.getCellRange(10 + recordResultList.size(), 6);
		inspector2.setText(report.getInspector2());
		CellRange auditor1 = sheet.getCellRange(11 + recordResultList.size(), 5);
		auditor1.setText(report.getAuditor1());
		CellRange auditor2 = sheet.getCellRange(11 + recordResultList.size(), 6);
		auditor2.setText(report.getAuditor2());

		// 目录信息
		String directoryInfo = reportPath + report.getProductType() + "\\";
		// 创建目录对象
		File directory = new File(directoryInfo);
		// 判断当前目录是否存在
		if (!directory.exists()) {
			// 目录不存在，需要创建
			boolean mkdirs = directory.mkdirs();
			if (!mkdirs) {
				return false;
			}
		}
		// 文件名去除非法字符
		String fileName = FileNameUtil.cleanInvalid(
				report.getCustomerName() + "_" + report.getProductType() + "_" + applicationId);
		// 文件路径
		String path = directoryInfo + fileName;
		// String path = directoryInfo + report.getCustomerName() + "_" +
		// report.getProductType()
		// + "_" + applicationId;

		ConverterSetting converterSetting = workbook.getConverterSetting();
		converterSetting.setSheetFitToPage(true);
		converterSetting.setSheetFitToWidth(true);
		workbook.getWorksheets().get(0).saveToImage(path + ".png");
		return true;
	}

	/**
	 * 获取指标map
	 *
	 * @param requirement 指标字符串
	 * @return 指标map
	 */
	private Map<String, Map<String, String>> getRequirementMap(String requirement) {
		// 创建指标map
		Map<String, Map<String, String>> requirementMap = new HashMap<>();
		// 无对应指标
		if (requirement == null) {
			return requirementMap;
		}
		// 处理指标字符串
		String[] pairs = requirement.split(";");
		for (String pair : pairs) {
			String[] keyValue = pair.split(":");
			if (keyValue.length == 2) {
				String item = keyValue[0];
				String itemRequirement = keyValue[1];
				Map<String, String> itemMap = new HashMap<>();
				if (item.contains("|")) {
					String[] split = item.split("\\|");
					String[] itemNameSplit = split[1].split(",");
					if (itemNameSplit.length > 1) {
						itemMap.put("itemName", itemNameSplit[0] + "(" + itemNameSplit[1] + ")");
					} else {
						itemMap.put("itemName", split[1]);
					}
					itemMap.put("matchingName", split[0]);
					// itemMap.put("itemName", split[1]);
					itemMap.put("requirement", itemRequirement);
					requirementMap.put(split[0], itemMap);
				} else {
					String[] itemNameSplit = item.split(",");
					if (itemNameSplit.length > 1) {
						itemMap.put("itemName", itemNameSplit[0] + "(" + itemNameSplit[1] + ")");
					} else {
						itemMap.put("itemName", item);
					}
					itemMap.put("matchingName", item);
					// itemMap.put("itemName", item);
					itemMap.put("requirement", itemRequirement);
					requirementMap.put(item, itemMap);
				}
			}
		}
		return requirementMap;
	}

	/**
	 * 填充未提交的基础信息
	 *
	 * @param basicInformationNotSaved     未提交的基础信息
	 * @param applicationDTO               质检申请DTO
	 * @param qualityInspectionApplication 质检申请
	 * @param applicationIds               质检申请id
	 */
	private void addInformation(
			BasicInformationNotSaved basicInformationNotSaved,
			ApplicationDTO applicationDTO,
			QualityInspectionApplication qualityInspectionApplication,
			List<Integer> applicationIds) {
		// 所属厂区
		basicInformationNotSaved.setLinkId(qualityInspectionApplication.getLinkId());
		// 质检申请id
		basicInformationNotSaved.setProductionInformation(applicationIds.toString());
		// 记录编码
		basicInformationNotSaved.setCoding("Q/SSHG（03）8.6-5");
		// 质检批号
		basicInformationNotSaved.setQualityInspectionBatch(applicationDTO.getQualityInspectionBatch());
		// 产品类型
		basicInformationNotSaved.setProductType(qualityInspectionApplication.getProductType());
		basicInformationNotSaved.setProductCategoryNumber(
				qualityInspectionApplication.getProductCategoryNumber());
		// 取样数
		basicInformationNotSaved.setNumberOfSamples(applicationDTO.getSampleNumber());
		// 取样日期
		basicInformationNotSaved.setSamplingDate(applicationDTO.getSamplingDate());
	}

	private Integer isQualified(String requirement, float result) {
		log.info("指标：" + requirement + "；计算结果：" + result);
		if (requirement.contains("暂无")) {
			return null;
		}

		if (StrUtil.equals(requirement, "/")) {
			return 2;
		}

		float min = Float.MIN_VALUE;
		float max = Float.MAX_VALUE;

		Pattern pattern = Pattern.compile(
				"(?<symbol>[≥≤><＞＜=])?(?<value>\\d+(\\.\\d+)?)((?<rangeSeparator>[±-])(?<maxValue>\\d+(\\.\\d+)?))?(?<unit>[\\p{L}\\p{S}\\p{Zs}\\p{P}]*)");
		Matcher matcher = pattern.matcher(requirement);

		if (matcher.find()) {
			String symbol = matcher.group("symbol");
			log.info("符号：" + symbol);
			String rangeSeparator = matcher.group("rangeSeparator");
			log.info("区间符号：" + rangeSeparator);
			float value = Float.parseFloat(matcher.group("value"));
			log.info("指标数值：" + value);
			String maxValueStr = matcher.group("maxValue");
			float maxValue = maxValueStr != null ? Float.parseFloat(maxValueStr) : Float.MAX_VALUE;
			log.info("指标范围值：" + maxValue);
			String unit = matcher.group("unit");
			log.info("单位：" + unit);

			// requirement不包含任何限制条件
			if (symbol == null && rangeSeparator == null) {
				log.info("requirement不包含任何限制条件");
				String format = "%.2f";
				String valueString = String.format(format, value);
				String resultString = String.format(format, result);
				log.info("指标数值：" + valueString);
				log.info("结果数值：" + resultString);
				return valueString.equals(resultString) ? 1 : 0;
			}

			// 将带有区间的指标转换为±格式
			if (symbol == null) {
				log.info("将带有区间的指标统一转换为±格式");
				if ("-".equals(rangeSeparator)) {
					float centerValue = (value + maxValue) / 2;
					float deviation = (maxValue - value) / 2;
					value = centerValue;
					log.info("数值：" + value);
					maxValue = deviation;
					log.info("范围值：" + maxValue);
					symbol = "±";
				} else if ("±".equals(rangeSeparator)) {
					symbol = "±";
				}
			}

			switch (Objects.requireNonNull(symbol)) {
				case "≥":
					min = value;
					log.info("最小值：" + min);
					log.info("最大值：" + max);
					break;
				case "＞":
				case ">":
					min = value + 0.00001f;
					log.info("最小值：" + min);
					log.info("最大值：" + max);
					break;
				case "≤":
					max = value;
					min = 0;
					log.info("最小值：" + min);
					log.info("最大值：" + max);
					break;
				case "＜":
				case "<":
					max = value - 0.00001f;
					min = 0;
					log.info("最小值：" + min);
					log.info("最大值：" + max);
					break;
				case "±":
					min = value - maxValue;
					max = value + maxValue;
					log.info("最小值：" + min);
					log.info("最大值：" + max);
					break;
				default:
					min = value;
					max = value;
					log.info("最小值：" + min);
					log.info("最大值：" + max);
					break;
			}
		}
		BigDecimal resultBigDecimal = new BigDecimal(result);
		BigDecimal minBigDecimal = new BigDecimal(min);
		BigDecimal maxBigDecimal = new BigDecimal(max);

		// 根据最小值和最大值进行判断
		boolean greaterOrEqual = NumberUtil.isGreaterOrEqual(resultBigDecimal, minBigDecimal);
		boolean lessOrEqual = NumberUtil.isLessOrEqual(resultBigDecimal, maxBigDecimal);
		return (greaterOrEqual && lessOrEqual) ? 1 : 0;
	}

	/**
	 * 获取数据计算最终结果
	 *
	 * @param general 通用结果
	 * @param group   客户结果
	 * @return 最终结果（0：不合格；1：合格；2：不符合；null：暂无数据）
	 */
	public Integer generateFinalResult(Integer general, Integer group, boolean groupRequirement) {
		// 初始化最终结果
		Integer finalResult = null;
		// 客户结果为null（无客户指标）
		if (group == null) {
			// 通用指标不为“/”
			if (general != 2) {
				finalResult = general;
			}
			// 客户结果合格
		} else if (group == 1) {
			// 最终结果合格
			finalResult = 1;
			// 客户结果不合格
		} else if (group == 0) {
			// 通用结果也不合格
			if (general != null && general == 0) {
				// 最终结果不合格
				finalResult = 0;
				// 通用结果是合格以外的状态
			} else {
				// 最终结果为不符合
				finalResult = 2;
			}
		}
		return finalResult;
	}
}
