package com.haihang.controller.outbound;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.haihang.common.R;
import com.haihang.model.DTO.outbound.customerApproval.CustomerApprovalDTO;
import com.haihang.model.DTO.outbound.customerApproval.CustomerApprovalDetailDTO;
import com.haihang.model.DTO.outbound.customerApproval.CustomerApprovalReviewDTO;
import com.haihang.model.Query.outbound.CustomerApprovalQuery;
import com.haihang.service.outbound.CustomerApprovalService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: 客户审批Controller
 * @Author: zad
 * @Create: 2024/12/20
 */
@RestController
@RequestMapping("/outbound/customerApproval")
@RequiredArgsConstructor
@Slf4j
public class CustomerApprovalController {
    
    private final CustomerApprovalService customerApprovalService;
    
    /**
     * 分页查询客户审批列表
     */
    @PostMapping("/getCustomerApprovalPage")
    public R getCustomerApprovalPage(@RequestBody CustomerApprovalQuery query) {
        try {
            IPage<CustomerApprovalDTO> result = customerApprovalService.getCustomerApprovalPage(
                    query.getCurrent(), query.getSize(), query);
            return new R(true, result);
        } catch (Exception e) {
            log.error("查询客户审批列表失败", e);
            return new R(false, "查询失败");
        }
    }
    
    /**
     * 审批客户申请
     */
    @PostMapping("/approveCustomerApproval")
    public R approveCustomerApproval(@RequestBody CustomerApprovalReviewDTO reviewDTO) {
        try {
            boolean result = customerApprovalService.approveCustomerApproval(
                    reviewDTO.getId(), reviewDTO.getStatus(), reviewDTO.getApprover());
            return result ? new R(true, "审批成功") : new R(false, "审批失败");
        } catch (Exception e) {
            log.error("审批客户申请失败", e);
            return new R(false, "审批失败");
        }
    }
    
    /**
     * 获取客户审批详情
     */
    @GetMapping("/getCustomerApprovalDetail/{id}")
    public R getCustomerApprovalDetail(@PathVariable Integer id) {
        try {
            CustomerApprovalDTO result = customerApprovalService.getCustomerApprovalDetail(id);
            return result != null ? new R(true, result) : new R(false, "记录不存在");
        } catch (Exception e) {
            log.error("获取客户审批详情失败", e);
            return new R(false, "获取详情失败");
        }
    }
    
    /**
     * 获取客户审批详情，包含检测数据
     */
    @GetMapping("/getCustomerApprovalDetailWithInspectionData/{id}")
    public R getCustomerApprovalDetailWithInspectionData(@PathVariable Integer id) {
        try {
            CustomerApprovalDetailDTO result = customerApprovalService.getCustomerApprovalDetailWithInspectionData(id);
            return result != null ? new R(true, result) : new R(false, "记录不存在");
        } catch (Exception e) {
            log.error("获取客户审批详情失败", e);
            return new R(false, "获取详情失败");
        }
    }
} 