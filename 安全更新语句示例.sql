-- ==========================================
-- 安全更新 is_separate_report_per_batch 字段的方法
-- ==========================================

-- 1. 首先查询当前记录状态
SELECT 
    jt_bh,
    jt_mc,
    is_separate_report_per_batch,
    update_time,
    operator
FROM cmf_0430.cmf_jt_xx 
WHERE jt_bh = '056' AND jt_mc = '赛轮集团';

-- ==========================================
-- 2. 方法一：使用主键字段更新（推荐）
-- ==========================================

-- 使用主要标识字段进行更新
UPDATE cmf_0430.cmf_jt_xx 
SET is_separate_report_per_batch = '{"1": false, "2": false, "3": true}' 
WHERE jt_bh = '056' AND jt_mc = '赛轮集团';

-- ==========================================
-- 3. 方法二：如果担心误更新，先用SELECT测试
-- ==========================================

-- 先用SELECT确认要更新的记录
SELECT jt_bh, jt_mc, is_separate_report_per_batch
FROM cmf_0430.cmf_jt_xx 
WHERE jt_bh = '056' 
  AND jt_mc = '赛轮集团'
  AND is_separate_report_per_batch = '{"1": true, "2": false, "3": true}';

-- 如果上面的查询返回了记录，再执行更新
UPDATE cmf_0430.cmf_jt_xx 
SET is_separate_report_per_batch = '{"1": false, "2": false, "3": true}' 
WHERE jt_bh = '056' 
  AND jt_mc = '赛轮集团'
  AND is_separate_report_per_batch = '{"1": true, "2": false, "3": true}';

-- ==========================================
-- 4. 方法三：使用事务确保安全
-- ==========================================

START TRANSACTION;

-- 查看更新前的状态
SELECT 'Before Update' as status, jt_bh, jt_mc, is_separate_report_per_batch
FROM cmf_0430.cmf_jt_xx 
WHERE jt_bh = '056' AND jt_mc = '赛轮集团';

-- 执行更新
UPDATE cmf_0430.cmf_jt_xx 
SET is_separate_report_per_batch = '{"1": false, "2": false, "3": true}' 
WHERE jt_bh = '056' AND jt_mc = '赛轮集团';

-- 查看更新后的状态
SELECT 'After Update' as status, jt_bh, jt_mc, is_separate_report_per_batch
FROM cmf_0430.cmf_jt_xx 
WHERE jt_bh = '056' AND jt_mc = '赛轮集团';

-- 如果结果正确，提交事务
COMMIT;
-- 如果结果不正确，回滚事务
-- ROLLBACK;

-- ==========================================
-- 5. 检查更新结果
-- ==========================================

-- 验证更新是否成功
SELECT 
    jt_bh,
    jt_mc,
    is_separate_report_per_batch,
    -- 如果MySQL支持JSON函数，可以解析各个语言类型的设置
    JSON_EXTRACT(is_separate_report_per_batch, '$.1') as chinese_setting,
    JSON_EXTRACT(is_separate_report_per_batch, '$.2') as english_setting,
    JSON_EXTRACT(is_separate_report_per_batch, '$.3') as bilingual_setting
FROM cmf_0430.cmf_jt_xx 
WHERE jt_bh = '056' AND jt_mc = '赛轮集团';

-- ==========================================
-- 6. 批量更新多个记录的示例
-- ==========================================

-- 如果需要更新多个集团的配置
-- UPDATE cmf_0430.cmf_jt_xx 
-- SET is_separate_report_per_batch = '{"1": false, "2": false, "3": true}' 
-- WHERE jt_bh IN ('056', '057', '058');

-- ==========================================
-- 7. 常见问题排查
-- ==========================================

-- 检查字段类型是否支持JSON
SHOW COLUMNS FROM cmf_0430.cmf_jt_xx LIKE 'is_separate_report_per_batch';

-- 检查是否有其他约束或触发器
SHOW CREATE TABLE cmf_0430.cmf_jt_xx;

-- 检查是否有权限问题
SHOW GRANTS; 