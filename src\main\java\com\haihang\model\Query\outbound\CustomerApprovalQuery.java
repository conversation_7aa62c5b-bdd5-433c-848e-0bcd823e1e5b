package com.haihang.model.Query.outbound;

import lombok.Data;

import java.util.List;

/**
 * @Description: 客户审批查询条件
 * @Author: zad
 * @Create: 2024/12/20
 */
@Data
public class CustomerApprovalQuery {
    
    /**
     * 当前页码
     */
    private Integer current;
    
    /**
     * 每页显示的记录数
     */
    private Integer size;
    
    /**
     * 总记录数
     */
    private Integer total;
    
    /**
     * 公司ID列表
     */
    private List<Integer> linkId;
    
    /**
     * 客户名称
     */
    private String customerName;
    
    /**
     * 产品名称
     */
    private String productName;
    
    /**
     * 生产批号
     */
    private String productionBatch;
    
    /**
     * 质检批号
     */
    private String qualityInspectionBatch;
    
    /**
     * 审批状态
     */
    private Integer status;
    
    /**
     * 我方合同号
     */
    private String ourContractNumber;
    
    /**
     * 开始日期
     */
    private String startDate;
    
    /**
     * 结束日期
     */
    private String endDate;
    
    /**
     * 查询日期范围（用于前端传递）
     */
    private String[] queryDate;
} 