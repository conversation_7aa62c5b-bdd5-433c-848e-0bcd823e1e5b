package com.haihang.model.DTO.outbound.transportReport;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * 随车质检单导出数据DTO
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@Accessors(chain = true)
public class TransportReportExportData {
    
    /**
     * 客户名称
     */
    private String customerName;
    
    /**
     * 产品名称
     */
    private String productName;
    
    /**
     * 产品类别名称
     */
    private String productCategoryName;
    
    /**
     * 产品类别编号
     */
    private String productCategoryNumber;
    
    /**
     * 数量
     */
    private String quantity;
    
    /**
     * 规格
     */
    private String specification;
    
    /**
     * 执行标准
     */
    private String executionStandard;
    
    /**
     * 生产批号
     */
    private String productionBatch;
    
    /**
     * 生产日期
     */
    private String productionDate;
    
    /**
     * 检测项目数据
     * key: 检测项目名称
     * value: 检测数据值
     */
    private Map<String, String> inspectionData;
    
    /**
     * 检测结论
     */
    private String inspectionConclusion;
    
    /**
     * 主检
     */
    private String inspector;
    
    /**
     * 校核
     */
    private String verifier;
    
    /**
     * 审核
     */
    private String auditor;
    
    /**
     * 出单日期
     */
    private String reportDate;
}
