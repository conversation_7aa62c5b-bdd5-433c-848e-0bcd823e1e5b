package com.haihang.model.DO.outbound;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 运输报告公司实体类
 * <p>
 * 该类用于存储和管理运输报告相关的公司信息，包括公司基本信息、备注信息及操作记录等。
 * 与数据库表 cmf_transport_report_company 一一对应。
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@Accessors(chain = true)
@TableName(value = "cmf_transport_report_company")
public class TransportReportCompany {
    
    /**
     * 主键ID
     * <p>数据库自增主键，唯一标识一条公司记录</p>
     */
    @TableId(value = "id")
    private Integer id;
    
    /**
     * 集团ID
     * <p>关联集团信息的外键，标识该公司所属的集团</p>
     */
    private Integer groupId;
    
    /**
     * 公司编号
     * <p>公司的唯一编号，用于系统内部识别</p>
     */
    private String companyNumber;
    
    /**
     * 公司名称
     * <p>公司的完整名称</p>
     */
    private String companyName;
    
    /**
     * 检测记录备注
     * <p>与检测记录相关的特殊说明或备注信息</p>
     */
    private String inspectionRecordRemark;
    
    /**
     * 备注信息
     * <p>关于该公司的一般性备注说明</p>
     */
    private String remark;

    /**
     * 随车质检单排除字段
     * <p>
     * 用于存储随车质检单中需要排除的字段信息
     * </p>
     */
    private String transportReportExcludeFields;

    private Boolean generalExecutionStandard;
    private String executionStandard;
    private Boolean generalConclusion;
    private String conclusion;

    /**
     * 是否强制应用执行标准配置（不考虑客户指标）
     * <p>
     * true: 直接按配置覆盖执行标准，不判断客户是否有指标
     * false: 先判断客户是否有指标，无指标时不覆盖，有指标时按配置覆盖
     * </p>
     */
    private Boolean forceApplyExecutionStandard;

    /**
     * 是否强制应用结论配置（不考虑客户指标）
     * <p>
     * true: 直接按配置覆盖结论，不判断客户是否有指标
     * false: 先判断客户是否有指标，无指标时不覆盖，有指标时按配置覆盖
     * </p>
     */
    private Boolean forceApplyConclusion;

    /**
     * 是否每个生产批号生成单独报告
     * <p>
     * 控制是否为每个生产批号生成单独的运输报告，而非将多个批号合并在一个报告中
     * 支持两种格式：
     * 1. Boolean类型（向后兼容）
     * 2. JSON字符串，按语言类型分别设置：{"1": false, "2": true, "3": false}
     *    其中1-中文，2-英文，3-中英文
     * </p>
     */
    private Object isSeparateReportPerBatch;
    
    /**
     * 操作人ID
     * <p>最后一次修改该记录的用户ID</p>
     */
    private Integer operatorId;
    
    /**
     * 操作人名称
     * <p>最后一次修改该记录的用户名称</p>
     */
    private String operatorName;
    
    /**
     * 操作时间
     * <p>最后一次修改该记录的时间戳</p>
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime operateTime;

    /**
     * 客户自定义字段1的行位置
     * <p>
     * 用于在模板中定位客户自定义内容1的行位置
     * </p>
     */
    private Integer customerRow1;

    /**
     * 客户自定义字段1的列位置
     * <p>
     * 用于在模板中定位客户自定义内容1的列位置
     * </p>
     */
    private Integer customerColumn1;

    /**
     * 客户自定义字段1的内容
     * <p>
     * 用于存储客户自定义内容1的具体值
     * </p>
     */
    private String customerItem1;

    /**
     * 客户自定义字段2的行位置
     * <p>
     * 用于在模板中定位客户自定义内容2的行位置
     * </p>
     */
    private Integer customerRow2;

    /**
     * 客户自定义字段2的列位置
     * <p>
     * 用于在模板中定位客户自定义内容2的列位置
     * </p>
     */
    private Integer customerColumn2;

    /**
     * 客户自定义字段2的内容
     * <p>
     * 用于存储客户自定义内容2的具体值
     * </p>
     */
    private String customerItem2;

    /**
     * 客户自定义字段3的行位置
     * <p>
     * 用于在模板中定位客户自定义内容3的行位置
     * </p>
     */
    private Integer customerRow3;

    /**
     * 客户自定义字段3的列位置
     * <p>
     * 用于在模板中定位客户自定义内容3的列位置
     * </p>
     */
    private Integer customerColumn3;

    /**
     * 客户自定义字段3的内容
     * <p>
     * 用于存储客户自定义内容3的具体值
     * </p>
     */
    private String customerItem3;

    /**
     * 客户自定义字段4的行位置
     * <p>
     * 用于在模板中定位客户自定义内容4的行位置
     * </p>
     */
    private Integer customerRow4;

    /**
     * 客户自定义字段4的列位置
     * <p>
     * 用于在模板中定位客户自定义内容4的列位置
     * </p>
     */
    private Integer customerColumn4;

    /**
     * 客户自定义字段4的内容
     * <p>
     * 用于存储客户自定义内容4的具体值
     * </p>
     */
    private String customerItem4;
}
