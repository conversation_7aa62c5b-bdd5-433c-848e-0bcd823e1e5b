<!DOCTYPE html>
<html lang="en">
<head>
    <!-- 页面meta -->
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>指标维护-尚舜化工</title>
    <meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" name="viewport">
    <!-- 引入样式 -->
    <link rel="stylesheet" href="../../plugins/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="../../css/style.css">
    <link rel="icon" type="image/png" href="../../picture/logo.png">
    <style>
        [v-cloak] {
            display: none;
        }

        /* 字体设置 */
        @font-face {
            font-family: "zad";
            src: url("../../font/MiSans-Medium.woff2");
            /*font-weight: bold;*/
        }

        /* 字体应用组件 */
        html, body, button, input, select, textarea, form {
            font-family: "zad", sans-serif;
            font-size: 14px !important;
        }

        .el-input .el-input__inner {
            font-size: 14px !important;
            text-align: center !important;
        }

        .el-divider__text {
            font-size: 18px !important;
        }

        .el-message__content {
            font-size: 14px !important;
        }

        .el-tag {
            font-size: 14px !important;
        }

        .el-table .cell {
            font-size: 16px !important;
        }

        .el-form-item .el-form-item__label {
            font-size: 16px !important; /* 更改此值以设置所需的字体大小，并使用 !important 标志 */
        }

        .el-dialog__title {
            font-size: 22px !important; /* 修改为自己想要的字体大小 */
        }

        .el-tooltip__popper {
            font-size: 14px !important; /* 更改此值以设置所需的字体大小 */
        }

        .el-descriptions-item__label,
        .el-descriptions-item__content {
            font-size: 16px !important; /* 修改为你需要的字体大小 */
        }

        .custom-prepend-text {
            font-size: 14px !important; /* 更改此值以设置所需的字体大小 */
        }

        /* 修改标题文字大小 */
        .el-message-box__header .el-message-box__title {
            font-size: 18px !important; /* 更改此值以设置标题文字的字体大小 */
        }

        /* 修改内容文字大小 */
        .el-message-box__message {
            font-size: 16px !important; /* 更改此值以设置内容文字的字体大小 */
        }


        /*.el-table__body tr:hover > td {
            background-color: #FFDAB9 !important;
        }*/

        .el-table__body .el-table__row.hover-row td {
            background-color: #FFDAB9 !important;
        }


        /* 选中某行颜色 */
        .el-table--striped .el-table__body tr.el-table__row--striped.current-row td,
        .el-table__body tr.current-row > td {
            background-color: #FFDAB9 !important;
        }

        .el-select .el-select__input,
        .el-select-dropdown .el-select-dropdown__item {
            text-align: center !important;
            font-size: 14px !important;
            /*font-weight: bolder !important;*/
        }

        .el-select .el-input__inner {
            text-align: center !important;
            font-size: 14px !important;
            /*font-weight: bolder !important;*/
        }

        .el-select__tags-text {
            text-align: center !important;
            font-size: 14px !important;
            /*font-weight: bolder !important;*/
        }

        .el-dialog .el-dialog__body {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .tag-title {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            text-align: center;
            /*font-weight: bolder !important;*/
            font-size: 14px !important;
        }

        .tag-result {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            text-align: center;
            /*font-weight: bolder !important;*/
            font-size: 14px !important;
        }
    </style>
</head>
<body class="hold-transition">
<div id="app" v-loading.fullscreen.lock="fullscreenLoading" v-cloak>
    <!--标题-->
    <div class="content-header">
        <h1>质检指标维护</h1>
    </div>
    <!--内容-->
    <div class="app-container">
        <div class="box">
            <br>
            <!--条件查询-->
            <div>
                <el-row :gutter="20">
                    <el-col :span="4">
                        <el-input placeholder="集团编号"
                                  v-model="groupQuery.groupNumber"
                                  prefix-icon="el-icon-search"
                                  :style="{width: '100%'}"
                                  @change="getAll()"
                        ></el-input>
                    </el-col>
                    <el-col :span="4">
                        <el-input placeholder="集团名称"
                                  v-model="groupQuery.groupName"
                                  prefix-icon="el-icon-search"
                                  :style="{width: '100%'}"
                                  @change="getAll()"
                        ></el-input>
                    </el-col>
                    <el-col :span="4">
                        <el-input placeholder="公司名称"
                                  v-model="groupQuery.companyName"
                                  prefix-icon="el-icon-search"
                                  :style="{width: '100%'}"
                                  @change="getAll()"
                        ></el-input>
                    </el-col>
                    <el-col :span="6">
                        <el-tooltip content="集团查询"
                                    placement="top">
                            <el-button @click="getAll()"
                                       type="primary"
                                       size="small"
                                       icon="el-icon-search"
                                       circle>
                            </el-button>
                        </el-tooltip>
                        <el-tooltip content="查询刷新" placement="top">
                            <el-button @click="resetGroupSearch()"
                                       type="primary"
                                       size="small"
                                       icon="el-icon-refresh"
                                       circle>
                            </el-button>
                        </el-tooltip>
                        <el-tooltip content="新增集团"
                                    placement="top">
                            <el-button type="primary"
                                       @click="openGroupAddDialog()"
                                       size="small"
                                       circle
                                       icon="el-icon-plus"></el-button>
                        </el-tooltip>
                        <el-tooltip content="通用指标" placement="top">
                            <el-button @click="generalProductCheck()"
                                       type="warning"
                                       size="small"
                                       icon="el-icon-s-home"
                                       circle>
                            </el-button>
                        </el-tooltip>
                        <el-tooltip v-if="!user.workshopEmployee" content="操作日志" placement="top">
                            <el-button type="primary" class="butT" @click="openOperationLog()" size="small"
                                       icon="el-icon-time" circle>
                            </el-button>
                        </el-tooltip>
                    </el-col>
                </el-row>
            </div>
            <br>
            <!--分页表格-->
            <div>
                <el-table ref="multipleTable"
                          size="small"
                          current-row-key="id"
                          :data="groupList"
                          stripe
                          highlight-current-row>
                    <el-table-column type="index"
                                     align="center"
                                     label="序号"
                                     width="60"
                    ></el-table-column>
                    <el-table-column prop="groupNumber"
                                     label="集团编号"
                                     align="center">
                        <template slot-scope="scope">
                            <el-tag type="info"
                                    effect="dark">
                                {{scope.row.groupNumber}}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="groupName"
                                     label="集团名称"
                                     align="center">
                        <template slot-scope="scope">
                            <el-tooltip :content="scope.row.groupName" placement="top">
                                <el-tag type="primary"
                                        effect="dark">
                                    {{scope.row.groupName.slice(0, 15)}}
                                </el-tag>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                    <el-table-column prop="companyCount"
                                     label="公司数量"
                                     align="center">
                        <template slot-scope="scope">
                            <el-tag type="danger"
                                    effect="dark">
                                {{scope.row.companyCount}}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作"
                                     align="center">
                        <template slot-scope="scope">
                            <el-tooltip
                                    content="查看产品"
                                    placement="top">
                                <el-button type="primary"
                                           size="small"
                                           @click="groupProductCheck(scope.row)"
                                           icon="el-icon-search"
                                           circle
                                ></el-button>
                            </el-tooltip>
                            <el-tooltip
                                    content="查看公司"
                                    placement="top">
                                <el-button type="primary"
                                           size="small"
                                           @click="openCompanyVisible(scope.row)"
                                           icon="el-icon-office-building"
                                           circle
                                ></el-button>
                            </el-tooltip>
                            <el-tooltip
                                    content="集团删除"
                                    placement="top">
                                <el-button type="danger"
                                           size="small"
                                           @click="groupDelete(scope.row)"
                                           icon="el-icon-delete-solid"
                                           circle
                                ></el-button>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <br>
            <!--分页组件-->
            <div class="pagination-container">
                <el-pagination
                        class="pagiantion"
                        @current-change="handleCurrentChange"
                        :current-page="pagination.currentPage"
                        :page-size="pagination.pageSize"
                        layout="total, prev, pager, next, jumper"
                        :total="pagination.total">
                </el-pagination>
            </div>
            <!--新增集团-->
            <div>
                <el-dialog :visible.sync="groupAddVisible"
                           :title="groupAddTitle"
                           :append-to-body="true"
                           :lock-scroll="true"
                           :before-close="backToGroupList"
                           width="60%"
                           center>
                    <div style="width: 100%; display: flex; flex-direction: column; margin: 0 20px;">
                        <el-form ref="form" label-width="80px">
                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <el-form-item label="集团名称">
                                        <el-select v-model="groupAddQuery.groupNameList"
                                                   multiple
                                                   filterable
                                                   clearable
                                                   allow-create
                                                   default-first-option
                                                   :reserve-keyword="true"
                                                   :style="{width: '100%'}"
                                                   placeholder="请输入集团名称">
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                    </div>
                    <span slot="footer" class="dialog-footer">
                        <el-button type="primary" @click="groupAdd" size="small" circle
                                   icon="el-icon-check"></el-button>
                        <el-button type="danger" @click="backToGroupList" size="small" circle
                                   icon="el-icon-close"></el-button>
                    </span>
                </el-dialog>
            </div>
            <!--查看产品-->
            <div>
                <el-dialog :visible.sync="productVisible"
                           :title="title"
                           :append-to-body="true"
                           :lock-scroll="true"
                           :before-close="backToGroupList"
                           width="80%"
                           center>
                    <div style="width: 100%; display: flex; flex-direction: column; margin: 0 20px;">
                        <el-row :gutter="20">
                            <el-col :span="5">
                                <el-input placeholder="产品类别"
                                          v-model="productQuery.productCategory"
                                          prefix-icon="el-icon-search"
                                          :style="{width: '100%'}"
                                          @change="productCheck()"
                                ></el-input>
                            </el-col>
                            <el-col :span="5">
                                <el-input placeholder="产品名称"
                                          v-model="productQuery.productType"
                                          prefix-icon="el-icon-search"
                                          :style="{width: '100%'}"
                                          @change="productCheck()"
                                ></el-input>
                            </el-col>
                            <el-col :span="8">
                                <el-tooltip content="产品查询"
                                            placement="top">
                                    <el-button @click="productCheck()"
                                               type="primary"
                                               size="small"
                                               icon="el-icon-search"
                                               circle>
                                    </el-button>
                                </el-tooltip>
                                <el-tooltip content="查询刷新"
                                            placement="top">
                                    <el-button @click="resetProductSearch()"
                                               type="primary"
                                               size="small"
                                               icon="el-icon-refresh"
                                               circle>
                                    </el-button>
                                </el-tooltip>
                                <el-tooltip content="新增产品"
                                            placement="top">
                                    <el-button
                                            @click="openProductRequirementVisible(null, false, false, false, false, false, false, true, true, false, false)"
                                            type="primary"
                                            size="small"
                                            icon="el-icon-plus"
                                            circle>
                                    </el-button>
                                </el-tooltip>
                                <el-tooltip content="批量编辑"
                                            placement="top">
                                    <el-button
                                            @click="openProductRequirementVisible(null, false, false, false, true, false, false, false, false, false, false)"
                                            type="primary"
                                            size="small"
                                            icon="el-icon-edit"
                                            circle>
                                    </el-button>
                                </el-tooltip>
                                <el-tooltip content="指标复制(复制多个产品至现有集团)"
                                            placement="top">
                                    <el-button @click="openRequirementCopyDialog(null, true, true, false, false)"
                                               type="warning"
                                               size="small"
                                               icon="el-icon-document-copy"
                                               circle>
                                    </el-button>
                                </el-tooltip>
                                <el-tooltip content="指标复制(复制多个产品至新增集团)"
                                            placement="top">
                                    <el-button @click="openRequirementCopyDialog(null, true, true, true, false)"
                                               type="success"
                                               size="small"
                                               icon="el-icon-document-add"
                                               circle>
                                    </el-button>
                                </el-tooltip>
                            </el-col>
                        </el-row>
                        <br>
                        <el-table ref="multipleTable"
                                  size="small"
                                  current-row-key="id"
                                  :data="productList"
                                  max-height="500"
                                  stripe
                                  highlight-current-row>
                            <el-table-column type="index"
                                             align="center"
                                             label="序号"
                                             width="60"
                            ></el-table-column>
                            <el-table-column prop="productCategory"
                                             label="产品类别"
                                             align="center">
                                <template slot-scope="scope">
                                    <el-tag type="info"
                                            effect="dark">
                                        {{scope.row.productCategory}}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="productType"
                                             label="产品名称"
                                             min-width="75%"
                                             align="center">
                                <template slot-scope="scope">
                                    <el-tag type="primary"
                                            effect="dark">
                                        {{scope.row.productType ? scope.row.productType : "所有产品"}}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column v-if="groupRequirement"
                                             prop="categoryCount"
                                             label="类别数量"
                                             min-width="50%"
                                             align="center">
                                <template slot-scope="scope">
                                    <el-tag type="danger"
                                            effect="dark">
                                        {{scope.row.categoryCount ? scope.row.categoryCount : "暂无类别"}}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column v-if="groupRequirement"
                                             prop="requirementCategory"
                                             label="默认类别"
                                             min-width="75%"
                                             align="center">
                                <template slot-scope="scope">
                                    <el-tag type="warning"
                                            effect="dark">
                                        {{scope.row.requirementCategory}}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作"
                                             width="300px"
                                             align="center">
                                <template slot-scope="scope">
                                    <el-tooltip
                                            content="指标编辑"
                                            placement="top">
                                        <el-button type="primary"
                                                   size="small"
                                                   @click="openProductRequirementVisible(scope.row, false, false, true, false, false, false, false, false, false)"
                                                   icon="el-icon-edit"
                                                   circle
                                        ></el-button>
                                    </el-tooltip>
                                    <el-tooltip v-if="groupRequirement"
                                                content="指标类别"
                                                placement="top">
                                        <el-button type="primary"
                                                   size="small"
                                                   @click="openProductRequirementCategoryVisible(scope.row)"
                                                   icon="el-icon-notebook-2"
                                                   circle
                                        ></el-button>
                                    </el-tooltip>
                                    <el-tooltip content="指标复制(复制本产品至其他集团)"
                                                placement="top">
                                        <el-button
                                                @click="openProductRequirementVisible(scope.row, true, false, false, false, true, false, false, true, false)"
                                                type="warning"
                                                size="small"
                                                icon="el-icon-document-copy"
                                                circle>
                                        </el-button>
                                    </el-tooltip>
                                    <el-tooltip content="指标复制(复制本产品至本集团其他产品)"
                                                placement="top">
                                        <el-button
                                                @click="openProductRequirementVisible(scope.row, true, false, false, false, false, true, false, true, false)"
                                                type="success"
                                                size="small"
                                                icon="el-icon-document-add"
                                                circle>
                                        </el-button>
                                    </el-tooltip>
                                    <el-tooltip
                                            content="产品删除"
                                            placement="top">
                                        <el-button type="danger"
                                                   size="small"
                                                   @click="productDelete(scope.row)"
                                                   icon="el-icon-delete-solid"
                                                   circle
                                        ></el-button>
                                    </el-tooltip>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-dialog>
            </div>
            <!--指标复制（产品至集团）-->
            <div>
                <el-dialog :visible.sync="requirementCopyVisible"
                           :title="requirementCopyTitle"
                           :append-to-body="true"
                           :lock-scroll="true"
                           :before-close="backToProductList"
                           width="60%"
                           center>
                    <div style="width: 100%; display: flex; flex-direction: column; margin: 0 20px;">
                        <el-form ref="form" label-width="80px">
                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <el-form-item label="产品类型">
                                        <el-select v-model="requirementCopyQuery.product"
                                                   value-key="id"
                                                   multiple
                                                   filterable
                                                   clearable
                                                   :reserve-keyword="true"
                                                   :style="{width: '100%'}"
                                                   placeholder="请选择需要复制指标的产品">
                                            <el-option
                                                    v-for="(item, index) in requirementCopyProductOptions"
                                                    :key="item.id"
                                                    :label="item.productCategory"
                                                    :value="item">
                                                <span style="float: left">{{ item.productCategory }}</span>
                                                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.productType
                                                    }}</span>
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20" v-show="!creatGroup">
                                <el-col :span="24">
                                    <el-form-item label="集团名称">
                                        <el-select v-model="requirementCopyQuery.groupNumber"
                                                   multiple
                                                   filterable
                                                   clearable
                                                   :reserve-keyword="true"
                                                   :style="{width: '100%'}"
                                                   placeholder="请选择指标复制的目标集团">
                                            <el-option
                                                    v-for="(item, index) in requirementCopyGroupOptions"
                                                    :key="index"
                                                    :label="item.groupName"
                                                    :value="item.groupNumber">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20" v-show="creatGroup">
                                <el-col :span="24">
                                    <el-form-item label="集团名称">
                                        <el-select v-model="requirementCopyQuery.groupName"
                                                   multiple
                                                   filterable
                                                   clearable
                                                   allow-create
                                                   default-first-option
                                                   :reserve-keyword="true"
                                                   :style="{width: '100%'}"
                                                   placeholder="请输入指标复制的目标集团">
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                    </div>
                    <span slot="footer" class="dialog-footer">
                        <el-button type="primary" @click="requirementCopy" size="small" circle
                                   icon="el-icon-check"></el-button>
                        <el-button type="danger" @click="backToProductList" size="small" circle
                                   icon="el-icon-close"></el-button>
                    </span>
                </el-dialog>
            </div>
            <!--指标查看-->
            <div>
                <el-dialog :visible.sync="productRequirementVisible"
                           :title="productRequirementTitle"
                           :append-to-body="true"
                           :lock-scroll="true"
                           :before-close="backToProductList"
                           width="85%"
                           center>
                    <div style="width: 100%; display: flex; flex-direction: column; margin: 0 20px;">
                        <el-form ref="form" label-width="80px">
                            <el-row :gutter="20" v-show="isBatchEdit">
                                <el-col :span="24">
                                    <el-form-item label="目标产品">
                                        <el-select v-model="requirementBatchEditQuery.productList"
                                                   value-key="id"
                                                   multiple
                                                   filterable
                                                   clearable
                                                   :reserve-keyword="true"
                                                   :style="{width: '100%'}"
                                                   placeholder="请选择需要修改指标的产品">
                                            <el-option
                                                    v-for="(item, index) in requirementCopyProductOptions"
                                                    :key="item.id"
                                                    :label="item.productCategory"
                                                    :value="item">
                                                <span style="float: left">{{ item.productCategory }}</span>
                                                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.productType
                                                    }}</span>
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="6"
                                        v-show="!isBatchEdit && groupRequirement && !(creatProduct && !(copyToGroup || copyToProduct))">
                                    <el-form-item label="指标类别">
                                        <el-select v-model="productRequirementQuery.requirementCategory"
                                                   placeholder="请选择指标类别"
                                                   filterable
                                                   size="medium"
                                                   @change="getProductRequirement"
                                                   :style="{width: '100%'}">
                                            <el-option
                                                    v-for="item in productRequirementCategoryOptions"
                                                    :key="item.value"
                                                    :label="item.label"
                                                    :value="item.value">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6"
                                        v-show="isCategoryCreat">
                                    <el-form-item label="类别名称">
                                        <el-input placeholder="请输入待创建的类别名称"
                                                  clearable
                                                  size="small"
                                                  v-model="productCategoryQuery.productCategory"
                                                  :style="{width: '100%'}"
                                        ></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="9" v-show="copyToGroup">
                                    <el-form-item label="目标集团">
                                        <el-select v-model="requirementCopyQuery.groupNumber"
                                                   multiple
                                                   filterable
                                                   clearable
                                                   :reserve-keyword="true"
                                                   :style="{width: '100%'}"
                                                   placeholder="请选择指标复制的目标集团">
                                            <el-option
                                                    v-for="(item, index) in requirementCopyGroupOptions"
                                                    :key="index"
                                                    :label="item.groupName"
                                                    :value="item.groupNumber">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="groupRequirement ? (copyToGroup ? 9 : 18 ) : (copyToGroup ? 15 : 24 )"
                                        v-show="copyToGroup || copyToProduct">
                                    <el-form-item label="目标产品">
                                        <el-cascader
                                            v-model="selectedMaterialCascade"
                                            :options="materialCascadeOptions"
                                            :props="cascaderProps"
                                            @change="handleMaterialCascadeChange"
                                            clearable
                                            filterable
                                            multiple
                                            collapse-tags
                                            :style="{width: '100%'}"
                                            placeholder="请选择指标复制的目标产品">
                                            <template slot-scope="{ node, data }">
                                                <span>{{ data.id && data.id.startsWith('category_') ? data.categoryName : (data.specification ? data.materialName + '-' + data.specification : data.materialName) }}</span>
                                            </template>
                                        </el-cascader>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="24"
                                        v-show="creatProduct && !(copyToGroup || copyToProduct)">
                                    <el-form-item label="产品类型">
                                        <el-cascader
                                            v-model="selectedMaterialCascade"
                                            :options="materialCascadeOptions"
                                            :props="cascaderProps"
                                            @change="handleMaterialCascadeChange"
                                            clearable
                                            filterable
                                            multiple
                                            collapse-tags
                                            :style="{width: '100%'}"
                                            placeholder="请选择新增指标产品">
                                            <template slot-scope="{ node, data }">
                                                <span>{{ data.id && data.id.startsWith('category_') ? data.categoryName : (data.specification ? data.materialName + '-' + data.specification : data.materialName) }}</span>
                                            </template>
                                        </el-cascader>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                        <el-table ref="multipleTable"
                                  size="small"
                                  current-row-key="id"
                                  :data="requirementList"
                                  max-height="500"
                                  stripe
                                  highlight-current-row>
                            <el-table-column type="index"
                                             align="center"
                                             label="序号"
                                             width="60"
                            ></el-table-column>
                            <el-table-column prop="qualityInspectionItem"
                                             label="检测项目"
                                             align="center">
                                <template slot-scope="scope">
                                    <el-select v-model="scope.row.qualityInspectionItem"
                                               placeholder="请选择检测项目"
                                               filterable
                                               size="small"
                                               @change="copyToItemName(scope.row)"
                                               :style="{width: '100%'}">
                                        <el-option
                                                v-for="item in inspectionItemOptions"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value">
                                        </el-option>
                                    </el-select>
                                </template>
                            </el-table-column>
                            <el-table-column v-if="groupRequirement" prop="itemName"
                                             label="客户别名"
                                             align="center">
                                <template slot-scope="scope">
                                    <el-input class="commonInput"
                                              v-model.trim="scope.row.itemName"
                                              placeholder=""
                                              clearable
                                              size="small"
                                              :style="{width: '100%'}">
                                    </el-input>
                                </template>
                            </el-table-column>
                            <el-table-column prop="qualityRequirement"
                                             :label="groupRequirement ? '客户指标' : '通用指标'"
                                             align="center">
                                <template slot-scope="scope">
                                    <el-input class="commonInput"
                                              v-model.trim="scope.row.qualityRequirement"
                                              placeholder=""
                                              clearable
                                              size="small"
                                              :style="{width: '100%'}">
                                    </el-input>
                                </template>
                            </el-table-column>
                            <el-table-column v-if="groupRequirement" prop="internalControlRequirement"
                                             label="内控指标"
                                             align="center">
                                <template slot-scope="scope">
                                    <el-input class="commonInput"
                                              v-model.trim="scope.row.internalControlRequirement"
                                              placeholder=""
                                              clearable
                                              size="small"
                                              :style="{width: '100%'}">
                                    </el-input>
                                </template>
                            </el-table-column>
                            <el-table-column prop="showStatus"
                                             label="显示状态"
                                             align="center"
                                             min-width="50%">
                                <template slot-scope="scope">
                                    <el-switch
                                            v-model="scope.row.showStatus"
                                            active-color="#13ce66"
                                            inactive-color="#ff4949">
                                    </el-switch>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作"
                                             align="center"
                                             min-width="50%">
                                <template slot-scope="scope">
                                    <el-tooltip
                                            content="项目删除"
                                            placement="top">
                                        <el-button type="danger"
                                                   size="small"
                                                   @click="requirementItemDelete(scope.row)"
                                                   icon="el-icon-delete-solid"
                                                   circle
                                        ></el-button>
                                    </el-tooltip>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                    <span slot="footer" class="dialog-footer">
                        <el-tooltip content="添加"
                                    placement="top">
                            <el-button type="primary" @click="requirementItemAdd" size="small" circle
                                       icon="el-icon-plus"></el-button>
                        </el-tooltip>
                        <el-tooltip content="确认"
                                    placement="top">
                            <el-button type="primary" @click="requirementItemHandle" size="small" circle
                                       icon="el-icon-check"></el-button>
                        </el-tooltip>
                        <el-tooltip content="取消"
                                    placement="top">
                            <el-button type="danger" @click="backToProductList" size="small" circle
                                       icon="el-icon-close"></el-button>
                        </el-tooltip>
                    </span>
                </el-dialog>
            </div>
            <!--指标类别查看-->
            <div>
                <el-dialog :visible.sync="productRequirementCategoryVisible"
                           :title="productRequirementCategoryTitle"
                           :append-to-body="true"
                           :lock-scroll="true"
                           :before-close="backToProductList"
                           width="85%"
                           center>
                    <div style="width: 100%; display: flex; flex-direction: column; margin: 0 20px;">
                        <el-table ref="multipleTable"
                                  size="small"
                                  current-row-key="id"
                                  :data="requirementCategoryList"
                                  max-height="500"
                                  stripe
                                  highlight-current-row>
                            <el-table-column type="index"
                                             align="center"
                                             label="序号"
                                             width="60"
                            ></el-table-column>
                            <el-table-column prop="newRequirementCategory"
                                             label="类别名称"
                                             align="center">
                                <template slot-scope="scope">
                                    <el-input class="commonInput"
                                              v-model.trim="scope.row.newRequirementCategory"
                                              placeholder=""
                                              clearable
                                              size="small"
                                              :style="{width: '80%'}">
                                    </el-input>
                                </template>
                            </el-table-column>
                            <el-table-column prop="defaultStatus"
                                             label="关联公司"
                                             align="center"
                                             min-width="50%">
                                <template slot-scope="scope">
                                    <template v-if="scope.row.companyList.length === 0">
                                        <el-tag effect="dark" type="info" style="margin-right: 5px">
                                            暂无关联公司
                                        </el-tag>
                                    </template>
                                    <template v-else-if="scope.row.companyList.length === 1">
                                        <el-tooltip v-for="(item, index) in scope.row.companyList" :key="index"
                                                    :content="item.companyName"
                                                    placement="top">
                                            <el-tag effect="dark" style="margin-right: 5px">
                                                {{ item.companyName.slice(0, 6) }}
                                            </el-tag>
                                        </el-tooltip>
                                    </template>
                                    <template v-else="scope.row.companyList.length > 1">
                                        <el-tooltip v-for="(item, index) in scope.row.companyList.slice(0, 1)"
                                                    :key="index"
                                                    :content="item.companyName" placement="top">
                                            <el-tag effect="dark" style="margin-right: 5px">
                                                {{ item.companyName.slice(0, 6) }}
                                            </el-tag>
                                        </el-tooltip>
                                        <el-tooltip
                                                :content="scope.row.companyList.slice(1).map(app => app.companyName).join(',')"
                                                placement="top">
                                            <el-tag effect="plain" style="margin-right: 5px">
                                                +{{ scope.row.companyList.length - 1 }}
                                            </el-tag>
                                        </el-tooltip>
                                    </template>
                                </template>
                            </el-table-column>
                            <el-table-column prop="defaultStatus"
                                             label="默认指标"
                                             align="center"
                                             min-width="50%">
                                <template slot-scope="scope">
                                    <el-switch
                                            v-model="scope.row.newDefaultStatus"
                                            :disabled="scope.row.newDefaultStatus"
                                            @change="defaultStatusChange(scope.row)"
                                            active-color="#13ce66"
                                            inactive-color="#ff4949">
                                    </el-switch>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作"
                                             align="center"
                                             min-width="50%">
                                <template slot-scope="scope">
                                    <el-tooltip
                                            content="编辑关联公司"
                                            placement="top">
                                        <el-button type="primary"
                                                   size="small"
                                                   icon="el-icon-edit"
                                                   @click="openLinkCompanyVisible(scope.row)"
                                                   circle
                                        ></el-button>
                                    </el-tooltip>
                                    <el-tooltip
                                            content="类别删除"
                                            placement="top">
                                        <el-button type="danger"
                                                   size="small"
                                                   @click="categoryDelete(scope.row)"
                                                   icon="el-icon-delete-solid"
                                                   circle
                                        ></el-button>
                                    </el-tooltip>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                    <span slot="footer" class="dialog-footer">
                        <el-tooltip content="类别添加"
                                    placement="top">
                            <el-button type="primary"
                                       @click="openProductRequirementVisible(null, false, false, false, false, false, false, false, false, true)"
                                       size="small" circle
                                       icon="el-icon-plus"></el-button>
                        </el-tooltip>
                        <el-tooltip content="确认修改"
                                    placement="top">
                            <el-button type="primary" @click="categoryEdit" size="small" circle
                                       icon="el-icon-check"></el-button>
                        </el-tooltip>
                        <el-tooltip content="关闭"
                                    placement="top">
                            <el-button type="danger" @click="backToProductList" size="small" circle
                                       icon="el-icon-close"></el-button>
                        </el-tooltip>
                    </span>
                </el-dialog>
            </div>
            <!--查看公司-->
            <div>
                <el-dialog :visible.sync="companyVisible"
                           :title="title"
                           :append-to-body="true"
                           :lock-scroll="true"
                           :before-close="backToGroupList"
                           width="80%"
                           center>
                    <div style="width: 100%; display: flex; flex-direction: column; margin: 0 20px;">
                        <el-table ref="multipleTable"
                                  size="small"
                                  current-row-key="id"
                                  :data="companyList"
                                  max-height="500"
                                  stripe
                                  highlight-current-row>
                            <el-table-column type="index"
                                             align="center"
                                             label="序号"
                                             width="60"
                            ></el-table-column>
                            <el-table-column prop="companyNumber"
                                             label="公司编号"
                                             align="center">
                                <template slot-scope="scope">
                                    <el-tag type="info"
                                            effect="dark">
                                        {{scope.row.companyNumber}}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="companyName"
                                             label="公司名称"
                                             align="center">
                                <template slot-scope="scope">
                                    <el-tooltip :content="scope.row.companyName" placement="top">
                                        <el-tag effect="dark">{{ scope.row.companyName.slice(0, 15) }}</el-tag>
                                    </el-tooltip>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作"
                                             align="center"
                                             min-width="50%">
                                <template slot-scope="scope">
                                    <el-tooltip
                                            content="公司删除"
                                            placement="top">
                                        <el-button type="danger"
                                                   size="small"
                                                   @click="companyDelete(scope.row)"
                                                   icon="el-icon-delete-solid"
                                                   circle
                                        ></el-button>
                                    </el-tooltip>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                    <span slot="footer" class="dialog-footer">
                        <el-button type="primary" @click="openCompanyAddVisible" size="small" circle
                                   icon="el-icon-plus"></el-button>
                        <el-button type="danger" @click="backToGroupList" size="small" circle
                                   icon="el-icon-close"></el-button>
                    </span>
                </el-dialog>
            </div>
            <!--添加公司-->
            <div>
                <el-dialog :visible.sync="companyAddVisible"
                           :title="companyAddTitle"
                           :append-to-body="true"
                           :lock-scroll="true"
                           :before-close="backToCompanyList"
                           width="40%"
                           center>
                    <div style="width: 100%; display: flex; flex-direction: column; margin: 0 20px;">
                        <el-form ref="form" label-width="80px">
                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <el-form-item label="公司名称">
                                        <el-select v-model="companyAddQuery.companyNumberList"
                                                   multiple
                                                   filterable
                                                   remote
                                                   clearable
                                                   :reserve-keyword="true"
                                                   :style="{width: '100%'}"
                                                   :loading="getCompanyOptionsLoading"
                                                   :remote-method="getCompanyOptions"
                                                   placeholder="请搜索并选择需要添加至集团的公司">
                                            <el-option
                                                    v-for="item in companyAddOptions"
                                                    :key="item.value"
                                                    :label="item.label"
                                                    :value="item.value">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                    </div>
                    <span slot="footer" class="dialog-footer">
                        <el-button type="primary" @click="companyAdd" size="small" circle
                                   icon="el-icon-check"></el-button>
                        <el-button type="danger" @click="backToCompanyList" size="small" circle
                                   icon="el-icon-close"></el-button>
                    </span>
                </el-dialog>
            </div>
            <!--查看关联公司-->
            <div>
                <el-dialog :visible.sync="linkCompanyVisible"
                           :title="linkCompanyTitle"
                           :append-to-body="true"
                           :lock-scroll="true"
                           :before-close="backToProductRequirementCategoryList"
                           width="80%"
                           center>
                    <div style="width: 100%; display: flex; flex-direction: column; margin: 0 20px;">
                        <el-table ref="multipleTable"
                                  size="small"
                                  current-row-key="id"
                                  :data="linkCompanyList"
                                  max-height="500"
                                  stripe
                                  highlight-current-row>
                            <el-table-column type="index"
                                             align="center"
                                             label="序号"
                                             width="60"
                            ></el-table-column>
                            <el-table-column prop="companyNumber"
                                             label="公司编号"
                                             align="center">
                                <template slot-scope="scope">
                                    <el-tag type="info"
                                            effect="dark">
                                        {{scope.row.companyNumber}}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="companyName"
                                             label="公司名称"
                                             align="center">
                                <template slot-scope="scope">
                                    <el-tooltip :content="scope.row.companyName" placement="top">
                                        <el-tag effect="dark">{{ scope.row.companyName.slice(0, 15) }}</el-tag>
                                    </el-tooltip>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作"
                                             align="center"
                                             min-width="50%">
                                <template slot-scope="scope">
                                    <el-tooltip
                                            content="公司删除"
                                            placement="top">
                                        <el-button type="danger"
                                                   size="small"
                                                   @click="linkCompanyDelete(scope.row)"
                                                   icon="el-icon-delete-solid"
                                                   circle
                                        ></el-button>
                                    </el-tooltip>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                    <span slot="footer" class="dialog-footer">
                        <el-button type="primary" @click="openLinkCompanyAddVisible" size="small" circle
                                   icon="el-icon-plus"></el-button>
                        <el-button type="danger" @click="backToProductRequirementCategoryList" size="small" circle
                                   icon="el-icon-close"></el-button>
                    </span>
                </el-dialog>
            </div>
            <!--添加关联公司-->
            <div>
                <el-dialog :visible.sync="linkCompanyAddVisible"
                           :title="linkCompanyAddTitle"
                           :append-to-body="true"
                           :lock-scroll="true"
                           :before-close="backToLinkCompanyList"
                           width="40%"
                           center>
                    <div style="width: 100%; display: flex; flex-direction: column; margin: 0 20px;">
                        <el-form ref="form" label-width="80px">
                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <el-form-item label="公司名称">
                                        <el-select v-model="linkCompanyAddQuery.companyNumberList"
                                                   multiple
                                                   filterable
                                                   remote
                                                   clearable
                                                   :reserve-keyword="true"
                                                   :style="{width: '100%'}"
                                                   :loading="getLinkCompanyOptionsLoading"
                                                   :remote-method="getLinkCompanyOptions"
                                                   placeholder="请搜索并选择需要关联至本类别的公司">
                                            <el-option
                                                    v-for="item in linkCompanyAddOptions"
                                                    :key="item.value"
                                                    :label="item.label"
                                                    :value="item.value">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                    </div>
                    <span slot="footer" class="dialog-footer">
                        <el-button type="primary" @click="linkCompanyAdd" size="small" circle
                                   icon="el-icon-check"></el-button>
                        <el-button type="danger" @click="backToLinkCompanyList" size="small" circle
                                   icon="el-icon-close"></el-button>
                    </span>
                </el-dialog>
            </div>
            <!--查看操作日志-->
            <div>
                <el-dialog :visible.sync="operationLogVisible"
                           title="操作日志"
                           :append-to-body="true"
                           :lock-scroll="true"
                           :before-close="backToRecordList"
                           width="90%"
                           center>
                    <div style="width: 100%; display: flex; flex-direction: column; margin: 0 20px;">
                        <el-row :gutter="20">
                            <el-col :span="4">
                                <el-input placeholder="集团编号" v-model="logPagination.groupNumber"
                                          :style="{width: '100%'}"
                                          @change="getOperationLog()"></el-input>
                            </el-col>
                            <el-col :span="4">
                                <el-input placeholder="集团名称" v-model="logPagination.groupName"
                                          :style="{width: '100%'}"
                                          @change="getOperationLog()"></el-input>
                            </el-col>
                            <el-col :span="4">
                                <el-input placeholder="公司编号" v-model="logPagination.companyNumber"
                                          :style="{width: '100%'}"
                                          @change="getOperationLog()"></el-input>
                            </el-col>
                            <el-col :span="4">
                                <el-input placeholder="公司名称" v-model="logPagination.companyName"
                                          :style="{width: '100%'}"
                                          @change="getOperationLog()"></el-input>
                            </el-col>
                            <el-col :span="6">
                                <el-date-picker
                                        v-model="logPagination.operationDate"
                                        type="daterange"
                                        :unlink-panels="true"
                                        range-separator="至"
                                        start-placeholder="起始日期"
                                        end-placeholder="截止日期"
                                        value-format="yyyy-MM-dd"
                                        @change="getOperationLog()"
                                        :style="{width: '100%'}">
                                </el-date-picker>
                            </el-col>
                            <el-col :span="2">
                                <el-tooltip content="日志查询" placement="top">
                                    <el-button @click="getOperationLog()" type="primary" size="small"
                                               icon="el-icon-search" circle>
                                    </el-button>
                                </el-tooltip>
                                <el-tooltip content="页面刷新" placement="top">
                                    <el-button @click="resetLogSearch()" type="primary" size="small"
                                               icon="el-icon-refresh"
                                               circle>
                                    </el-button>
                                </el-tooltip>
                            </el-col>
                        </el-row>
                        <br>
                        <el-table ref="multipleTable"
                                  size="small"
                                  current-row-key="id"
                                  :data="logList"
                                  stripe
                                  highlight-current-row>
                            <el-table-column type="index"
                                             align="center"
                                             label="序号"
                                             width="60"
                            ></el-table-column>
                            <el-table-column prop="groupNumber" label="集团编号" align="center" min-width="125">
                                <template slot-scope="scope">
                                    <el-tag effect="dark" type="info">{{scope.row.groupNumber}}</el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="groupName" label="集团名称" align="center" min-width="100%">
                                <template slot-scope="scope">
                                    <el-tooltip :content="scope.row.groupName" placement="top">
                                        <el-tag effect="dark">{{scope.row.groupName.slice(0, 8)}}</el-tag>
                                    </el-tooltip>
                                </template>
                            </el-table-column>
                            <el-table-column prop="companyNumber" label="公司编号" align="center" min-width="125">
                                <template slot-scope="scope">
                                    <el-tag effect="dark" type="info">{{scope.row.companyNumber}}</el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="companyName" label="公司名称" align="center" min-width="100%">
                                <template slot-scope="scope">
                                    <el-tooltip :content="scope.row.companyName" placement="top">
                                        <el-tag effect="dark">{{scope.row.companyName.slice(0, 8)}}</el-tag>
                                    </el-tooltip>
                                </template>
                            </el-table-column>
                            <el-table-column prop="shift" label="操作类型" align="center" width="120">
                                <template slot-scope="scope">
                                    <el-tag v-if="scope.row.operationType === '添加'"
                                            effect="dark" type="success">{{scope.row.operationType}}
                                    </el-tag>
                                    <el-tag v-if="scope.row.operationType === '删除'"
                                            effect="dark" type="danger">{{scope.row.operationType}}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="shift" label="操作人" align="center" width="120">
                                <template slot-scope="scope">
                                    <el-tag effect="dark" type="success">{{scope.row.operator}}</el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="shift" label="操作时间" align="center" width="220">
                                <template slot-scope="scope">
                                    <el-tag effect="dark" type="danger">{{scope.row.operationTime}}</el-tag>
                                </template>
                            </el-table-column>
                        </el-table>
                        <!--分页组件-->
                        <div class="pagination-container">
                            <el-pagination
                                    class="pagiantion"
                                    @current-change="handleLogCurrentChange"
                                    :current-page="logPagination.currentPage"
                                    :page-size="logPagination.pageSize"
                                    layout="total, prev, pager, next, jumper"
                                    :total="logPagination.total">
                            </el-pagination>
                        </div>
                    </div>
                </el-dialog>
            </div>
        </div>
    </div>
</div>
</body>
<!-- 引入组件库 -->
<script src="../../js/vue.js"></script>
<script src="../../element-ui/lib/index.js"></script>
<link rel="stylesheet" href="../../element-ui/lib/theme-chalk/index.css">
<script type="text/javascript" src="../../js/jquery.min.js"></script>
<script src="../../js/axios.min.js"></script>
<script>
    let vue = new Vue({
        el: '#app',
        data: {
            /*基本信息*/
            param: "", // url参数
            user: "", // 当前用户
            /*加载*/
            fullscreenLoading: true, // 全屏加载
            getCompanyOptionsLoading: true, // 公司查询加载
            getLinkCompanyOptionsLoading: true, // 公司查询加载
            /*弹窗可见性*/
            groupAddVisible: false, // 新增集团弹窗可见性
            productVisible: false, // 客户产品产品弹窗可见性
            requirementCopyVisible: false, // 指标复制（按产品）弹窗可见性
            productRequirementVisible: false, // 产品指标弹窗可见性
            productRequirementCategoryVisible: false, // 产品指标类别可见性
            productRequirementCopyVisible: false, // 产品指标复制弹窗可见性
            companyVisible: false, // 产品指标复制弹窗可见性
            companyAddVisible: false, // 产品指标复制弹窗可见性
            linkCompanyVisible: false, // 关联公司弹窗
            linkCompanyAddVisible: false, // 关联公司添加弹窗
            operationLogVisible: false,
            /*查询相关*/
            pagination: { // 分页信息
                currentPage: 1, // 当前页码
                pageSize: 10, // 页面大小
                total: 0, // 总分页数
            },
            groupQuery: { // 集团查询
                groupNumber: "", // 集团编号
                groupName: "", // 集团名称
                companyName: "" // 公司名称
            },
            productQuery: { // 产品查询
                groupNumber: "", // 集团编号
                groupName: "", // 集团名称
                productCategory: "", // 产品类别
                productType: "" // 产品名称
            },
            requirementCopyQuery: { // 指标复制查询（DTO）
                product: [], // 产品信息集合
                groupNumber: [], // 集团编号集合
                groupName: [], // 集团名称集合
                awaitingCopyProduct: ""
            },
            groupAddQuery: {
                groupNameList: []
            },
            companyAddQuery: {
                companyNumberList: [],
                groupNumber: ""
            },
            linkCompanyAddQuery: {
                companyNumberList: [],
                groupNumber: ""
            },
            productRequirementQuery: {
                requirementProductDTO: "",
                group: "",
                requirementCategory: ""
            },
            requirementBatchEditQuery: {
                productList: [],
            },
            productCategoryQuery: {
                productCategory: ""
            },
            logPagination: {
                currentPage: 1,//当前页码
                pageSize: 10,//每页显示的记录数
                total: 0,//总记录数
                groupNumber: "",
                groupName: "",
                companyNumber: "",
                companyName: "",
                operationDate: ["", ""],
                operator: "",
            },
            /*数据集合*/
            groupList: [], // 集团信息集合
            productList: [], // 产品信息集合
            requirementList: [], // 指标信息集合
            deletedRequirementList: [], // 被删除的指标信息集合
            requirementCategoryList: [], // 指标类别信息集合
            companyList: [], // 公司信息集合
            materialInformationDTOList: [], // 物料信息集合
            logList: [],
            linkCompanyList: [],
            /*下拉框选项*/
            requirementCopyProductOptions: [], // 指标复制产品选项集合
            requirementCopyGroupOptions: [], // 指标复制集团选项集合
            companyAddOptions: [], // 指标复制集团选项集合
            inspectionItemOptions: [], // 指标复制集团选项集合
            productRequirementCategoryOptions: [], // 产品指标类别选项集合
            materialInformationOptions: [], // 物料信息选项集合
            linkCompanyAddOptions: [], // 关联公司添加选项集合
            /*标题*/
            title: "", // 产品列表标题
            requirementCopyTitle: "", // 指标复制标题（按产品）
            companyAddTitle: "", // 公司添加标题
            productRequirementCategoryTitle: "", // 产品指标类别标题
            productRequirementTitle: "", // 产品指标标题
            groupAddTitle: "", // 新增集团标题
            linkCompanyTitle: "", // 关联公司标题
            linkCompanyAddTitle: "", // 关联公司添加标题
            /*功能相关条件判断*/
            creatGroup: false, // 是否创建新集团
            copyToGroup: false, // 是否复制至集团
            copyToProduct: false, // 是否复制至产品
            groupRequirement: false, // 是否为集团指标
            isRequirementCopy: false, // 是否为指标复制
            isRequirementEdit: false, // 是否为指标修改
            isBatchEdit: false, // 是否为批量修改
            creatProduct: false, // 是否为新建产品
            isRequirementProductCopy: false, // 是否为产品复制至产品
            isCategoryCreat: false,
            
            /*级联选择器相关*/
            selectedMaterialCascade: [], // 级联选择器选中值
            materialCascadeOptions: [], // 级联选择器选项
            cascaderProps: {
                multiple: true,
                checkStrictly: true, // 允许选择任意级别
                expandTrigger: 'click', // 点击时展开
                value: 'id', // 选项的值为id字段
                label: 'name', // 选项的标签为name字段
                children: 'childrenDTOList', // 子选项字段名称
                emitPath: false // 仅返回选中节点值，不返回路径
            },
            /*当前开启状态*/
            openedCompanyVisibleRow: "",
            openedRequirementCategoryRow: "", // 当前正在查看指标类别的行（产品信息）
            openedLinkCompanyVisibleRow: "", // 当前正在查看关联公司的行

        },

        created() {
            let url = decodeURI(location.search).slice(1);
            let obj = {};
            let paramsArr = url.split('&')
            for (let i = 0, len = paramsArr.length; i < len; i++) {
                let arr = paramsArr[i].split('=')
                obj[arr[0]] = arr[1];
            }
            this.param = {
                id: obj.uid,
                password: obj.pwd
            };
            axios.post("/user/verification", this.param).then((res) => {
                if (res.data.flag) {
                    this.user = res.data.data;
                    this.getAll();
                }
            });

        },

        methods: {
            // 分页查询
            getAll() {
                this.fullscreenLoading = true;
                let param = "?groupNumber=" + this.groupQuery.groupNumber;
                param += "&groupName=" + this.groupQuery.groupName;
                param += "&companyName=" + this.groupQuery.companyName;
                axios.get("/requirementMaintenance/" + this.pagination.currentPage + "/" + this.pagination.pageSize + param).then((res) => {
                    if (res.data.flag) {
                        this.pagination.currentPage = res.data.data.current;
                        this.pagination.pageSize = res.data.data.size;
                        this.pagination.total = res.data.data.total;
                        this.groupList = res.data.data.records;
                        this.fullscreenLoading = false;
                    } else {
                        this.$message.error("集团信息获取异常");
                    }
                })
            },

            // 切换页码
            handleCurrentChange(currentPage) {
                this.pagination.currentPage = currentPage;
                this.getAll();
            },

            // 重置集团查询
            resetGroupSearch() {
                this.groupQuery.groupNumber = "";
                this.groupQuery.groupName = "";
                this.groupQuery.companyName = "";
                this.getAll();
            },

            // 通用产品查询
            generalProductCheck() {
                this.groupRequirement = false;
                this.productCheck();
            },

            // 集团产品查询
            groupProductCheck(row) {
                this.groupRequirement = true;
                this.productQuery.groupNumber = row.groupNumber;
                this.productQuery.groupName = row.groupName;
                this.productCheck();
            },

            // 产品查询
            productCheck() {
                let param = "";
                if (this.groupRequirement) {
                    param = "?groupNumber=" + this.productQuery.groupNumber;
                    param += "&group=true";
                } else {
                    param += "?group=false";
                }
                param += "&productCategory=" + this.productQuery.productCategory;
                param += "&productType=" + this.productQuery.productType;
                axios.get("/requirementMaintenance/getProductList" + param).then((res) => {
                    if (res.data.flag) {
                        this.productList = res.data.data;
                        this.requirementCopyProductOptions = this.productList.map(item => {
                            return {
                                id: item.id,
                                groupNumber: item.groupNumber,
                                productCategory: item.productCategory,
                                productCategoryNumber: item.productCategoryNumber,
                                productType: item.productType ? item.productType : "所有产品",
                                productTypeNumber: item.productTypeNumber,
                            };
                        })
                        if (this.groupRequirement) {
                            this.title = this.productQuery.groupName + "客户指标产品";
                        } else {
                            this.title = "通用指标产品";
                        }
                        this.productVisible = true;
                    } else {
                        this.$message.error("产品获取失败")
                    }
                })
            },

            // 重置产品查询
            resetProductSearch() {
                this.productQuery.productCategory = "";
                this.productQuery.productType = "";
                this.productCheck();
            },

            // 返回集团列表
            backToGroupList() {
                this.productVisible = false;
                this.companyVisible = false;
                this.groupAddVisible = false;
                this.productQuery.productCategory = "";
                this.productQuery.productType = "";
                this.getAll();
                this.$message.info("返回集团列表")
            },

            // 返回产品列表
            backToProductList() {
                this.requirementCopyVisible = false;
                console.log(this.productRequirementCategoryVisible);
                if (!this.productRequirementVisible) {
                    this.productRequirementCategoryVisible = false;
                }
                this.productRequirementVisible = false;
                this.requirementCopyQuery.product = [];
                this.requirementCopyQuery.groupNumber = [];
                this.requirementCopyQuery.groupName = [];
                this.$message.info("返回产品列表")
            },

            /*
            * 打开产品指标复制弹窗
            *
            * isRequirementCopy： 是否为指标复制
            * copyToGroup： 是否复制至集团
            * creatGroup： 是否新建集团
            * isRequirementEdit： 是否新建集团
            * */
            openRequirementCopyDialog(row, isRequirementCopy, copyToGroup, creatGroup, isRequirementEdit) {
                this.isRequirementCopy = isRequirementCopy;
                this.copyToGroup = copyToGroup;
                this.creatGroup = creatGroup;
                this.requirementCopyQuery.awaitingCopyProduct = row;
                this.isRequirementEdit = isRequirementEdit;
                axios.get("/requirementMaintenance/getGroupList").then((res) => {
                    if (res.data.flag) {
                        this.requirementCopyGroupOptions = res.data.data.map(item => {
                            return {
                                groupNumber: item.groupNumber,
                                groupName: item.groupName,
                            };
                        })
                        if (this.groupRequirement) {
                            this.requirementCopyTitle = this.productQuery.groupName + "客户指标复制";
                        } else {
                            this.requirementCopyTitle = "通用指标复制";
                        }
                        this.requirementCopyQuery.product = [];
                        this.requirementCopyQuery.groupNumber = [];
                        this.requirementCopyQuery.groupName = [];
                        this.requirementCopyVisible = true;
                    } else {
                        this.$message.error("集团获取失败")
                    }
                })
            },

            // 指标复制(按产品)
            requirementCopy() {
                this.fullscreenLoading = true;
                let requirementItemHandleDTO = {};
                requirementItemHandleDTO.groupRequirement = this.groupRequirement;
                requirementItemHandleDTO.isRequirementCopy = this.isRequirementCopy;
                requirementItemHandleDTO.copyToGroup = this.copyToGroup;
                requirementItemHandleDTO.creatGroup = this.creatGroup;
                requirementItemHandleDTO.isRequirementEdit = this.isRequirementEdit;
                requirementItemHandleDTO.user = this.user; // 添加 user 属性

                requirementItemHandleDTO.requirementProductDTO = this.requirementCopyQuery.awaitingCopyProduct;
                requirementItemHandleDTO.groupNmberList = this.requirementCopyQuery.groupNumber;
                requirementItemHandleDTO.groupNameList = this.requirementCopyQuery.groupName;
                requirementItemHandleDTO.requirementProductDTOList = this.requirementCopyQuery.product;

                axios.post("/requirementMaintenance/requirementItemHandle", requirementItemHandleDTO).then((res) => {
                    if (res.data.flag) {
                        this.fullscreenLoading = false;
                        this.requirementCopyVisible = false;
                        this.productCheck();
                        this.$message.success("指标复制成功");
                    } else {
                        this.fullscreenLoading = false;
                        if (this.creatGroup) {
                            this.$message.error("指标复制失败,请检查集团名称是否重复");
                        } else {
                            this.$message.error("指标复制失败");
                        }
                    }
                });
            },

            /*
            * 打开产品指标弹窗
            *
            * row： 当前行
            * isRequirementCopy： 是否为指标复制
            * creatGroup： 是否新建集团
            *
            * isRequirementEdit： 是否为指标编辑
            * isBatchEdit： 是否为批量编辑
            * copyToGroup： 复制至集团
            * copyToProduct： 复制至产品
            * creatProduct： 新增产品
            * isRequirementProductCopy： 是否为指标复制（产品至产品）
            * isCategoryCreat： 是否为新建类别
            * */
            openProductRequirementVisible(row, isRequirementCopy, creatGroup, isRequirementEdit, isBatchEdit, copyToGroup, copyToProduct, creatProduct, isRequirementProductCopy, isCategoryCreat) {
                this.isRequirementCopy = isRequirementCopy;
                this.creatGroup = creatGroup;
                this.isRequirementEdit = isRequirementEdit;
                this.isBatchEdit = isBatchEdit;
                this.copyToGroup = copyToGroup;
                this.copyToProduct = copyToProduct;
                this.creatProduct = creatProduct;
                this.isRequirementProductCopy = isRequirementProductCopy;
                this.isCategoryCreat = isCategoryCreat;

                this.productRequirementQuery = {
                    requirementProductDTO: "",
                    group: "",
                    requirementCategory: ""
                };
                this.productCategoryQuery = {
                    productCategory: ""
                };
                this.productRequirementQuery.group = this.groupRequirement;
                if (!isBatchEdit && !creatProduct && !isCategoryCreat) {
                    this.productRequirementQuery.requirementProductDTO = row;
                    this.productRequirementQuery.requirementCategory = row.requirementCategory;
                } else if (isCategoryCreat) {
                    this.productRequirementQuery.requirementProductDTO = this.openedRequirementCategoryRow;
                    this.productRequirementQuery.requirementCategory = this.openedRequirementCategoryRow.requirementCategory;
                } else {
                    this.requirementBatchEditQuery.productList = [];
                }
                this.materialInformationDTOList = [];
                // 重置级联选择器的值，确保UI更新
                this.$nextTick(() => {
                    this.selectedMaterialCascade = [];
                });
                this.requirementCopyQuery.groupNumber = [];
                this.getProductRequirement();
            },

            // 获取产品指标
            getProductRequirement() {
                // 重置选择状态
                this.$nextTick(() => {
                    this.selectedMaterialCascade = [];
                });
                this.materialInformationDTOList = [];
                
                if (this.creatProduct) {
                    axios.get("/requirementMaintenance/getMaterialInformationList").then((res) => {
                        if (res.data.flag) {
                            // 预处理数据，添加唯一ID
                            let processedData = this.processMaterialData(res.data.data);
                            this.materialInformationOptions = processedData;
                            // 为级联选择器准备数据
                            this.materialCascadeOptions = processedData;
                            this.requirementList = [];
                            this.productRequirementTitle = "新增产品";
                            axios.get("/requirementMaintenance/getItemSelectLabel").then((res) => {
                                if (res.data.flag) {
                                    this.inspectionItemOptions = res.data.data;
                                    this.productRequirementVisible = true;
                                }
                            })
                        } else {
                            this.$message.error("产品信息获取失败")
                        }
                    });
                }
                if (this.copyToGroup) {
                    axios.get("/requirementMaintenance/getGroupList").then((res) => {
                        if (res.data.flag) {
                            this.requirementCopyGroupOptions = res.data.data.map(item => {
                                return {
                                    groupNumber: item.groupNumber,
                                    groupName: item.groupName,
                                };
                            })
                            axios.get("/requirementMaintenance/getMaterialInformationList").then((res) => {
                                if (res.data.flag) {
                                    // 预处理数据，添加唯一ID
                                    let processedData = this.processMaterialData(res.data.data);
                                    this.materialInformationOptions = processedData;
                                    // 为级联选择器准备数据
                                    this.materialCascadeOptions = processedData;
                                } else {
                                    this.$message.error("产品信息获取失败")
                                }
                            });
                        } else {
                            this.$message.error("集团获取失败")
                        }
                    });
                }
                if (this.copyToProduct) {
                    axios.get("/requirementMaintenance/getMaterialInformationList").then((res) => {
                        if (res.data.flag) {
                            // 预处理数据，添加唯一ID
                            let processedData = this.processMaterialData(res.data.data);
                            this.materialInformationOptions = processedData;
                            // 为级联选择器准备数据
                            this.materialCascadeOptions = processedData;
                        } else {
                            this.$message.error("产品信息获取失败")
                        }
                    });
                }
                if (this.isBatchEdit) {
                    this.requirementList = [];
                    axios.get("/requirementMaintenance/getItemSelectLabel").then((res) => {
                            if (res.data.flag) {
                                this.inspectionItemOptions = res.data.data;
                                if (this.productRequirementQuery.group) {
                                    this.productRequirementTitle = this.productQuery.groupName + "客户产品指标批量编辑";
                                } else {
                                    this.productRequirementTitle = "通用产品指标批量编辑";
                                }
                                this.productRequirementVisible = true;
                            } else {
                                this.$message.error("检测项目获取失败")
                            }
                        }
                    );
                } else if (!this.creatProduct) {
                    axios.post("/requirementMaintenance/getProductRequirement", Object.assign({}, this.productRequirementQuery, { user: this.user })).then((res) => { // 添加 user 属性
                            if (res.data.flag) {
                                this.requirementList = res.data.data;
                                this.deletedRequirementList = [];
                                axios.get("/requirementMaintenance/getItemSelectLabel").then((res) => {
                                        if (res.data.flag) {
                                            this.inspectionItemOptions = res.data.data;
                                            axios.post("/requirementMaintenance/getRequirementCategorySelectLabel", Object.assign({}, this.productRequirementQuery, { user: this.user })).then((res) => { // 添加 user 属性
                                                if (res.data.flag) {
                                                    this.productRequirementCategoryOptions = res.data.data;
                                                    if (this.productRequirementQuery.group) {
                                                        this.productRequirementTitle = this.productQuery.groupName
                                                            + "-"
                                                            + this.productRequirementQuery.requirementProductDTO.productCategory
                                                            + "-" + this.productRequirementQuery.requirementCategory;
                                                    } else {
                                                        this.productRequirementTitle = this.productRequirementQuery.requirementProductDTO.productCategory;
                                                    }
                                                    this.productRequirementVisible = true;
                                                } else {
                                                    this.$message.error("产品类别获取失败")
                                                }
                                            })
                                        } else {
                                            this.$message.error("检测项目获取失败")
                                        }
                                    }
                                )
                            } else {
                                this.$message.error("产品指标获取失败")
                            }
                        }
                    );
                }

            },

            // 产品项目添加
            requirementItemAdd() {
                let item = {
                    qualityInspectionItem: "",
                    itemName: "",
                    qualityRequirement: "",
                    internalControlRequirement: "",
                    showStatus: true
                };
                this.requirementList.push(item);
            },

            // 产品项目删除
            requirementItemDelete(row) {
                this.$confirm("是否删除检测项目?", "提示", {type: "warning"}).then(() => {
                    this.requirementList = this.requirementList.filter(item => item !== row);
                    /*if (row.qualityInspectionItem !== "") {
                        this.inspectionItemOptions.push({
                            label: row.qualityInspectionItem,
                            value: row.qualityInspectionItem
                        });
                    }*/
                    this.deletedRequirementList.push(row);
                }).catch(() => {
                    this.$message.info("操作取消");
                });
            },

            // 产品项目处理
            requirementItemHandle() {
                this.fullscreenLoading = true;
                let requirementItemHandleDTO = {};
                requirementItemHandleDTO.isRequirementCopy = this.isRequirementCopy;
                requirementItemHandleDTO.isRequirementEdit = this.isRequirementEdit;
                requirementItemHandleDTO.groupRequirement = this.groupRequirement;
                requirementItemHandleDTO.productRequirementDTOList = this.requirementList;
                requirementItemHandleDTO.deletedProductRequirementDTOList = this.deletedRequirementList;
                requirementItemHandleDTO.user = this.user; // 添加 user 属性
                if (!this.isBatchEdit) {
                    requirementItemHandleDTO.requirementProductDTO = this.productRequirementQuery.requirementProductDTO;
                } else {
                    requirementItemHandleDTO.requirementProductDTO = null;
                }
                requirementItemHandleDTO.isBatchEdit = this.isBatchEdit;
                requirementItemHandleDTO.groupNumber = this.productQuery.groupNumber;
                requirementItemHandleDTO.requirementProductDTOList = this.requirementBatchEditQuery.productList;
                requirementItemHandleDTO.requirementCategory = this.productRequirementQuery.requirementCategory;

                if (this.copyToGroup || this.copyToProduct || this.creatProduct) {
                    requirementItemHandleDTO.requirementProductDTO = null;
                }
                requirementItemHandleDTO.copyToGroup = this.copyToGroup;
                requirementItemHandleDTO.copyToProduct = this.copyToProduct;
                requirementItemHandleDTO.creatProduct = this.creatProduct;
                requirementItemHandleDTO.isRequirementProductCopy = this.isRequirementProductCopy;
                requirementItemHandleDTO.materialInformationDTOList = this.materialInformationDTOList;
                requirementItemHandleDTO.groupNmberList = this.requirementCopyQuery.groupNumber;

                requirementItemHandleDTO.isCategoryCreat = this.isCategoryCreat;
                requirementItemHandleDTO.newCategory = this.productCategoryQuery.productCategory;

                axios.post("/requirementMaintenance/requirementItemHandle", requirementItemHandleDTO).then((res) => {
                    if (res.data.flag) {
                        this.$message.success("指标处理成功")
                        this.productCheck();
                        if (this.isCategoryCreat) {
                            axios.post("/requirementMaintenance/getRequirementCategory", this.openedRequirementCategoryRow).then((res) => {
                                if (res.data.flag) {
                                    this.requirementCategoryList = res.data.data;
                                    this.productRequirementCategoryTitle = this.productQuery.groupName + "-" + row.productCategory + "-指标类别";
                                    this.productRequirementCategoryVisible = true;
                                } else {
                                    this.$message.error("公司获取失败")
                                }
                            })
                        }
                        this.fullscreenLoading = false;
                        this.productRequirementVisible = false;
                    } else {
                        this.$message.error("指标处理失败")
                        this.getProductRequirement();
                        this.fullscreenLoading = false;
                    }
                })
            },

            // 打开公司弹窗
            openCompanyVisible(row) {
                this.openedCompanyVisibleRow = row;
                this.companyAddQuery.groupNumber = row.groupNumber;
                this.title = this.openedCompanyVisibleRow.groupName + "所属公司";
                this.getGroupCompany();
            },

            // 获取集团所属公司
            getGroupCompany() {
                axios.get("/requirementMaintenance/getGroupCompany/" + this.openedCompanyVisibleRow.groupNumber).then((res) => {
                    if (res.data.flag) {
                        this.companyList = res.data.data;
                        this.companyVisible = true;
                    } else {
                        this.$message.error("产品指标获取失败")
                    }
                })
            },

            // 打开公司添加弹窗
            openCompanyAddVisible() {
                this.companyAddQuery.companyNumberList = [];
                this.companyAddOptions = [];
                this.companyAddTitle = "公司添加";
                this.companyAddVisible = true;
            },

            // 获取公司添加选项
            getCompanyOptions(query) {
                if (query !== '') {
                    this.getCompanyOptionsLoading = true;
                    axios.get("/requirementMaintenance/getCompanyOptions/" + query).then((res) => {
                        if (res.data.flag) {
                            this.companyAddOptions = res.data.data;
                            this.getCompanyOptionsLoading = false;
                        } else {
                            this.$message.error("公司获取失败")
                        }
                    })
                } else {
                    this.companyAddOptions = [];
                }
            },

            // 公司添加
            companyAdd() {
                if (this.companyAddQuery.companyNumberList.length > 0) {
                    this.fullscreenLoading = true;
                    let companyOperationDTO = {};
                    companyOperationDTO.user = this.user;
                    companyOperationDTO.companyAddDTO = this.companyAddQuery;
                    axios.post("/requirementMaintenance/companyAdd", companyOperationDTO).then((res) => {
                        if (res.data.flag) {
                            this.getGroupCompany();
                            this.companyAddVisible = false;
                            this.fullscreenLoading = false;
                            res.data.data.forEach((company) => {
                                setTimeout(() => {
                                    this.$message.success("公司[" + company.companyName + "]添加成功");
                                }, 10)
                            })
                        } else {
                            this.$message.error("公司添加失败");
                        }
                    });
                } else {
                    this.$message.warning("请选择需要添加至集团的公司");
                }

            },

            // 公司删除
            companyDelete(row) {
                this.$confirm("此操作将从集团中删除该公司,是否继续?", "提示", {type: "warning"}).then(() => {
                    this.fullscreenLoading = true;
                    let companyOperationDTO = {};
                    companyOperationDTO.user = this.user;
                    companyOperationDTO.companyInformationDTO = row;
                    axios.post("/requirementMaintenance/companyDelete", companyOperationDTO).then((res) => {
                        if (res.data.flag) {
                            this.getGroupCompany();
                            this.$message.success("公司删除成功");
                            this.fullscreenLoading = false;
                        } else {
                            this.$message.error("公司删除失败");
                        }
                    });
                }).catch(() => {
                    this.$message.info("操作取消");
                });
            },

            // 返回公司列表
            backToCompanyList() {
                this.companyAddVisible = false;
                this.$message.info("返回公司列表")
            },

            // 打开产品指标类别弹窗
            openProductRequirementCategoryVisible(row) {
                this.openedRequirementCategoryRow = row
                axios.post("/requirementMaintenance/getRequirementCategory", Object.assign({}, row, { user: this.user })).then((res) => { // 添加 user 属性
                    if (res.data.flag) {
                        this.requirementCategoryList = res.data.data;
                        this.productRequirementCategoryTitle = this.productQuery.groupName + "-" + row.productCategory + "-指标类别";
                        this.productRequirementCategoryVisible = true;
                    } else {
                        this.$message.error("类别查询失败")
                    }
                })
            },

            // 复制检测项目至客户项目名称
            copyToItemName(row) {
                /*if (row.qualityInspectionItem !== "") {
                    this.inspectionItemOptions = this.inspectionItemOptions.filter(item => item.value !== row.qualityInspectionItem);
                }*/
                row.itemName = row.qualityInspectionItem;
            },

            // 默认状态切换
            defaultStatusChange(row) {
                this.requirementCategoryList.forEach((item) => {
                    if (item !== row) {
                        if (item.newDefaultStatus) {
                            item.newDefaultStatus = false;
                        }
                    }
                });
            },

            // 打开新增集团弹窗
            openGroupAddDialog() {
                this.groupAddTitle = "新增集团";
                this.groupAddQuery = {
                    groupNameList: []
                };
                this.groupAddVisible = true;
            },

            // 新增集团
            groupAdd() {
                if (this.groupAddQuery.groupNameList.length > 0) {
                    axios.post("/requirementMaintenance/groupAdd", Object.assign({}, this.groupAddQuery, { user: this.user })).then((res) => { // 添加 user 属性
                        if (res.data.flag) {
                            this.getAll();
                            this.groupAddVisible = false;
                            this.$message.success("集团新增成功");
                        } else {
                            this.$message.error("集团名称重复");
                        }
                    });
                } else {
                    this.$message.warning("请输入需要新增的集团名称");
                }
            },

            // 删除集团
            groupDelete(row) {
                this.$confirm("此操作将删除该集团,是否继续?", "提示", {type: "warning"}).then(() => {
                    this.fullscreenLoading = true;
                    axios.post("/requirementMaintenance/groupDelete", Object.assign({}, row, { user: this.user })).then((res) => { // 添加 user 属性
                        if (res.data.flag) {
                            this.getAll();
                            this.$message.success("集团删除成功");
                            this.fullscreenLoading = false;
                        } else {
                            this.$message.error("集团删除失败");
                        }
                    });
                }).catch(() => {
                    this.$message.info("操作取消");
                });
            },

            // 删除产品
            productDelete(row) {
                this.$confirm("此操作将删除该产品,是否继续?", "提示", {type: "warning"}).then(() => {
                    this.fullscreenLoading = true;
                    axios.post("/requirementMaintenance/productDelete", Object.assign({}, row, { user: this.user })).then((res) => { // 添加 user 属性
                        if (res.data.flag) {
                            this.productCheck();
                            this.$message.success("产品删除成功");
                            this.fullscreenLoading = false;
                        } else {
                            this.$message.error("产品删除失败");
                        }
                    });
                }).catch(() => {
                    this.$message.info("操作取消");
                });
            },

            // 类别删除
            categoryDelete(row) {
                this.$confirm("此操作将删除该类别,是否继续?", "提示", {type: "warning"}).then(() => {
                    this.fullscreenLoading = true;
                    let categoryDeleteDTO = {};
                    categoryDeleteDTO.requirementCategoryDTO = row;
                    categoryDeleteDTO.requirementProductDTO = this.openedRequirementCategoryRow;
                    categoryDeleteDTO.user = this.user; // 添加 user 属性
                    axios.post("/requirementMaintenance/categoryDelete", categoryDeleteDTO).then((res) => {
                        if (res.data.flag) {
                            axios.post("/requirementMaintenance/getRequirementCategory", Object.assign({}, this.openedRequirementCategoryRow, { user: this.user })).then((res) => { // 添加 user 属性
                                if (res.data.flag) {
                                    this.requirementCategoryList = res.data.data;
                                    if (this.requirementCategoryList.length === 0) {
                                        this.productRequirementCategoryVisible = false;
                                    }
                                } else {
                                    this.$message.error("公司获取失败")
                                }
                            })
                            this.productCheck();
                            this.fullscreenLoading = false;
                            this.$message.success("类别删除成功");
                        } else {
                            this.$message.error("类别删除失败");
                        }
                    });
                }).catch(() => {
                    this.$message.info("操作取消");
                });
            },

            // 类别编辑
            categoryEdit() {
                this.fullscreenLoading = true;
                let productCategoryEditDTO = {};
                productCategoryEditDTO.requirementProductDTO = this.openedRequirementCategoryRow;
                productCategoryEditDTO.requirementCategoryDTOList = this.requirementCategoryList;
                productCategoryEditDTO.user = this.user; // 添加 user 属性
                axios.post("/requirementMaintenance/categoryEdit", productCategoryEditDTO).then((res) => {
                    if (res.data.flag) {
                        axios.post("/requirementMaintenance/getRequirementCategory", Object.assign({}, this.openedRequirementCategoryRow, { user: this.user })).then((res) => { // 添加 user 属性
                            if (res.data.flag) {
                                this.requirementCategoryList = res.data.data;
                                if (this.requirementCategoryList.length === 0) {
                                    this.productRequirementCategoryVisible = false;
                                }
                                this.fullscreenLoading = false;
                            } else {
                                this.$message.error("公司获取失败")
                            }
                        })

                        this.productCheck();
                        this.$message.success("类别修改成功");
                    } else {
                        this.$message.error("类别修改失败");
                    }
                });
            },

            // 打开操作记录dialog
            openOperationLog() {
                this.resetLogSearch();
                this.getOperationLog();
                this.operationLogVisible = true;
            },

            // 获取操作日志
            getOperationLog() {
                let param = "?groupNumber=" + this.logPagination.groupNumber;
                param += "&groupName=" + this.logPagination.groupName;
                param += "&companyNumber=" + this.logPagination.companyNumber;
                param += "&companyName=" + this.logPagination.companyName;
                if (this.logPagination.operationDate == null) {
                    this.logPagination.operationDate = ["", ""];
                }
                param += "&startDate=" + this.logPagination.operationDate[0];
                param += "&endDate=" + this.logPagination.operationDate[1];
                param += "&userId=" + this.user.id;
                axios.get("/requirementMaintenance/getRequirementOperationLog/" + this.logPagination.currentPage + "/" + this.logPagination.pageSize + param).then((res) => {
                    if (res.data.flag) {
                        this.logPagination.currentPage = res.data.data.current;
                        this.logPagination.pageSize = res.data.data.size;
                        this.logPagination.total = res.data.data.total;
                        this.logList = res.data.data.records;
                    } else {
                        this.$message.error("获取操作日志异常")
                    }
                })
            },

            // 切换页码
            handleLogCurrentChange(currentPage) {
                this.logPagination.currentPage = currentPage;
                this.getOperationLog();
            },

            // 返回纪录列表
            backToRecordList() {
                this.operationLogVisible = false;
                this.$message.info("返回质检记录列表");
            },

            // 重置搜索
            resetLogSearch() {
                this.logPagination.groupNumber = "";
                this.logPagination.groupName = "";
                this.logPagination.companyNumber = "";
                this.logPagination.companyName = "";
                this.logPagination.operationDate = ["", ""];
                this.getOperationLog();
            },

            // 打开关联公司弹窗
            openLinkCompanyVisible(row) {
                this.openedLinkCompanyVisibleRow = row;
                this.linkCompanyTitle = this.productQuery.groupName + "-" + this.openedRequirementCategoryRow.productCategory + "-" + this.openedLinkCompanyVisibleRow.oldRequirementCategory + "-关联公司";
                this.getGroupLinkCompany();
            },

            // 获取指标类别关联公司
            getGroupLinkCompany() {
                this.linkCompanyList = this.openedLinkCompanyVisibleRow.companyList;
                this.linkCompanyVisible = true;
                /*let productRequirementQuery = {};
                productRequirementQuery.requirementProductDTO = this.openedRequirementCategoryRow;
                productRequirementQuery.requirementCategory = this.openedLinkCompanyVisibleRow.oldRequirementCategory;
                axios.post("/requirementMaintenance/getGroupCompany", productRequirementQuery).then((res) => {
                    if (res.data.flag) {
                        this.companyList = res.data.data;
                        this.linkCompanyVisible = true;
                    } else {
                        this.$message.error("产品指标获取失败")
                    }
                })*/
            },

            // 打开关联公司添加弹窗
            openLinkCompanyAddVisible() {
                this.linkCompanyAddQuery.companyNumberList = [];
                this.linkCompanyAddOptions = [];
                this.linkCompanyAddTitle = "关联公司添加";
                this.linkCompanyAddVisible = true;
            },

            // 获取关联公司添加选项
            getLinkCompanyOptions(query) {
                if (query !== '') {
                    this.getLinkCompanyOptionsLoading = true;
                    let productRequirementQuery = {};
                    productRequirementQuery.requirementProductDTO = this.openedRequirementCategoryRow;
                    productRequirementQuery.RequirementCategoryDTO = this.openedLinkCompanyVisibleRow;
                    productRequirementQuery.queryString = query;
                    productRequirementQuery.user = this.user; // 添加 user 属性
                    axios.post("/requirementMaintenance/getLinkCompanyAddOptions", productRequirementQuery).then((res) => {
                        if (res.data.flag) {
                            this.linkCompanyAddOptions = res.data.data;
                            this.getLinkCompanyOptionsLoading = false;
                        } else {
                            this.$message.error("公司获取失败")
                        }
                    })
                } else {
                    this.companyAddOptions = [];
                }
            },

            // 关联公司添加
            linkCompanyAdd() {
                if (this.linkCompanyAddQuery.companyNumberList.length > 0) {
                    this.fullscreenLoading = true;
                    let companyOperationDTO = {};
                    companyOperationDTO.companyAddDTO = this.linkCompanyAddQuery;
                    companyOperationDTO.requirementProductDTO = this.openedRequirementCategoryRow;
                    companyOperationDTO.requirementCategoryDTO = this.openedLinkCompanyVisibleRow;
                    companyOperationDTO.user = this.user; // 添加 user 属性
                    axios.post("/requirementMaintenance/linkCompanyAdd", companyOperationDTO).then((res) => {
                        if (res.data.flag) {
                            this.linkCompanyList = res.data.data;
                            this.linkCompanyAddQuery.companyNumberList.forEach((company) => {
                                setTimeout(() => {
                                    this.$message.success("公司[" + company.companyName + "]关联成功");
                                }, 10)
                            })
                            this.linkCompanyAddVisible = false;
                            this.fullscreenLoading = false;
                        } else {
                            this.$message.error("公司关联失败");
                        }
                    });

                } else {
                    this.$message.warning("请选择需要关联至本类别的公司");
                }
            },

            // 关联公司删除
            linkCompanyDelete(row) {
                this.$confirm("此操作将删除该公司与类别的关联关系,是否继续?", "提示", {type: "warning"}).then(() => {
                    this.fullscreenLoading = true;
                    let companyOperationDTO = {};
                    companyOperationDTO.companyInformationDTO = row;
                    companyOperationDTO.requirementProductDTO = this.openedRequirementCategoryRow;
                    companyOperationDTO.requirementCategoryDTO = this.openedLinkCompanyVisibleRow;
                    companyOperationDTO.user = this.user; // 添加 user 属性
                    axios.post("/requirementMaintenance/linkCompanyDelete", companyOperationDTO).then((res) => {
                        if (res.data.flag) {
                            this.linkCompanyList = this.linkCompanyList.filter(i => i.companyNumber !== row.companyNumber);
                            this.$message.success("公司删除成功");
                            this.fullscreenLoading = false;
                        } else {
                            this.$message.error("公司删除失败");
                        }
                    });
                }).catch(() => {
                    this.$message.info("操作取消");
                });
            },

            // 返回关联公司列表
            backToLinkCompanyList() {
                this.linkCompanyAddVisible = false;
                this.$message.info("返回公司列表")
            },

            // 返回指标类别列表
            backToProductRequirementCategoryList() {
                axios.post("/requirementMaintenance/getRequirementCategory", Object.assign({}, this.openedRequirementCategoryRow, { user: this.user })).then((res) => { // 添加 user 属性
                    if (res.data.flag) {
                        this.requirementCategoryList = res.data.data;
                        this.linkCompanyVisible = false;
                        this.$message.info("返回类别列表")
                    } else {
                        this.linkCompanyVisible = false;
                        this.$message.error("类别查询失败")
                    }
                })

            },

            // 处理级联选择器的数据变化
            handleMaterialCascadeChange(selectedNodes) {
                // 清空原有选择
                this.materialInformationDTOList = [];
                console.log("选中的节点:", selectedNodes);
                
                if (selectedNodes && selectedNodes.length > 0) {
                    // 对于每个选中的节点ID，在materialCascadeOptions中查找完整的节点对象
                    selectedNodes.forEach(nodeId => {
                        if (!nodeId) return; // 跳过无效节点
                        
                        try {
                            // 判断是类别还是物料节点
                            const isCategory = nodeId.startsWith('category_');
                            
                            // 查找对应的完整节点对象
                            let foundNode = null;
                            
                            // 在所有类别和物料中查找匹配的节点
                            if (isCategory) {
                                // 查找类别节点
                                foundNode = this.findCategoryById(this.materialCascadeOptions, nodeId);
                            } else {
                                // 查找物料节点
                                foundNode = this.findMaterialById(this.materialCascadeOptions, nodeId);
                            }
                            
                            if (foundNode) {
                                // 创建一个简单的新对象，只复制需要的属性
                                const newNode = {
                                    // 物料节点也应有类别信息，来自其父类别
                                    categoryNumber: isCategory ? 
                                        foundNode.categoryNumber : 
                                        (foundNode.parentCategory ? foundNode.parentCategory.categoryNumber : null),
                                    categoryName: isCategory ? 
                                        foundNode.categoryName : 
                                        (foundNode.parentCategory ? foundNode.parentCategory.categoryName : null),
                                    materialNumber: isCategory ? null : foundNode.materialNumber,
                                    materialName: isCategory ? null : foundNode.materialName,
                                    materialCount: foundNode.materialCount || 0,
                                    specification: foundNode.specification || ''
                                };
                                
                                // 添加到结果列表
                                this.materialInformationDTOList.push(newNode);
                            } else {
                                console.error("未找到节点:", nodeId);
                            }
                        } catch (error) {
                            console.error("处理节点出错:", error, nodeId);
                        }
                    });
                }
                
                console.log("处理后的列表:", this.materialInformationDTOList);
            },
            
            // 根据ID查找类别节点
            findCategoryById(categories, categoryId) {
                if (!categories || !Array.isArray(categories)) return null;
                
                for (let i = 0; i < categories.length; i++) {
                    if (categories[i].id === categoryId) {
                        return categories[i];
                    }
                }
                return null;
            },
            
            // 根据ID查找物料节点
            findMaterialById(categories, materialId) {
                if (!categories || !Array.isArray(categories)) return null;
                
                for (let i = 0; i < categories.length; i++) {
                    const category = categories[i];
                    
                    // 在当前类别的子节点中查找
                    if (category.childrenDTOList && Array.isArray(category.childrenDTOList)) {
                        for (let j = 0; j < category.childrenDTOList.length; j++) {
                            if (category.childrenDTOList[j].id === materialId) {
                                // 返回带有父类别信息的物料节点
                                return {
                                    ...category.childrenDTOList[j],
                                    parentCategory: {
                                        categoryNumber: category.categoryNumber,
                                        categoryName: category.categoryName
                                    }
                                };
                            }
                        }
                    }
                }
                return null;
            },

            // 处理物料数据，为每个节点添加唯一ID
            processMaterialData(data) {
                if (!data || !Array.isArray(data)) return [];
                
                return data.map(category => {
                    // 为类别添加ID和属性
                    const categoryId = 'category_' + (category.categoryNumber || Date.now() + Math.random().toString(36).substring(2, 10));
                    const newCategory = {
                        ...category,
                        id: categoryId,
                        name: category.categoryName || '未命名类别'
                    };
                    
                    // 处理子节点（物料）
                    if (category.childrenDTOList && Array.isArray(category.childrenDTOList)) {
                        newCategory.childrenDTOList = category.childrenDTOList.map(material => {
                            if (!material) return null;
                            
                            // 为物料添加ID和属性
                            const materialId = 'material_' + (material.materialNumber || Date.now() + Math.random().toString(36).substring(2, 10));
                            return {
                                ...material,
                                id: materialId,
                                name: material.specification ? 
                                    (material.materialName || '未命名物料') + '-' + material.specification : 
                                    (material.materialName || '未命名物料')
                            };
                        }).filter(Boolean); // 过滤掉null值
                    } else {
                        newCategory.childrenDTOList = [];
                    }
                    
                    return newCategory;
                });
            },
        }
    })
</script>
</html>