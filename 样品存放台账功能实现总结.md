# 样品存放台账功能实现总结

## 功能概述
在台账下载弹窗中新增了"样品存放台账下载"按钮，用于生成专门的样品存放台账Excel文件。

## 功能特点

### 1. 台账结构
- **按产品分类分工作表**：根据产品类别层级关系自动分组到不同的工作表
- **工作表命名规则**：使用产品类别的上级类别名称作为工作表名
- **预分散体特殊处理**：所有0105开头的产品类别单独放在"预分散体"工作表中
- **第一行（主标题）**：样品存放台账（横向合并所有列的单元格）
- **第二行（列名）**：
  - 质检批号
  - 罐次
  - 包装规格
  - 数量
  - 客户名称
  - 质检人员
  - 样品存放区域

### 2. 数据处理规则
- **质检批号**：剔除带"[复]"字样的记录，去除"[放行]"字样（不剔除记录，仅对质检批号进行处理）
- **样品存放区域**：格式为"工作表名称+样品橱柜X月份区域"，例如"CBS样品橱柜4月份区域"
- **质检人员**：合并inspector1、inspector2、auditor1、auditor2字段，用"/"分隔
- **分类逻辑**：
  1. 查询产品类别记录（记录a）
  2. 如果记录a的categoryLevel≤3，直接使用当前类别作为工作表名
  3. 如果记录a的categoryLevel>3，向上查找到categoryLevel=3的父类别
  4. 查询所有同级别产品类别，归到同一工作表
  5. 预分散体（0105开头）特殊处理到单独工作表
  6. 工作表按照类别编号（categoryNumber）排序
- **其他列**：参考原有台账下载逻辑获取数据

## 实现细节

### 1. 前端实现
#### 文件：`src/main/resources/static/qualityInspection/data/submittedStandingBook.html`

1. **新增按钮**：
```html
<el-tooltip content="样品存放台账下载" placement="top">
    <el-button @click="sampleStorageStandingBookDownload()"
               type="info"
               size="small"
               icon="el-icon-box"
               circle
    ></el-button>
</el-tooltip>
```

2. **新增JavaScript方法**：
```javascript
sampleStorageStandingBookDownload() {
    this.fullscreenLoading = true;
    if (this.standingBookQuery.samplingDate == null) {
        this.standingBookQuery.samplingDate = ["", ""];
    }
    let param = "?startDate=" + this.standingBookQuery.samplingDate[0];
    param += "&endDate=" + this.standingBookQuery.samplingDate[1];
    param += "&userId=" + this.param.id;
    param += "&linkId=" + this.standingBookQuery.linkId;
    param += "&dateRange=" + this.dateRange;
    axios.get("/standingBook/sampleStorageStandingBookExcelCreate" + param).then((res) => {
        if (res.data.flag) {
            this.fullscreenLoading = false;
            window.location.href = "/standingBook/standingBookExcelCreateDownload/" + this.user.id + "/" + res.data.msg;
        } else {
            this.$message.error(res.data.msg);
            this.fullscreenLoading = false;
        }
    })
}
```

### 2. 后端实现

#### 服务接口：`src/main/java/com/haihang/service/record/common/StandingBookService.java`
```java
/**
 * 生成样品存放台账Excel文件
 */
String sampleStorageStandingBookExcelCreate(StandingBookQuery standingBookQuery);
```

#### 服务实现：`src/main/java/com/haihang/service/record/common/impl/StandingBookServiceImpl.java`
主要实现逻辑：
1. 查询用户信息
2. 构建查询条件，剔除带"[复]"字样的记录
3. 处理日期范围查询条件
4. 按产品分类分组数据（`groupDataByCategory`方法）
5. 为每个分类创建独立的工作表（`createWorksheet`方法）
6. 生成Excel文件，包含：
   - 多工作表结构
   - 设置表头结构
   - 数据处理（包括样品存放区域的生成）
   - Excel样式设置
7. 返回文件名

**核心新增方法**：
- `groupDataByCategory()`：按产品分类分组数据，并按类别编号排序工作表
- `getSheetNameForCategory()`：根据产品类别获取工作表名称，精确到level=3
- `getWorkSheetCategoryNumber()`：获取工作表对应的类别编号（用于排序）
- `createWorksheet()`：创建单个工作表

#### 控制器：`src/main/java/com/haihang/controller/standingBook/StandingBookController.java`
```java
/**
 * 样品存放台账Excel创建
 */
@GetMapping("/sampleStorageStandingBookExcelCreate")
public R sampleStorageStandingBookExcelCreate(StandingBookQuery standingBookQuery) {
    try {
        String fileName = standingBookService.sampleStorageStandingBookExcelCreate(standingBookQuery);
        return new R(true, fileName);
    } catch (Exception e) {
        return new R(false, "样品存放台账生成失败：" + e.getMessage());
    }
}
```

### 3. 核心逻辑实现

#### 查询条件构建
```java
MPJLambdaWrapper<BasicInformation> recordWrapper = new MPJLambdaWrapper<BasicInformation>()
    .selectAll(BasicInformation.class)
    .leftJoin(BasicInformationNotSaved.class, BasicInformationNotSaved::getId,
            BasicInformation::getNotSubmittedRecordId)
    .leftJoin(QualityInspectionApplication.class, QualityInspectionApplication::getId,
            BasicInformation::getApplicationId)
    .isNotNull(BasicInformationNotSaved::getMergedUnSubmittedRecordId)
    .eq(BasicInformation::getLinkId, standingBookQuery.getLinkId())
    .isNotNull(BasicInformation::getProductCategoryNumber)
    .isNotNull(BasicInformation::getProductNumber)
    .notLike(BasicInformation::getProductType, "油粒")
    .notLike(BasicInformation::getProductType, "加工")
    .apply("t.product_category_number not like {0}", "0105%")
    .eq(BasicInformation::getIsDeleted, false)
    // 剔除带"[复]"字样的记录
    .notLike(BasicInformation::getQualityInspectionBatch, "[复]");
```

#### 样品存放区域生成
```java
// 样品存放区域：产品名称+样品橱柜X月份区域
String sampleStorageArea = "";
if (StrUtil.isNotBlank(dataDTO.getProductType()) && ObjectUtil.isNotNull(dataDTO.getSamplingDate())) {
    String productType = dataDTO.getProductType();
    // 获取采样日期的月份
    LocalDate samplingDate = dataDTO.getSamplingDate().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
    int month = samplingDate.getMonthValue();
    sampleStorageArea = productType + "样品橱柜" + month + "月份区域";
}
```

#### 质检批号处理
```java
// 质检批号 - 去除"[放行]"字样
String qualityInspectionBatch = dataDTO.getQualityInspectionBatch();
if (StrUtil.isNotBlank(qualityInspectionBatch)) {
    qualityInspectionBatch = StrUtil.removeAll(qualityInspectionBatch, "[放行]");
}
```

#### 两级表头结构
```java
// 创建两级表头结构（第一行为主标题，第二行为列名）
// 质检批号列
List<String> qualityInspectionBatchHead = new ArrayList<>();
qualityInspectionBatchHead.add("样品存放台账");  // 第一级标题
qualityInspectionBatchHead.add("质检批号");    // 第二级标题
writeHeadList.add(qualityInspectionBatchHead);

// 其他列类似，第一级标题都是"样品存放台账"，会自动合并
```

#### 产品分类逻辑
```java
/**
 * 按产品分类分组数据
 */
private Map<String, List<StandingBookDataDTO>> groupDataByCategory(List<StandingBookDataDTO> dataList) {
    Map<String, List<StandingBookDataDTO>> tempGroupedData = new HashMap<>();
    Map<String, String> sheetNameToCategoryNumber = new HashMap<>();
    
    // 预分散体特殊处理
    List<StandingBookDataDTO> preDispersedData = new ArrayList<>();
    List<StandingBookDataDTO> normalData = new ArrayList<>();
    
    // 分离预分散体和普通产品
    for (StandingBookDataDTO data : dataList) {
        if (StrUtil.isNotBlank(data.getProductCategoryNumber()) && 
            data.getProductCategoryNumber().startsWith("0105")) {
            preDispersedData.add(data);
        } else {
            normalData.add(data);
        }
    }
    
    // 处理预分散体
    if (CollUtil.isNotEmpty(preDispersedData)) {
        tempGroupedData.put("预分散体", preDispersedData);
        sheetNameToCategoryNumber.put("预分散体", "0105");
    }
    
    // 处理普通产品的分类映射和排序
    // 根据categoryNumber排序工作表
    List<Map.Entry<String, String>> sortedEntries = sheetNameToCategoryNumber.entrySet()
        .stream()
        .sorted(Map.Entry.comparingByValue())
        .collect(Collectors.toList());
    
    Map<String, List<StandingBookDataDTO>> groupedData = new LinkedHashMap<>();
    for (Map.Entry<String, String> entry : sortedEntries) {
        String sheetName = entry.getKey();
        if (tempGroupedData.containsKey(sheetName)) {
            groupedData.put(sheetName, tempGroupedData.get(sheetName));
        }
    }
    
    return groupedData;
}

/**
 * 根据产品类别编号获取工作表名称（精确到level=3）
 */
private String getSheetNameForCategory(String categoryNumber, Map<String, String> cache) {
    // 1. 查询当前产品类别记录
    MaterialCategory recordA = materialCategoryMapper.selectOne(
        new LambdaQueryWrapper<MaterialCategory>()
            .eq(MaterialCategory::getCategoryNumber, categoryNumber)
    );
    
    // 2. 如果当前类别级别已经是3或更小，直接使用当前类别
    if (recordA.getCategoryLevel() <= 3) {
        cache.put(categoryNumber, recordA.getCategoryName());
        return recordA.getCategoryName();
    }
    
    // 3. 查询上级类别记录，直到level=3
    MaterialCategory targetCategory = recordA;
    while (targetCategory.getCategoryLevel() > 3) {
        // 向上查找父类别，直到level=3
        // ...
    }
    
    return targetCategory.getCategoryName();
}
```

## 文件命名规则
生成的Excel文件命名格式：`公司名_样品存放台账(开始日期至结束日期).xlsx`

例如：`尚舜_样品存放台账(2024-01至2024-03).xlsx`

## 使用方式
1. 点击台账下载弹窗中的"样品存放台账下载"按钮（蓝色圆形按钮，图标为盒子）
2. 系统会根据选择的公司、日期范围生成对应的样品存放台账
3. 下载完成后可查看Excel文件，包含：
   - 多个工作表，每个工作表对应一个产品分类
   - 预分散体单独一个工作表
   - 每个工作表内的样品存放区域格式为：工作表名称+样品橱柜+月份+区域
   - 所有符合条件的样品存放记录按分类组织

## Excel格式设置
### 1. 单元格格式
- **缩小字体填充**：内容和标题单元格都设置为缩小字体填充（`setShrinkToFit(true)`）
- **对齐方式**：垂直居中、水平居中
- **边框**：四周细线边框
- **字体**：黑体，内容12号，标题14号

### 2. 列宽设置
各列宽度设置如下：
- **质检批号**：25个字符宽度
- **罐次**：25个字符宽度  
- **包装规格**：12个字符宽度
- **数量**：12个字符宽度
- **客户名称**：50个字符宽度
- **质检人员**：30个字符宽度
- **样品存放区域**：40个字符宽度

## 最新优化（2024年版本）

### 1. 产品分类逻辑优化
- **精确分类级别**：非预分散体的工作表分类只精确到categoryLevel=3
- **分类规则改进**：
  - 如果产品类别的categoryLevel已经≤3，直接使用当前类别作为工作表名
  - 如果产品类别的categoryLevel>3，向上查找到level=3的父类别作为工作表名
  - 预分散体（0105开头）仍然单独处理到"预分散体"工作表

### 2. 工作表排序优化
- **排序依据**：按照工作表对应类别的categoryNumber进行排序
- **排序逻辑**：
  1. 查询每个工作表名称对应的MaterialCategory记录
  2. 优先查找categoryLevel=3的记录
  3. 如果找不到level=3的记录，则查找最小level的记录
  4. 按照categoryNumber升序排列工作表

### 3. 核心方法改进
- **`getSheetNameForCategory()`**：优化为只查找到level=3即停止
- **`groupDataByCategory()`**：增加工作表排序功能
- **`getWorkSheetCategoryNumber()`**：新增方法，用于获取工作表排序的categoryNumber

## 注意事项
1. 功能复用了原有的台账下载框架和接口
2. 剔除了带"[复]"字样的记录以避免重复数据
3. 质检批号去除"[放行]"字样但保留记录
4. 样品存放区域根据产品类型和采样月份自动生成
5. 使用自定义的Excel样式和列宽设置策略
6. 工作表分类精确到categoryLevel=3，避免过度细分
7. 工作表按categoryNumber排序，保证输出顺序的一致性 