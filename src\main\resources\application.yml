server:
  port: 80
spring:
  datasource:
    dynamic:
      #设置默认的数据源或者数据源组,默认值即为master
      primary: qualityInspection
      #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      strict: false
      datasource:
        qualityInspection:
          # 正式库
          url: ***********************************************************************************************************************************************
          username: root
          password: 1AJYmdYHajIlzeZm
          # 本地库
          # url: ***********************************************************************************************
          # username: root
          # password: 1234
          # 海航测试库
          # url: **************************************************************************************************************************************************
          # username: root
          # password: root
          driver-class-name: com.mysql.cj.jdbc.Driver
        safetyManagement:
          # 正式库
          url: ********************************************************************************************************************
          username: root
          password: Aa20230728
          # 本地库
          # url: *************************************************************
          # username: root
          # password: 1234
          driver-class-name: com.mysql.cj.jdbc.Driver
  servlet:
    multipart:
      max-file-size: -1
      max-request-size: -1
  jackson:
    date-format: yyyy-MM-dd
    time-zone: GMT+8
  output:
    ansi:
      enabled: always

mybatis-plus:
  global-config:
    db-config:
      table-prefix: cmf_
      id-type: auto
  configuration:
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
    # log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
    # log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

logging:
  level:
    "[com.haihang.mapper]": INFO
    "[com.baomidou.mybatisplus]": INFO
    # "[com.haihang.service.record.common.impl.StandingBookServiceImpl]": OFF

mybatis-plus-join:
  #是否打印 mybatis plus join banner 默认true
  banner: true
  #全局启用副表逻辑删除(默认true) 关闭后关联查询不会加副表逻辑删除
  sub-table-logic: true
  #拦截器MappedStatement缓存(默认true)
  ms-cache: true
  #表别名(默认 t)
  table-alias: t
  #副表逻辑删除条件的位置，支持where、on默认 where （1.4.4+）
  logic-del-type: where

path-config:
  file-path: D:\SunsineQualityInspection\export\
  plugin-path: D:\SunsineQualityInspection\plugins
  excel-path: D:\SunsineQualityInspection\excel\
  standingBook-path: D:\SunsineQualityInspection\standingBook\
  salesPlan-path: D:\SunsineQualityInspection\salesPlan\
  template-path: D:\SunsineQualityInspection\template\
  report-path: D:\SunsineQualityInspection\report\
  safety-management-path: D:\SunsineQualityInspection\safetyManagement\
  # 销售提单模板地址
  sales-bill-of-lading-path: D:\SunsineQualityInspection\template\
  # sales-bill-of-lading-path: C:\Users\<USER>\Desktop\尚舜_销售\销售提单模板\

  # file-path: D:\project\haihang\sunsine_qualityinspection_data\exportTest\
  # plugin-path: D:\project\haihang\sunsine_qualityinspection_data\plugins
  # excel-path: D:\project\haihang\sunsine_qualityinspection_data\excelTest\
  # standingBook-path: D:\project\haihang\sunsine_qualityinspection_data\standingBookTest\
  # template-path: D:\project\haihang\sunsine_qualityinspection_data\templateTest\
  # report-path: D:\project\haihang\sunsine_qualityinspection_data\reportTest\
  # safety-management-path: D:\project\haihang\sunsine_qualityinspection_data\safetyManagementTest\

url-config:
  url: http://************/qualityInspection/data/notSubmittedData.html?uid=
  # url: http://localhost/qualityInspection/data/notSubmittedData.html?uid=

# 跨域请求相关配置
cors:
  supply:
    table:
      allowed-origins: http://************:8087
      # allowed-origins: http://************
      # 质检服务器ip: http://************
