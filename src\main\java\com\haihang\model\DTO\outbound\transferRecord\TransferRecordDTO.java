package com.haihang.model.DTO.outbound.transferRecord;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.haihang.model.DO.user.User;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author: zad
 * @Create: 2024/9/21 08:21
 */
@Data
@Accessors(chain = true)
public class TransferRecordDTO {
    private User user;

    private Integer id;
    private Integer linkId;

    private String customerNumber;
    private String customerName;
    private String productNumber;
    private String productCategoryNumber;
    private String productName;
    private String warehouseNumber;
    private String warehouseName;

    private String outboundNumber;
    private String outboundDate;

    private String productionBatch;
    private String outboundProductionBatch;
    private String qualityInspectionBatch;

    private BigDecimal weight;
    private BigDecimal totalWeight;
    private BigDecimal quantity;
    private String productionPackagingSpecification;

    private Integer addUserId;
    private String addUserName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime addTime;

    private Boolean status;
    private Boolean handleStatus;
    private Boolean firstReviewStatus;
    private Boolean secondReviewStatus;

    private String ourContractNumber;
    private String theirContractNumber;
    private String theirContractDetail;

    private Integer applicationId;
    private String requirement;
    private String requirementCategory;

    private String executionStandard;

    private Integer onlineAvailable;
    
    // 批号信息列表，用于在前端显示
    private List<Map<String, String>> batchInfo;
}
