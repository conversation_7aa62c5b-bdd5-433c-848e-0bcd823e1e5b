package com.haihang.model.DO.sales.salesPlan;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;


/**
 * 销售订单详情实体类（订单分录）
 * <p>
 * 该类用于管理销售订单的详细信息，包括产品信息、交货日期、质量要求等。
 * 与销售订单表形成主子表关系，一个订单可以包含多条订单详情记录。
 * 对应数据库表 cmf_saleorder_info。
 * </p>
 * 
 * @Description: 销售订单详情实体类（订单分录）
 * @Author: 系统生成
 * @Create: 2025/06/09
 * @version 1.0
 * @since 1.0
 */
@Data
@Accessors(chain = true)
@TableName(value = "cmf_saleorder_info")
public class SalesOrderDetail {
    
    /**
     * 订单详情ID，主键
     */
    @TableId(value = "id")
    private Integer id;
    
    /**
     * 订单ID，外键关联销售订单表
     * <p>
     * 关联到销售订单主表的外键，建立主子表关系
     * </p>
     */
    @TableField(value = "fid")
    private Integer orderId;

    /**
     * 交货日期
     * <p>
     * 订单明细对应产品的预计交货日期，用于生产计划和交期管理
     * </p>
     */
    @TableField(value = "XSDDMX_JHRQ")
    private String deliveryDate;

    /**
     * 产品编号
     * <p>
     * 订单明细中物料的编号，用于标识具体的产品型号
     * </p>
     */
    @TableField(value = "XSDDMX_WLBH")
    private String productNumber;
    
    /**
     * 产品名称
     * <p>
     * 订单明细中物料的名称，用于显示产品的具体信息
     * </p>
     */
    @TableField(value = "XSDDMX_WLMC")
    private String productName;

    /**
     * 通用质量要求
     * <p>
     * 适用于该产品的通用质量标准和要求，用于质检过程的参考依据
     * </p>
     */
    @TableField(value = "ty_zl_yq")
    private String generalQualityRequirements;

    /**
     * 客户质量要求类别
     * <p>
     * 集团质量指标类别，用于分类管理不同类型的质量要求
     * </p>
     */
    @TableField(value = "jt_zl_zb_lb")
    private String customerQualityRequirementsType;

    /**
     * 客户质量要求
     * <p>
     * 集团或客户特定的质量要求，用于满足特殊的产品质量标准
     * </p>
     */
    @TableField(value = "jt_zl_yq")
    private String customerQualityRequirements;

    /**
     * 内控质量要求
     * <p>
     * 公司内部制定的质量控制要求，通常比通用要求更加严格，用于内部质量管控
     * </p>
     */
    @TableField(value = "nk_zl_yq")
    private String internalQualityRequirements;
} 