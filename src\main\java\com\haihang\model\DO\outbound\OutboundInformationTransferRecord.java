package com.haihang.model.DO.outbound;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description:
 * @Author: zad
 * @Create: 2024/9/9 09:21
 */
@Data
@Accessors(chain = true)
@TableName(value = "cmf_ck_cd")
public class OutboundInformationTransferRecord {
    @TableId(value = "id")
    private Integer id;
    @TableField(value = "link_id")
    private Integer linkId;

    @TableField(value = "kh_bh")
    private String customerNumber;
    @TableField(value = "kh_mc")
    private String customerName;
    @TableField(value = "cp_bh")
    private String productNumber;
    @TableField(value = "cp_lb_bh")
    private String productCategoryNumber;
    @TableField(value = "cp_mc")
    private String productName;
    @TableField(value = "ck_bh")
    private String warehouseNumber;
    @TableField(value = "ck_mc")
    private String warehouseName;

    @TableField(value = "sj_dh")
    private String outboundNumber;
    @TableField(value = "dj_rq")
    private String outboundDate;

    @TableField(value = "pch")
    private String productionBatch;
    @TableField(value = "package_pch")
    private String outboundProductionBatch;
    @TableField(value = "zj_pch")
    private String qualityInspectionBatch;

    @TableField(value = "sl")
    private BigDecimal weight;
    @TableField(value = "all_sl")
    private BigDecimal totalWeight;
    @TableField(value = "bz_sl")
    private BigDecimal quantity;

    @TableField(value = "add_uid")
    private Integer addUserId;
    @TableField(value = "add_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime addTime;

    @TableField(value = "status")
    private Boolean status;
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Boolean handleStatus;
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Boolean firstReviewStatus;
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Boolean secondReviewStatus;

    @TableField(value = "wf_ht")
    private String ourContractNumber;
    @TableField(value = "df_ht")
    private String theirContractNumber;
    @TableField(value = "df_ht_mx")
    private String theirContractDetail;

    @TableField(value = "zid")
    private Integer applicationId;
    @TableField(value = "zl_yq")
    private String requirement;
    @TableField(value = "jt_zl_zb_lb")
    private String requirementCategory;
}
