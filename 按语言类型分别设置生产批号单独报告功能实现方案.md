# 按语言类型分别设置生产批号单独报告功能实现方案

## 功能需求
在原有的 `isSeparateReportPerBatch` 字段基础上，支持对不同语言类型的模板分别设置是否为每个生产批号生成单独报告。

## 解决方案：JSON字段存储（推荐）

### 1. 核心设计思路

将 `isSeparateReportPerBatch` 字段改为支持两种格式：
- **Boolean类型**：向后兼容原有功能，对所有语言类型统一生效
- **JSON字符串**：按语言类型分别配置，格式如：`{"1": false, "2": true, "3": false}`

其中语言类型映射：
- `1` - 中文模板
- `2` - 英文模板  
- `3` - 中英文模板

### 2. 数据库修改

```sql
-- 修改四个表的字段类型
ALTER TABLE `cmf_jt_xx` MODIFY COLUMN `is_separate_report_per_batch` JSON 
COMMENT '按语言类型设置是否每个生产批号生成单独报告：{"1": false, "2": true, "3": false}，1-中文，2-英文，3-中英文';

ALTER TABLE `cmf_jt_xs_gs` MODIFY COLUMN `is_separate_report_per_batch` JSON 
COMMENT '按语言类型设置是否每个生产批号生成单独报告：{"1": false, "2": true, "3": false}，1-中文，2-英文，3-中英文';

ALTER TABLE `cmf_transport_report_company` MODIFY COLUMN `is_separate_report_per_batch` JSON 
COMMENT '按语言类型设置是否每个生产批号生成单独报告：{"1": false, "2": true, "3": false}，1-中文，2-英文，3-中英文';

ALTER TABLE `cmf_transport_report_group` MODIFY COLUMN `is_separate_report_per_batch` JSON 
COMMENT '按语言类型设置是否每个生产批号生成单独报告：{"1": false, "2": true, "3": false}，1-中文，2-英文，3-中英文';
```

### 3. 实体类修改

将四个实体类中的 `isSeparateReportPerBatch` 字段类型从 `Boolean` 改为 `Object`：

```java
/**
 * 是否每个生产批号生成单独报告
 * <p>
 * 控制是否为每个生产批号生成单独的运输报告，而非将多个批号合并在一个报告中
 * 支持两种格式：
 * 1. Boolean类型（向后兼容）
 * 2. JSON字符串，按语言类型分别设置：{"1": false, "2": true, "3": false}
 *    其中1-中文，2-英文，3-中英文
 * </p>
 */
private Object isSeparateReportPerBatch;
```

### 4. 核心逻辑修改

#### 4.1 配置解析逻辑

在 `getCustomerReportConfig` 方法中，返回按语言类型分别的配置：

```java
private Map<String, Boolean> getCustomerReportConfig(String customerNumber) {
    Map<String, Boolean> resultMap = new HashMap<>();
    
    // 默认按语言类型设置为false
    resultMap.put("isSeparateReportPerBatch_1", false); // 中文
    resultMap.put("isSeparateReportPerBatch_2", false); // 英文
    resultMap.put("isSeparateReportPerBatch_3", false); // 中英文
    
    // ... 其他配置获取逻辑
    
    // 处理集团/公司的语言类型配置
    handleLanguageSpecificConfig(resultMap, configValue);
}
```

#### 4.2 语言类型配置处理

新增 `handleLanguageSpecificConfig` 方法：

```java
private void handleLanguageSpecificConfig(Map<String, Boolean> resultMap, Object configValue) {
    if (configValue == null) {
        return;
    }
    
    if (configValue instanceof Boolean) {
        // 向后兼容：Boolean类型对所有语言类型统一生效
        Boolean boolValue = (Boolean) configValue;
        resultMap.put("isSeparateReportPerBatch_1", boolValue);
        resultMap.put("isSeparateReportPerBatch_2", boolValue);
        resultMap.put("isSeparateReportPerBatch_3", boolValue);
    } else if (configValue instanceof String) {
        // 新格式：JSON字符串按语言类型分别设置
        try {
            String jsonStr = (String) configValue;
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, Boolean> languageConfig = objectMapper.readValue(jsonStr, 
                new TypeReference<Map<String, Boolean>>() {});
            
            // 更新各语言类型的配置
            if (languageConfig.containsKey("1")) {
                resultMap.put("isSeparateReportPerBatch_1", languageConfig.get("1"));
            }
            if (languageConfig.containsKey("2")) {
                resultMap.put("isSeparateReportPerBatch_2", languageConfig.get("2"));
            }
            if (languageConfig.containsKey("3")) {
                resultMap.put("isSeparateReportPerBatch_3", languageConfig.get("3"));
            }
        } catch (Exception e) {
            log.warn("解析语言类型配置失败: {}, 使用默认配置", configValue, e);
        }
    }
}
```

#### 4.3 报告生成逻辑修改

在 `creatTransportReportPicture` 方法中，根据模板语言类型获取对应配置：

```java
// 判断是否为竖版模板
boolean isVerticalTemplate = transportReportTemplate.getItemDirection() != null && transportReportTemplate.getItemDirection() == 2;

// 根据模板语言类型获取对应的配置
String languageConfigKey = "isSeparateReportPerBatch_" + transportReportTemplate.getLanguageType();
boolean isSeparateByLanguage = Boolean.TRUE.equals(configMap.get(languageConfigKey));

// 竖版模板强制生成多个文件，或者根据语言类型配置判断是否生成多个文件
boolean generateMultipleFiles = (isVerticalTemplate || isSeparateByLanguage) 
        && transportReportDataList.size() > 1;
```

### 5. 功能特点

#### 5.1 向后兼容性
- 支持原有的Boolean类型配置，确保现有系统正常运行
- 新的JSON格式可以逐步迁移

#### 5.2 灵活性
- 可以为不同语言类型的模板设置不同的行为
- 例如：中文模板合并报告，英文模板分离报告

#### 5.3 优先级保持
- 公司配置 > 集团配置 > 默认配置
- 竖版模板强制分离优先级最高

### 6. 使用示例

#### 6.1 配置示例

**场景1：只有英文模板需要分离报告**
```json
{"1": false, "2": true, "3": false}
```

**场景2：中文和中英文模板分离，英文模板合并**
```json
{"1": true, "2": false, "3": true}
```

**场景3：所有语言类型都分离（等同于原Boolean true）**
```json
{"1": true, "2": true, "3": true}
```

#### 6.2 向后兼容使用

现有的Boolean配置会自动转换：
- `true` → 所有语言类型都分离
- `false` → 所有语言类型都合并

### 7. 优势总结

1. **精细化控制**：可以针对不同语言类型设置不同策略
2. **向后兼容**：原有配置继续有效，无需立即迁移
3. **易于扩展**：未来如果新增语言类型，只需在JSON中添加对应配置
4. **逻辑清晰**：每种语言类型的配置都是独立的，便于理解和维护

### 8. 替代方案

如果不想使用JSON格式，也可以考虑以下方案：

#### 方案二：独立字段
为每种语言类型添加独立的Boolean字段：
- `isSeparateReportPerBatch1` (中文)
- `isSeparateReportPerBatch2` (英文)  
- `isSeparateReportPerBatch3` (中英文)

#### 方案三：位掩码
使用整数的位来表示不同语言类型的开关：
- 第1位表示中文
- 第2位表示英文
- 第3位表示中英文

但考虑到可读性和扩展性，**推荐使用JSON格式的方案一**。 