package com.haihang.model.DTO.outbound.transportReport;

import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.haihang.model.DO.user.User;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class TransportReportCustomerDTO {
    private Integer groupId;
    private String groupNumber;
    private String groupName;
    private String groupRemark;
    private String companyCount;
    private Boolean requirementGroup;

    private Integer companyId;
    private String companyNumber;
    private String companyName;
    private String companyRemark;

    private List<String> companyNumbers;

    private Integer operatorId; // 操作人ID
    private String operatorName; // 操作人名称
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime operateTime; // 操作时间
    
    private User user; // 当前操作用户
}
