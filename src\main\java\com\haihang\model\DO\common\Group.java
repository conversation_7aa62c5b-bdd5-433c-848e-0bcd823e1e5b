package com.haihang.model.DO.common;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description: 集团
 * @Author: zad
 * @Create: 2023/8/16 10:31
 */
@Data
@Accessors(chain = true)
@TableName(value = "cmf_jt_xx")
public class Group {
    @TableField(value = "jt_bh")
    private String groupNumber;
    @TableField(value = "jt_mc")
    private String groupName;
    private Integer transportReportGroupId;
    private String transportReportRemark;

    private Boolean generalExecutionStandard;
    private String executionStandard;
    private Boolean generalConclusion;
    private String conclusion;

    /**
     * 是否强制应用执行标准配置（不考虑客户指标）
     * <p>
     * true: 直接按配置覆盖执行标准，不判断客户是否有指标
     * false: 先判断客户是否有指标，无指标时不覆盖，有指标时按配置覆盖
     * </p>
     */
    private Boolean forceApplyExecutionStandard;

    /**
     * 是否强制应用结论配置（不考虑客户指标）
     * <p>
     * true: 直接按配置覆盖结论，不判断客户是否有指标
     * false: 先判断客户是否有指标，无指标时不覆盖，有指标时按配置覆盖
     * </p>
     */
    private Boolean forceApplyConclusion;

    private Boolean isAddTestMethod;
    private Boolean isAddCustomerProductCode;
    private Boolean isAddTankNumber;
    private Boolean isAddInspectionDate;

    /**
     * 是否每个生产批号生成单独报告
     * <p>
     * 控制是否为每个生产批号生成单独的运输报告，而非将多个批号合并在一个报告中
     * 支持两种格式：
     * 1. Boolean类型（向后兼容）
     * 2. JSON字符串，按语言类型分别设置：{"1": false, "2": true, "3": false}
     *    其中1-中文，2-英文，3-中英文
     * </p>
     */
    private Object isSeparateReportPerBatch;

    /**
     * 随车质检单字段
     * <p>
     * 用于存储随车质检单中的字段信息
     * </p>
     */
    private String transportReportField;
     /**
     * 随车质检单排除字段
     * <p>
     * 用于存储随车质检单中需要排除的字段信息
     * </p>
     */
    private String transportReportExcludeFields;

    private String operator;

    /**
     * 客户自定义字段1的行位置
     * <p>
     * 用于在模板中定位客户自定义内容1的行位置
     * </p>
     */
    private Integer customerRow1;

    /**
     * 客户自定义字段1的列位置
     * <p>
     * 用于在模板中定位客户自定义内容1的列位置
     * </p>
     */
    private Integer customerColumn1;

    /**
     * 客户自定义字段1的内容
     * <p>
     * 用于存储客户自定义内容1的具体值
     * </p>
     */
    private String customerItem1;

    /**
     * 客户自定义字段2的行位置
     * <p>
     * 用于在模板中定位客户自定义内容2的行位置
     * </p>
     */
    private Integer customerRow2;

    /**
     * 客户自定义字段2的列位置
     * <p>
     * 用于在模板中定位客户自定义内容2的列位置
     * </p>
     */
    private Integer customerColumn2;

    /**
     * 客户自定义字段2的内容
     * <p>
     * 用于存储客户自定义内容2的具体值
     * </p>
     */
    private String customerItem2;

    /**
     * 客户自定义字段3的行位置
     * <p>
     * 用于在模板中定位客户自定义内容3的行位置
     * </p>
     */
    private Integer customerRow3;

    /**
     * 客户自定义字段3的列位置
     * <p>
     * 用于在模板中定位客户自定义内容3的列位置
     * </p>
     */
    private Integer customerColumn3;

    /**
     * 客户自定义字段3的内容
     * <p>
     * 用于存储客户自定义内容3的具体值
     * </p>
     */
    private String customerItem3;

    /**
     * 客户自定义字段4的行位置
     * <p>
     * 用于在模板中定位客户自定义内容4的行位置
     * </p>
     */
    private Integer customerRow4;

    /**
     * 客户自定义字段4的列位置
     * <p>
     * 用于在模板中定位客户自定义内容4的列位置
     * </p>
     */
    private Integer customerColumn4;

    /**
     * 客户自定义字段4的内容
     * <p>
     * 用于存储客户自定义内容4的具体值
     * </p>
     */
    private String customerItem4;
}
