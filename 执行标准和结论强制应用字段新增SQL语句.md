# 执行标准和结论强制应用字段新增SQL语句

## 功能说明

为了支持在客户对产品没有指标的情况下也能显示配置的执行标准和结论，在四个相关实体类对应的数据库表中新增了强制应用配置字段。

## 新增字段说明

### forceApplyExecutionStandard
- **类型**: Boolean
- **说明**: 是否强制应用执行标准配置（不考虑客户指标）
- **取值**: 
  - `true`: 直接按配置覆盖执行标准，不判断客户是否有指标
  - `false`: 先判断客户是否有指标，无指标时不覆盖，有指标时按配置覆盖

### forceApplyConclusion  
- **类型**: Boolean
- **说明**: 是否强制应用结论配置（不考虑客户指标）
- **取值**:
  - `true`: 直接按配置覆盖结论，不判断客户是否有指标
  - `false`: 先判断客户是否有指标，无指标时不覆盖，有指标时按配置覆盖

## SQL语句

### 1. 公司表（cmf_jt_xs_gs）
```sql
-- 为公司表添加强制应用执行标准字段
ALTER TABLE cmf_jt_xs_gs 
ADD COLUMN forceApplyExecutionStandard BOOLEAN DEFAULT FALSE COMMENT '是否强制应用执行标准配置（不考虑客户指标）';

-- 为公司表添加强制应用结论字段
ALTER TABLE cmf_jt_xs_gs 
ADD COLUMN forceApplyConclusion BOOLEAN DEFAULT FALSE COMMENT '是否强制应用结论配置（不考虑客户指标）';
```

### 2. 集团表（cmf_jt_xx）
```sql
-- 为集团表添加强制应用执行标准字段
ALTER TABLE cmf_jt_xx 
ADD COLUMN forceApplyExecutionStandard BOOLEAN DEFAULT FALSE COMMENT '是否强制应用执行标准配置（不考虑客户指标）';

-- 为集团表添加强制应用结论字段
ALTER TABLE cmf_jt_xx 
ADD COLUMN forceApplyConclusion BOOLEAN DEFAULT FALSE COMMENT '是否强制应用结论配置（不考虑客户指标）';
```

### 3. 运输报告公司表（cmf_transport_report_company）
```sql
-- 为运输报告公司表添加强制应用执行标准字段
ALTER TABLE cmf_transport_report_company 
ADD COLUMN forceApplyExecutionStandard BOOLEAN DEFAULT FALSE COMMENT '是否强制应用执行标准配置（不考虑客户指标）';

-- 为运输报告公司表添加强制应用结论字段
ALTER TABLE cmf_transport_report_company 
ADD COLUMN forceApplyConclusion BOOLEAN DEFAULT FALSE COMMENT '是否强制应用结论配置（不考虑客户指标）';
```

### 4. 运输报告集团表（cmf_transport_report_group）
```sql
-- 为运输报告集团表添加强制应用执行标准字段
ALTER TABLE cmf_transport_report_group 
ADD COLUMN forceApplyExecutionStandard BOOLEAN DEFAULT FALSE COMMENT '是否强制应用执行标准配置（不考虑客户指标）';

-- 为运输报告集团表添加强制应用结论字段
ALTER TABLE cmf_transport_report_group 
ADD COLUMN forceApplyConclusion BOOLEAN DEFAULT FALSE COMMENT '是否强制应用结论配置（不考虑客户指标）';
```

## 执行顺序
建议按照以下顺序执行SQL语句：
1. 集团表（cmf_jt_xx）
2. 公司表（cmf_jt_xs_gs）
3. 运输报告集团表（cmf_transport_report_group）
4. 运输报告公司表（cmf_transport_report_company）

## 注意事项
1. 所有新增字段默认值为 `FALSE`，保持向后兼容
2. 执行SQL前请备份相关数据表
3. 建议在测试环境先验证SQL语句的正确性
4. 新字段添加后，需要相应更新前端配置页面以支持这些字段的配置 