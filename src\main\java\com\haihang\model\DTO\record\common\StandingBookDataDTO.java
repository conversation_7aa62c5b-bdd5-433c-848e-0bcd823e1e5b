package com.haihang.model.DTO.record.common;

import com.haihang.model.DO.item.*;
import com.haihang.model.DO.record.ItemResultData;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @Author: zad
 * @Create: 2023/9/11 16:56
 */
@Data
@Accessors(chain = true)
public class StandingBookDataDTO{
    private Integer id;
    private Integer mergedUnSubmittedRecordId;
    private String qualityInspectionBatch;
    private String productionBatch;
    private String customerName;
    private String productType;
    private String productCategoryNumber;
    private String tankBatch;
    private String packagingSpecification;
    private String quantity;
    private Date samplingDate;
    private String inspector1;
    private String inspector2;
    private String auditor1;
    private String auditor2;
    private String preDispersedInspector;
    private String result;

    private List<ItemResultData> itemResultDataList;


    private List<CommonValue> commonValue;
    private List<CommonData> commonData;

    private List<Acidity> acidity;
    private List<Ash> ash;
    private List<AshOfSulfur> ashOfSulfur;

    private List<BlackPoint> blackPoint;
    private List<BulkDensity> bulkDensity;

    private List<Clarity> clarity;
    private List<CrystallizationPoint> crystallizationPoint;
    private List<CyclohexaneInsolubles> cyclohexaneInsolubles;

    private List<DContent> zadContent;
    private List<DTContent> dtContent;
    private List<DTTContent> dttContent;

    private List<EthanolInsolubleMatter> ethanolInsolubleMatter;

    private List<FinalMeltingPoint> finalMeltingPoint;
    private List<FreeAmine> freeAmine;
    private List<FreeM> freeM;

    private List<HeatingReduction> heatingReduction;

    private List<Impurity> impurity;
    private List<InfraredSpectroscopicSimilarity> infraredSpectroscopicSimilarity;
    private List<InitialMeltingPointAverage> initialMeltingPointAverage;
    private List<InitialMeltingPoint> initialMeltingPoint;
    private List<InsolubleSulfurContent> insolubleSulfurContent;
    private List<IsopropylDiphenylamine> isopropylDiphenylamine;

    private List<MethanolInsolubleMatter> methanolInsolubleMatter;

    private List<NitrogenContent> nitrogenContent;

    private List<OilAbsorptionValue> oilAbsorptionValue;
    private List<OilContent1> oilContent1;
    private List<OilContent2> oilContent2;
    private List<OutwardAppearance> outwardAppearance;

    private List<ParticleStrengthForSave> particleStrengthForSave;
    private List<PowderingRate> powderingRate;
    private List<Proportion> proportion;
    private List<Purity1> purity1;
    private List<Purity2> purity2;
    private List<Purity3> purity3;

    private List<ResidualAmountOfCarbonDisulfide> residualAmountOfCarbonDisulfide;

    private List<SieveResidue> sieveResidue;
    private List<SieveResidueOfNet> sieveResidueOfNet;
    private List<SofteningPoint> softeningPoint;
    private List<SulfurContent> sulfurContent;
    private List<SulfurContentOfColorMasterBatch> sulfurContentOfColorMasterBatch;

    private List<ThermalStability105> thermalStability105;
    private List<ThermalStability110> thermalStability110;
    private List<ThermalStability120> thermalStability120;
}
