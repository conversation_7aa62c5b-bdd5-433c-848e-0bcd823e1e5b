package com.haihang.controller.outbound;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.haihang.common.R;
import com.haihang.model.DO.common.Staff;
import com.haihang.model.DO.outbound.OutboundInformationTransferRecord;
import com.haihang.model.DO.user.User;
import com.haihang.model.DTO.outbound.transferRecord.TransferRecordDTO;
import com.haihang.model.DTO.outbound.transportReport.ResultJudgmentDTO;
import com.haihang.model.DTO.outbound.transportReport.TransportReportCreatDTO;
import com.haihang.model.DTO.outbound.transportReport.TransportReportDTO;
import com.haihang.model.DTO.outbound.transportReport.TransportReportReviewDTO;
import com.haihang.model.Query.outbound.TransferRecordQuery;
import com.haihang.service.outbound.TransferRecordService;
import com.haihang.service.outbound.OutboundInformationTransferRecordService;
import com.haihang.utils.common.FileUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import cn.hutool.core.util.StrUtil;

/**
 * @Description: 出库传递单控制器
 * @Author: zad
 * @Create: 2024/9/20 16:22
 */
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/transferRecord")
public class TransferRecordController {
    @Resource
    private HttpServletResponse response;
    @Resource
    private HttpServletRequest request;

    private final TransferRecordService transferRecordService;
    private final OutboundInformationTransferRecordService outboundInformationTransferRecordService;

    @Value("${path-config.file-path}")
    private String filePath;

    /**
     * 获取出库传递单页面
     *
     * @param transferRecordQuery 出库传递单查询
     * @return 出库传递单页面
     */
    @PostMapping("/getTransferRecordPage")
    public R getTransferRecordPage(@RequestBody TransferRecordQuery transferRecordQuery) {
        IPage<TransferRecordDTO> page = transferRecordService.getTransferRecordPage(transferRecordQuery.getCurrent(),
                transferRecordQuery.getSize(), transferRecordQuery);
        if (transferRecordQuery.getCurrent() > page.getPages()) {
            page = transferRecordService.getTransferRecordPage((int) page.getPages(), transferRecordQuery.getSize(),
                    transferRecordQuery);
        }
        return new R(true, page);
    }

    /**
     * 获取出库传递单详情
     *
     * @param transferRecordDTO 出库传递单DTO
     * @return 出库传递单详情
     */
    @PostMapping("/getTransferRecordDetails")
    public R getTransferRecordDetails(@RequestBody TransferRecordDTO transferRecordDTO) {
        return new R(true, transferRecordService.getTransferRecordDetails(transferRecordDTO));
    }

    /**
     * 创建随车质检单
     *
     * @param transportReportCreatDTO 随车质检单创建DTO
     * @return 随车质检单DTO
     */
    @PostMapping("/creatTransportReport")
    public R creatTransportReport(@RequestBody TransportReportCreatDTO transportReportCreatDTO) {
        OutboundInformationTransferRecord transferRecord = outboundInformationTransferRecordService
                .getById(transportReportCreatDTO.getTransferRecord().getId());
        if (ObjUtil.isNull(transferRecord)) {
            return new R(false, "相关出库传递单已删除，请刷新后重试");
        }
        if (ObjUtil.isNotNull(transferRecord.getHandleStatus())) {
            return new R(false, "相关出库传递单已处理，请刷新后重试");
        }
        return new R(true, transferRecordService.creatTransportReport(transportReportCreatDTO));
    }

    /**
     * 保存随车质检单并返回随车质检单预览图片
     *
     * @param transportReportDTO 随车质检单DTO
     * @return 随车质检单预览图片url
     */
    @PostMapping("/saveTransportReport")
    public R saveTransportReport(@RequestBody TransportReportDTO transportReportDTO) {
        OutboundInformationTransferRecord transferRecord = outboundInformationTransferRecordService
                .getById(transportReportDTO.getTransferRecord().getId());
        if (ObjUtil.isNull(transferRecord)) {
            return new R(false, "相关出库传递单已删除，请刷新后重试");
        }
        if (ObjUtil.isNotNull(transferRecord.getHandleStatus())) {
            return new R(false, "相关出库传递单已处理，请刷新后重试");
        }

        List<String> urlList = new ArrayList<>();
        long saveStartTime = System.currentTimeMillis();
        boolean saveResult = transferRecordService.saveTransportReport(transportReportDTO);
        double saveDuration = (System.currentTimeMillis() - saveStartTime) / 1000.0;
        log.info("传递单保存耗时: {}秒", String.format("%.2f", saveDuration));

        if (saveResult) {
            long fileStartTime = System.currentTimeMillis();
            urlList = transferRecordService.creatTransportReportPicture(
                    transportReportDTO.getTransportQualityInspectionReport().getLinkId(),
                    transportReportDTO.getTransportQualityInspectionReport().getOutboundNumber(),
                    transportReportDTO.getTransportQualityInspectionReport().getProductName());
            double fileDuration = (System.currentTimeMillis() - fileStartTime) / 1000.0;
            log.info("文件生成耗时: {}秒", String.format("%.2f", fileDuration));
            return new R(true, urlList);
        }
        return new R(false, "随车质检单保存失败");
    }

    /**
     * 获取随车质检单预览图片
     *
     * @param transferRecordDTO 出库传递单DTO
     * @return 随车质检单预览图片url
     */
    @PostMapping("/getTransportReportPreview")
    public R getTransportReportPreview(@RequestBody TransferRecordDTO transferRecordDTO) {
        return new R(true, transferRecordService.getTransportReportPreview(transferRecordDTO));
    }

    /**
     * 重置出库传递单处理状态
     *
     * @param transferRecordDTO 出库传递单DTO
     * @return 重置结果
     */
    @PostMapping("/resetTransportReport")
    public R resetTransportReport(@RequestBody TransferRecordDTO transferRecordDTO) {
        OutboundInformationTransferRecord transferRecord = outboundInformationTransferRecordService
                .getById(transferRecordDTO.getId());
        if (ObjUtil.isNull(transferRecord)) {
            return new R(false, "相关出库传递单已删除，请刷新后重试");
        }
        if (ObjUtil.isNull(transferRecord.getHandleStatus())) {
            return new R(false, "相关出库传递单暂未处理，请刷新后重试");
        }
        return new R(transferRecordService.resetTransportReport(transferRecordDTO));
    }

    /**
     * 随车质检单审核
     *
     * @param transportReportReviewDTO 随车质检单审核DTO
     * @return 审核结果
     */
    @PostMapping("/reportReview")
    public R reportReview(@RequestBody TransportReportReviewDTO transportReportReviewDTO) {
        OutboundInformationTransferRecord transferRecord = outboundInformationTransferRecordService
                .getById(transportReportReviewDTO.getTransferRecord().getId());
        if (ObjUtil.isNull(transferRecord)) {
            return new R(false, "相关出库传递单已删除，请刷新后重试");
        }
        return new R(transferRecordService.reportReview(transportReportReviewDTO));
    }

    /**
     * 下载随车质检单PDF
     *
     * @param linkId         出库传递单所属公司
     * @param outboundNumber 出库传递单编号
     * @param productName    产品名称
     * @param language       语言类型
     * @param userId         用户ID
     */
    @GetMapping("/downloadTransportReport/{linkId}/{outboundNumber}/{productName}/{language}/{userId}")
    public void downloadTransportReport(
            @PathVariable Integer linkId,
            @PathVariable String outboundNumber,
            @PathVariable String productName,
            @PathVariable String language,
            @PathVariable Integer userId) {

        Map<String, String> fileMap = transferRecordService.downloadTransportReport(
                linkId, outboundNumber, productName, language);

        if (fileMap.containsKey("error")) {
            log.error("调用 transferRecordService.downloadTransportReport 时发生错误: {}", fileMap.get("error"));
            try {
                this.response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                this.response.setContentType("text/plain; charset=utf-8");
                this.response.getWriter().write("准备下载文件时出错: " + fileMap.get("error"));
            } catch (IOException e) {
                log.error("向客户端写入错误响应失败", e);
            }
            return;
        }

        String directoryInfo = fileMap.get("directoryInfo");
        String fileName = fileMap.get("fileName");
        String downloadFileName = fileMap.get("downloadFileName");
        boolean isZip = Boolean.parseBoolean(fileMap.get("isZip"));

        if (StrUtil.isBlank(directoryInfo) || StrUtil.isBlank(fileName) || StrUtil.isBlank(downloadFileName)) {
            log.error("服务返回的下载文件信息不完整。 Map: {}", fileMap);
            try {
                this.response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                this.response.setContentType("text/plain; charset=utf-8");
                this.response.getWriter().write("服务器错误: 下载文件信息不完整。");
            } catch (IOException e) {
                log.error("向客户端写入文件信息不完整的错误响应失败", e);
            }
            return;
        }

        // 在开始文件操作前记录下载尝试
        transferRecordService.saveTransportReportDownloadRecord(linkId, outboundNumber, productName, language, userId);

        File file = new File(directoryInfo, fileName);

        // 假设 FileUtil.downloadFile 会处理成功下载的响应以及常见的文件未找到错误。
        // 如果文件在此处不存在，FileUtil 理想情况下应处理该情况（例如返回 404）。
        FileUtil.downloadFile(file, this.request, this.response, downloadFileName);

        // 下载尝试后，如果是 ZIP 文件，则尝试删除它。
        if (isZip) {
            // 使用传递给 FileUtil 的相同 File 对象
            if (file.exists()) { // 再次检查文件是否存在，以防 FileUtil 移动/删除了它，或者文件从未存在过
                if (file.delete()) {
                    log.info("临时 ZIP 文件已删除: {}", file.getAbsolutePath());
                } else {
                    log.warn("删除临时 ZIP 文件失败: {}", file.getAbsolutePath());
                }
            } else {
                // 如果 FileUtil 已妥善处理了不存在的源文件，这可能是正常情况，
                // 或者如果 ZIP 文件非常小并且是直接流式传输而没有持久化，
                // 或者如果 FileUtil 内部发生了错误。
                log.warn("用于删除的临时 ZIP 文件未找到 (或已被处理): {}", file.getAbsolutePath());
            }
        }
    }

    /**
     * 获取当前用户预设工作人员
     *
     * @param user 当前用户
     * @return 当前用户预设工作人员
     */
    @PostMapping("/getStaff")
    public R getStaff(@RequestBody User user) {
        return new R(true, transferRecordService.getStaff(user));
    }

    /**
     * 设置当前用户预设工作人员
     *
     * @param staff 预设工作人员
     * @return 设置结果
     */
    @PostMapping("/setStaff")
    public R setStaff(@RequestBody Staff staff) {
        return new R(transferRecordService.setStaff(staff));
    }

    /**
     * 判断当前项目检测
     *
     * @param resultJudgmentDTO 结果判断DTO
     * @return 判断结果
     */
    @PostMapping("/resultJudgment")
    public R resultJudgment(@RequestBody ResultJudgmentDTO resultJudgmentDTO) {
        return new R(transferRecordService.resultJudgment(resultJudgmentDTO));
    }

    /**
     * 获取生产批号历史出库记录
     *
     * @param transportReportCreatDTO 随车质检单创建DTO
     * @return 出库状态
     */
    @PostMapping("/getProductionBatchOutboundRecord")
    public R getProductionBatchOutboundRecord(@RequestBody TransportReportCreatDTO transportReportCreatDTO) {
        Map<String, Object> resultMap = transferRecordService.getProductionBatchOutboundRecord(transportReportCreatDTO);
        if ((Boolean) resultMap.get("outbound")) {
            return new R(true, resultMap.get("transportReportCreatDTO"));
        } else {
            return new R(false);
        }
    }

    @PostMapping("/getProductionBatchInspectionData")
    public R getProductionBatchInspectionData(@RequestBody TransportReportDTO transportReportDTO) {
        return new R(transferRecordService.getProductionBatchInspectionData(transportReportDTO));
    }

    @PostMapping("/changeOnlineAvailable")
    public R changeOnlineAvailable(@RequestBody TransferRecordDTO transferRecordDTO) {
        return new R(true, transferRecordService.changeOnlineAvailable(transferRecordDTO));
    }
}
