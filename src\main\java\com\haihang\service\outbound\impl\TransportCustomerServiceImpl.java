package com.haihang.service.outbound.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import java.util.Map;
import java.time.LocalDateTime;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.haihang.mapper.common.CommonCompanyMapper;
import com.haihang.mapper.common.CompanyMapper;
import com.haihang.mapper.common.GroupMapper;
import com.haihang.mapper.operationLog.RequirementDeleteOperationLogMapper;
import com.haihang.mapper.outbound.TransportReportCompanyMapper;
import com.haihang.mapper.outbound.TransportReportGroupMapper;
import com.haihang.mapper.requirementMaintenance.GroupQualityStandardMapper;
import com.haihang.model.DO.requirementMaintenance.GroupQualityStandard;
import com.haihang.model.DO.common.CommonCompany;
import com.haihang.model.DO.common.Company;
import com.haihang.model.DO.common.Group;
import com.haihang.model.DO.operationLog.RequirementDeleteOperationLog;
import com.haihang.model.DO.outbound.TransportReportCompany;
import com.haihang.model.DO.outbound.TransportReportGroup;
import com.haihang.model.DTO.elementUI.SelectLabelDTO;
import com.haihang.model.DTO.outbound.transportReport.TransportReportCustomerDTO;
import com.haihang.model.Query.outbound.TransportReportCustomerQuery;
import com.haihang.service.outbound.TransportCustomerService;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class TransportCustomerServiceImpl implements TransportCustomerService {

    private final TransportReportGroupMapper transportReportGroupMapper;
    private final TransportReportCompanyMapper transportReportCompanyMapper;

    private final GroupMapper groupMapper;
    private final CompanyMapper companyMapper;

    private final CommonCompanyMapper commonCompanyMapper;

    private final GroupQualityStandardMapper groupQualityStandardMapper;

    private final RequirementDeleteOperationLogMapper requirementDeleteOperationLogMapper;

    @Override
    public IPage<TransportReportCustomerDTO> getGroupPage(int current, int size,
            TransportReportCustomerQuery transportReportCustomerQuery) {
        // 1. 查询所有符合条件的集团和公司数据
        List<Group> groupList = groupMapper.selectList(
                new LambdaQueryWrapper<Group>()
                        .like(StrUtil.isNotBlank(transportReportCustomerQuery.getGroupName()),
                                Group::getGroupName, transportReportCustomerQuery.getGroupName()));

        List<TransportReportGroup> transportReportGroupList = transportReportGroupMapper.selectList(
                new LambdaQueryWrapper<TransportReportGroup>()
                        .like(StrUtil.isNotBlank(transportReportCustomerQuery.getGroupName()),
                                TransportReportGroup::getGroupName, transportReportCustomerQuery.getGroupName()));

        List<Company> companyList = companyMapper.selectList(
                new LambdaQueryWrapper<Company>()
                        .like(StrUtil.isNotBlank(transportReportCustomerQuery.getCompanyName()),
                                Company::getCompanyName, transportReportCustomerQuery.getCompanyName()));

        List<TransportReportCompany> transportReportCompanyList = transportReportCompanyMapper.selectList(
                new LambdaQueryWrapper<TransportReportCompany>()
                        .like(StrUtil.isNotBlank(transportReportCustomerQuery.getCompanyName()),
                                TransportReportCompany::getCompanyName, transportReportCustomerQuery.getCompanyName()));

        // 2. 合并结果并转换为DTO
        List<TransportReportCustomerDTO> resultList = new ArrayList<>();

        // 处理有指标的集团
        for (Group group : groupList) {
            // 先检查是否有符合条件的公司
            boolean hasMatchedCompany = companyList.stream()
                    .anyMatch(company -> company.getGroupNumber().equals(group.getGroupNumber()));

            // 如果查询条件中包含公司名称，且该集团下没有符合条件的公司，则跳过该集团
            if (StrUtil.isNotBlank(transportReportCustomerQuery.getCompanyName()) && !hasMatchedCompany) {
                continue;
            }

            // 查询该集团下的所有公司总数（不考虑查询条件）
            long totalCompanyCount = companyMapper.selectCount(
                    new LambdaQueryWrapper<Company>()
                            .eq(Company::getGroupNumber, group.getGroupNumber()));

            TransportReportCustomerDTO dto = new TransportReportCustomerDTO();
            dto.setGroupNumber(group.getGroupNumber());
            dto.setGroupName(group.getGroupName());
            dto.setGroupRemark(group.getTransportReportRemark());
            dto.setRequirementGroup(true);
            dto.setCompanyCount(String.valueOf(totalCompanyCount));

            resultList.add(dto);
        }

        // 处理无指标的集团
        for (TransportReportGroup group : transportReportGroupList) {
            // 先检查是否有符合条件的公司
            boolean hasMatchedCompany = transportReportCompanyList.stream()
                    .anyMatch(company -> company.getGroupId().equals(group.getId()));

            // 如果查询条件中包含公司名称，且该集团下没有符合条件的公司，则跳过该集团
            if (StrUtil.isNotBlank(transportReportCustomerQuery.getCompanyName()) && !hasMatchedCompany) {
                continue;
            }

            // 查询该集团下的所有公司总数（不考虑查询条件）
            long totalCompanyCount = transportReportCompanyMapper.selectCount(
                    new LambdaQueryWrapper<TransportReportCompany>()
                            .eq(TransportReportCompany::getGroupId, group.getId()));

            TransportReportCustomerDTO dto = new TransportReportCustomerDTO();
            dto.setGroupId(group.getId());
            dto.setGroupName(group.getGroupName());
            dto.setGroupRemark(group.getRemark());
            dto.setRequirementGroup(false);
            dto.setOperatorId(group.getOperatorId());
            dto.setOperatorName(group.getOperatorName());
            dto.setOperateTime(group.getOperateTime());
            dto.setCompanyCount(String.valueOf(totalCompanyCount));

            resultList.add(dto);
        }

        // 3. 手动分页
        int total = resultList.size();
        
        // 验证分页参数
        if (current <= 0) {
            current = 1;
        }
        if (size <= 0) {
            size = 10;  // 设置默认每页大小
        }
        
        int fromIndex = (current - 1) * size;
        // 确保 fromIndex 不会小于0
        fromIndex = Math.max(0, fromIndex);
        int toIndex = Math.min(fromIndex + size, total);

        // 创建分页对象
        Page<TransportReportCustomerDTO> page = new Page<>(current, size, total);
        
        // 设置分页后的数据
        page.setRecords(fromIndex < total ? resultList.subList(fromIndex, toIndex) : new ArrayList<>());

        return page;
    }

    @Override
    public List<TransportReportCustomerDTO> getCompanyList(TransportReportCustomerDTO transportReportCustomerDTO) {
        List<TransportReportCustomerDTO> resultList = new ArrayList<>();

        if (transportReportCustomerDTO.getRequirementGroup()) {
            // 特殊指标集团，从 company 表查询
            List<Company> companyList = companyMapper.selectList(
                    new LambdaQueryWrapper<Company>()
                            .eq(Company::getGroupNumber, transportReportCustomerDTO.getGroupNumber()));

            // 转换为 DTO
            for (Company company : companyList) {
                TransportReportCustomerDTO dto = new TransportReportCustomerDTO();
                dto.setCompanyNumber(company.getCompanyNumber());
                dto.setCompanyName(company.getCompanyName());
                dto.setCompanyRemark(company.getTransportReportRemark());
                dto.setRequirementGroup(true);
                resultList.add(dto);
            }
        } else {
            // 非特殊指标集团，从 transport_report_company 表查询
            List<TransportReportCompany> companyList = transportReportCompanyMapper.selectList(
                    new LambdaQueryWrapper<TransportReportCompany>()
                            .eq(TransportReportCompany::getGroupId, transportReportCustomerDTO.getGroupId()));

            // 转换为 DTO
            for (TransportReportCompany company : companyList) {
                TransportReportCustomerDTO dto = new TransportReportCustomerDTO();
                dto.setCompanyId(company.getId());
                dto.setCompanyNumber(company.getCompanyNumber());
                dto.setCompanyName(company.getCompanyName());
                dto.setCompanyRemark(company.getRemark());
                dto.setRequirementGroup(false);
                dto.setOperatorId(company.getOperatorId());
                dto.setOperatorName(company.getOperatorName());
                dto.setOperateTime(company.getOperateTime());
                resultList.add(dto);
            }
        }

        return resultList;
    }

    @Override
    public boolean updateGroup(TransportReportCustomerDTO transportReportCustomerDTO) {
        if (transportReportCustomerDTO.getRequirementGroup()) {
            // 特殊指标集团，通过groupNumber更新Group表
            Group group = new Group();
            group.setGroupName(transportReportCustomerDTO.getGroupName());
            group.setTransportReportRemark(transportReportCustomerDTO.getGroupRemark());

            return groupMapper.update(group,
                    new LambdaQueryWrapper<Group>()
                            .eq(Group::getGroupNumber, transportReportCustomerDTO.getGroupNumber())) > 0;
        } else {
            // 非特殊指标集团，更新 transport_report_group 表
            TransportReportGroup existingGroup = transportReportGroupMapper.selectOne(
                    new LambdaQueryWrapper<TransportReportGroup>()
                            .eq(TransportReportGroup::getId, transportReportCustomerDTO.getGroupId()));

            if (existingGroup != null) {
                existingGroup.setGroupName(transportReportCustomerDTO.getGroupName());
                existingGroup.setRemark(transportReportCustomerDTO.getGroupRemark());
                existingGroup.setOperatorId(transportReportCustomerDTO.getOperatorId());
                existingGroup.setOperatorName(transportReportCustomerDTO.getOperatorName());
                return transportReportGroupMapper.updateById(existingGroup) > 0;
            }
        }
        return false;
    }

    @Override
    public boolean updateCompany(TransportReportCustomerDTO transportReportCustomerDTO) {
        if (transportReportCustomerDTO.getRequirementGroup()) {
            // 特殊指标公司，通过 companyNumber 更新 Company 表
            Company company = new Company();
            company.setTransportReportRemark(transportReportCustomerDTO.getCompanyRemark());

            return companyMapper.update(company,
                    new LambdaQueryWrapper<Company>()
                            .eq(Company::getCompanyNumber, transportReportCustomerDTO.getCompanyNumber())) > 0;
        } else {
            // 非特殊指标公司，更新 transport_report_company 表
            TransportReportCompany existingCompany = transportReportCompanyMapper.selectOne(
                    new LambdaQueryWrapper<TransportReportCompany>()
                            .eq(TransportReportCompany::getCompanyNumber,
                                    transportReportCustomerDTO.getCompanyNumber()));

            if (existingCompany != null) {
                existingCompany.setRemark(transportReportCustomerDTO.getCompanyRemark());
                existingCompany.setOperatorId(transportReportCustomerDTO.getOperatorId());
                existingCompany.setOperatorName(transportReportCustomerDTO.getOperatorName());
                return transportReportCompanyMapper.updateById(existingCompany) > 0;
            }
        }
        return false;
    }

    @Override
    public boolean addGroup(TransportReportCustomerDTO transportReportCustomerDTO) {
        // 根据是否为特殊指标集团分别处理
        if (transportReportCustomerDTO.getRequirementGroup()) {
            // 特殊指标集团，添加到 Group 表
            Group group = new Group();

            // 获取所有现有的集团编号并排序
            List<Integer> groupNumbers = groupMapper.selectList(null).stream()
                    .map(g -> Integer.parseInt(g.getGroupNumber()))
                    .sorted()
                    .collect(Collectors.toList());

            // 生成新的集团编号
            int newGroupNumber;
            if (groupNumbers.isEmpty()) {
                newGroupNumber = 1;
            } else if (groupNumbers.size() == 1) {
                newGroupNumber = groupNumbers.get(0) + 1;
            } else {
                // 获取最大值和第二大的值
                int max = groupNumbers.get(groupNumbers.size() - 1);
                int secondMax = groupNumbers.get(groupNumbers.size() - 2);

                // 计算新的编号
                newGroupNumber = secondMax + 1;
                if (newGroupNumber == max) {
                    newGroupNumber = max + 1;
                }
            }

            group.setGroupNumber(String.valueOf(newGroupNumber));
            group.setGroupName(transportReportCustomerDTO.getGroupName());
            group.setTransportReportRemark(transportReportCustomerDTO.getGroupRemark());

            return groupMapper.insert(group) > 0;
        } else {
            // 非特殊指标集团，添加到 transport_report_group 表
            TransportReportGroup group = new TransportReportGroup();
            group.setGroupName(transportReportCustomerDTO.getGroupName());
            group.setRemark(transportReportCustomerDTO.getGroupRemark());
            group.setOperatorId(transportReportCustomerDTO.getOperatorId());
            group.setOperatorName(transportReportCustomerDTO.getOperatorName());

            return transportReportGroupMapper.insert(group) > 0;
        }
    }

    @Override
    public boolean deleteCompany(TransportReportCustomerDTO transportReportCustomerDTO) {
        if (transportReportCustomerDTO.getRequirementGroup()) {
            // 使用 LambdaQueryWrapper 根据公司编号删除
            return companyMapper.delete(
                    new LambdaQueryWrapper<Company>()
                            .eq(Company::getCompanyNumber, transportReportCustomerDTO.getCompanyNumber())) > 0;
        } else {
            // 对于非特殊指标公司，使用公司ID删除
            return transportReportCompanyMapper.deleteById(transportReportCustomerDTO.getCompanyId()) > 0;
        }
    }

    @Override
    public boolean deleteGroup(TransportReportCustomerDTO transportReportCustomerDTO) {
        if (transportReportCustomerDTO.getRequirementGroup()) {
            // 查询要删除的集团质量标准，记录删除日志
            List<GroupQualityStandard> toDeleteStandards = groupQualityStandardMapper.selectList(
                    new LambdaQueryWrapper<GroupQualityStandard>()
                            .eq(GroupQualityStandard::getGroupNumber, transportReportCustomerDTO.getGroupNumber()));
            String operator = (transportReportCustomerDTO.getUser() != null) ? 
                transportReportCustomerDTO.getUser().getUsername() : "未知用户";
            logGroupQualityStandardDeletion(toDeleteStandards, operator); // 使用真实用户信息
            
            groupQualityStandardMapper.delete(
                    new LambdaQueryWrapper<GroupQualityStandard>()
                            .eq(GroupQualityStandard::getGroupNumber, transportReportCustomerDTO.getGroupNumber()));
            // 特殊指标集团：先删除该集团下的所有公司，再删除集团
            companyMapper.delete(
                    new LambdaQueryWrapper<Company>()
                            .eq(Company::getGroupNumber, transportReportCustomerDTO.getGroupNumber()));

            return groupMapper.delete(
                    new LambdaQueryWrapper<Group>()
                            .eq(Group::getGroupNumber, transportReportCustomerDTO.getGroupNumber())) > 0;
        } else {
            // 非特殊指标集团：先删除该集团下的所有公司，再删除集团
            transportReportCompanyMapper.delete(
                    new LambdaQueryWrapper<TransportReportCompany>()
                            .eq(TransportReportCompany::getGroupId, transportReportCustomerDTO.getGroupId()));

            return transportReportGroupMapper.deleteById(transportReportCustomerDTO.getGroupId()) > 0;
        }
    }

    @Override
    public List<SelectLabelDTO> searchCompanies(TransportReportCustomerDTO transportReportCustomerDTO) {
        // 将公司名称按空格分割成关键词数组
        String[] keywords = transportReportCustomerDTO.getCompanyName().split("\\s+");
        
        // 构建查询条件
        LambdaQueryWrapper<Company> existingCompanyWrapper = new LambdaQueryWrapper<>();
        existingCompanyWrapper.select(Company::getCompanyNumber);
        List<String> existingCompanyNumbers = companyMapper.selectList(existingCompanyWrapper)
                .stream()
                .map(Company::getCompanyNumber)
                .collect(Collectors.toList());

        LambdaQueryWrapper<TransportReportCompany> transportCompanyWrapper = new LambdaQueryWrapper<>();
        transportCompanyWrapper.select(TransportReportCompany::getCompanyNumber);
        List<String> transportCompanyNumbers = transportReportCompanyMapper.selectList(transportCompanyWrapper)
                .stream()
                .map(TransportReportCompany::getCompanyNumber)
                .collect(Collectors.toList());

        // 合并已存在的公司编号
        List<String> allExistingNumbers = new ArrayList<>();
        allExistingNumbers.addAll(existingCompanyNumbers);
        allExistingNumbers.addAll(transportCompanyNumbers);

        // 构建CommonCompany的查询条件
        LambdaQueryWrapper<CommonCompany> commonCompanyWrapper = new LambdaQueryWrapper<>();
        // 排除已存在的公司
        if (!allExistingNumbers.isEmpty()) {
            commonCompanyWrapper.notIn(CommonCompany::getCompanyNumber, allExistingNumbers);
        }
        // 添加关键词查询条件
        for (String keyword : keywords) {
            commonCompanyWrapper.like(CommonCompany::getCompanyName, keyword);
        }

        // 查询并转换结果
        return commonCompanyMapper.selectList(commonCompanyWrapper)
                .stream()
                .map(company -> {
                    SelectLabelDTO dto = new SelectLabelDTO();
                    dto.setValue(company.getCompanyNumber());
                    dto.setLabel(company.getCompanyName());
                    return dto;
                })
                .collect(Collectors.toList());
    }

    @Override
    public boolean addCompanies(TransportReportCustomerDTO transportReportCustomerDTO) {
        try {
            // 批量查询所有公司信息
            List<CommonCompany> commonCompanies = commonCompanyMapper.selectList(
                new LambdaQueryWrapper<CommonCompany>()
                    .in(CommonCompany::getCompanyNumber, transportReportCustomerDTO.getCompanyNumbers())
            );

            // 将公司信息转换为 Map，方便后续使用
            Map<String, CommonCompany> companyMap = commonCompanies.stream()
                .collect(Collectors.toMap(CommonCompany::getCompanyNumber, company -> company));

            // 处理每个公司
            for (String companyNumber : transportReportCustomerDTO.getCompanyNumbers()) {
                CommonCompany commonCompany = companyMap.get(companyNumber);
                if (commonCompany == null) {
                    continue;
                }

                if (transportReportCustomerDTO.getRequirementGroup()) {
                    // 特殊指标集团，添加到 Company 表
                    Company company = new Company();
                    company.setCompanyNumber(companyNumber);
                    company.setCompanyName(commonCompany.getCompanyName());
                    company.setGroupNumber(transportReportCustomerDTO.getGroupNumber());
                    companyMapper.insert(company);
                } else {
                    // 非特殊指标集团，添加到 TransportReportCompany 表
                    TransportReportCompany company = new TransportReportCompany();
                    company.setCompanyNumber(companyNumber);
                    company.setCompanyName(commonCompany.getCompanyName());
                    company.setGroupId(transportReportCustomerDTO.getGroupId());
                    company.setOperatorId(transportReportCustomerDTO.getOperatorId());
                    company.setOperatorName(transportReportCustomerDTO.getOperatorName());
                    transportReportCompanyMapper.insert(company);
                }
            }
            return true;
        } catch (Exception e) {
            log.error("添加公司失败", e);
            return false;
        }
    }

    /**
     * 记录集团质量标准删除日志
     * @param standards 要删除的集团质量标准列表
     * @param operator 删除操作人员
     */
    private void logGroupQualityStandardDeletion(List<GroupQualityStandard> standards, String operator) {
        if (CollUtil.isEmpty(standards)) {
            return;
        }
        
        standards.forEach(standard -> {
            RequirementDeleteOperationLog deleteLog = new RequirementDeleteOperationLog()
                    .setOriginalIndicatorId(standard.getId())
                    .setStandardType(2) // 2:集团质量标准
                    .setGroupNumber(standard.getGroupNumber())
                    .setProductNumber(standard.getProductNumber())
                    .setProductName(standard.getProductName())
                    .setProductCategoryNumber(standard.getProductCategoryNumber())
                    .setProductCategoryName(standard.getProductCategoryName())
                    .setItem(standard.getQualityInspectionItem())
                    .setDeleteTime(LocalDateTime.now())
                    .setDeleteOperator(operator);
            
            requirementDeleteOperationLogMapper.insert(deleteLog);
        });
    }
}