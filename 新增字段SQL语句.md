# 新增字段SQL语句

## 字段说明
- **字段名**: `isSeparateReportPerBatch`
- **字段含义**: 是否每个生产批号生成单独报告
- **表名**: `cmf_jt_xs_gs` (集团公司表)
- **字段类型**: Boolean

## SQL语句

### 1. 添加字段
```sql
-- 在集团公司表中添加"是否每个生产批号生成单独报告"字段
ALTER TABLE `cmf_jt_xs_gs` 
ADD COLUMN `is_separate_report_per_batch` TINYINT(1) DEFAULT 0 
COMMENT '是否每个生产批号生成单独报告：0-否，1-是。控制是否为每个生产批号生成单独的运输报告，而非将多个批号合并在一个报告中';
```

### 2. 字段详细信息
- **数据库字段名**: `is_separate_report_per_batch`
- **数据类型**: `TINYINT(1)`
- **默认值**: `0` (表示默认不分离报告)
- **允许空值**: 否
- **注释**: 是否每个生产批号生成单独报告：0-否，1-是

### 3. 可选的索引创建 (如果需要频繁查询)
```sql
-- 如果需要根据此字段进行频繁查询，可以创建索引
CREATE INDEX `idx_is_separate_report_per_batch` ON `cmf_jt_xs_gs` (`is_separate_report_per_batch`);
```

### 4. 验证字段添加
```sql
-- 查看表结构确认字段已添加
DESC `cmf_jt_xs_gs`;

-- 或者使用
SHOW FULL COLUMNS FROM `cmf_jt_xs_gs` WHERE Field = 'is_separate_report_per_batch';
```

## 注意事项
1. 字段名采用下划线命名法，符合数据库命名规范
2. 使用 `TINYINT(1)` 类型存储布尔值，节省存储空间
3. 设置默认值为 0，保证向后兼容性
4. 添加详细的字段注释，便于后续维护 