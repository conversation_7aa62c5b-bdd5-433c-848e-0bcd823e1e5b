package com.haihang.model.DTO.outbound.customerApproval;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description: 客户审批DTO
 * @Author: zad
 * @Create: 2024/12/20
 */
@Data
public class CustomerApprovalDTO {
    
    /**
     * 主键ID
     */
    private Integer id;
    
    /**
     * 公司ID
     */
    private Integer linkId;
    
    /**
     * 公司名称
     */
    private String companyName;
    
    /**
     * 客户编号
     */
    private String customerCode;
    
    /**
     * 客户名称
     */
    private String customerName;
    
    /**
     * 产品编号
     */
    private String productCode;
    
    /**
     * 产品类别编号
     */
    private String productCategoryCode;
    
    /**
     * 产品名称
     */
    private String productName;
    
    /**
     * 生产批号
     */
    private String productionBatch;
    
    /**
     * 质检批号
     */
    private String qualityInspectionBatch;
    
    /**
     * 数量
     */
    private BigDecimal quantity;
    
    /**
     * 包装数量
     */
    private BigDecimal packagingQuantity;
    
    /**
     * 仓库传递人UID
     */
    private Integer addUserId;
    
    /**
     * 传递人姓名
     */
    private String addUserName;
    
    /**
     * 传递时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime addTime;
    
    /**
     * 我方合同号
     */
    private String ourContractNumber;
    
    /**
     * 生产入库单ID
     */
    private String inboundId;
    
    /**
     * 实际出库生产批次号
     */
    private String actualOutboundProductionBatch;
    
    /**
     * 质检单ID
     */
    private Integer qualityInspectionId;
    
    /**
     * 状态：0-待审，1-通过，2-否决
     */
    private Integer status;
    
    /**
     * 状态描述
     */
    private String statusDesc;
    
    /**
     * 质量要求
     */
    private String qualityRequirement;
    
    /**
     * 集团质量指标类别
     */
    private String groupQualityIndicatorCategory;
    
    /**
     * 审批人
     */
    private String approver;
    
    /**
     * 审批时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime approvalTime;
} 