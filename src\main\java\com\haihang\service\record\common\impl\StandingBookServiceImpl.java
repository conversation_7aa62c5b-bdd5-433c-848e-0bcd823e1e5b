package com.haihang.service.record.common.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

import cn.idev.excel.ExcelWriter;
import cn.idev.excel.FastExcel;
import cn.idev.excel.write.metadata.WriteSheet;
import cn.idev.excel.write.metadata.style.WriteCellStyle;
import cn.idev.excel.write.metadata.style.WriteFont;
import cn.idev.excel.write.style.HorizontalCellStyleStrategy;
import cn.idev.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.haihang.factory.QualityInspectionHandlerFactory;
import com.haihang.handler.excel.ExcelCellWriteHandler;
import com.haihang.handler.record.QualityInspectionHandler;
import com.haihang.mapper.application.QualityInspectionApplicationMapper;
import com.haihang.mapper.common.CommonItemPriorityMapper;
import com.haihang.mapper.common.ProductTypeForSortMapper;
import com.haihang.mapper.common.ProductTypeForStandingBookMapper;
import com.haihang.mapper.record.BasicInformationMapper;
import com.haihang.mapper.record.ItemPriorityForSortMapper;
import com.haihang.mapper.user.UserMapper;
import com.haihang.mapper.requirementMaintenance.MaterialCategoryMapper;
import com.haihang.model.DO.application.QualityInspectionApplication;
import com.haihang.model.DO.common.CommonItemPriority;
import com.haihang.model.DO.common.ProductTypeForSort;
import com.haihang.model.DO.common.ProductTypeForStandingBook;
import com.haihang.model.DO.record.BasicInformation;
import com.haihang.model.DO.record.BasicInformationNotSaved;
import com.haihang.model.DO.record.ItemPriorityForSort;
import com.haihang.model.DO.user.User;
import com.haihang.model.DO.requirementMaintenance.MaterialCategory;
import com.haihang.model.DTO.record.common.StandingBookDataDTO;
import com.haihang.model.Query.record.ItemDataQuery;
import com.haihang.model.Query.standingBook.StandingBookQuery;
import com.haihang.model.VO.record.ProductTypeLabelVO;
import com.haihang.service.record.common.StandingBookService;
import com.haihang.utils.excel.CellDataFormatStrategy;
import com.haihang.utils.excel.ColumnDataFormatStrategy;
import com.haihang.utils.excel.ColumnWidthStyleStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import cn.idev.excel.write.metadata.holder.WriteSheetHolder;
import cn.idev.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import cn.idev.excel.metadata.Head;
import cn.idev.excel.metadata.data.WriteCellData;
import org.apache.poi.ss.usermodel.Cell;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class StandingBookServiceImpl implements StandingBookService {
    private final BasicInformationMapper basicInformationMapper;
    private final QualityInspectionApplicationMapper qualityInspectionApplicationMapper;
    private final UserMapper userMapper;
    private final ProductTypeForSortMapper productTypeForSortMapper;
    private final ItemPriorityForSortMapper itemPriorityForSortMapper;
    private final ProductTypeForStandingBookMapper productTypeForStandingBookMapper;
    private final CommonItemPriorityMapper commonItemPriorityMapper;
    private final MaterialCategoryMapper materialCategoryMapper;

    @Value("${path-config.standingBook-path}")
    private String standingBookPath;

    @Override
    public List<ProductTypeLabelVO> getProductTypeList(String linkId, String isSemiFinished, String userId) {
        // 查询用户
        User user = userMapper.selectById(Integer.parseInt(userId));
        // 条件查询
        MPJLambdaWrapper<BasicInformation> queryWrapper = new MPJLambdaWrapper<BasicInformation>()
                .selectAll(BasicInformation.class)
                .leftJoin(BasicInformationNotSaved.class, BasicInformationNotSaved::getId,
                        BasicInformation::getNotSubmittedRecordId)
                .isNotNull(BasicInformationNotSaved::getMergedUnSubmittedRecordId)
                .isNotNull(BasicInformation::getProductCategoryNumber)
                .isNotNull(BasicInformation::getProductNumber)
                .notLike(BasicInformation::getProductType, "油粒")
                .notLike(BasicInformation::getProductType, "加工")
                .apply("t.product_category_number not like {0}", "0105%")
                .eq(BasicInformation::getLinkId, Integer.parseInt(linkId))
                .eq(BasicInformation::getIsDeleted, false)
                .groupBy(BasicInformation::getProductCategoryNumber);
        // 质检记录集合
        List<BasicInformation> basicInformationList = basicInformationMapper.selectJoinList(BasicInformation.class,
                queryWrapper);
        // 产品类型排序
        basicInformationList.sort(Comparator.comparing(BasicInformation::getProductType));
        // 产品类型选项VO集合
        List<ProductTypeLabelVO> productTypes = new ArrayList<>();
        basicInformationList.forEach(basicInformation -> {
            ProductTypeLabelVO typeLabelVO = new ProductTypeLabelVO();
            typeLabelVO.setLabel(basicInformation.getProductType());
            typeLabelVO.setValue(basicInformation.getProductCategoryNumber());
            productTypes.add(typeLabelVO);
        });
        if ("3".equals(linkId)) {
            ProductTypeLabelVO typeLabelVO = new ProductTypeLabelVO();
            typeLabelVO.setLabel("预分散体");
            typeLabelVO.setValue("0105");
            productTypes.add(typeLabelVO);
        }
        if (user.getId() == 827) {
            productTypes.clear();
            ProductTypeLabelVO typeLabelVO = new ProductTypeLabelVO();
            typeLabelVO.setLabel("预分散体");
            typeLabelVO.setValue("0105");
            productTypes.add(typeLabelVO);
        }
        return productTypes;
    }

    @Override
    public Map<String, Object> getStandingBook(StandingBookQuery standingBookQuery) {
        log.info("[台账]台账查询:开始;查询条件:" + standingBookQuery);
        if (standingBookQuery.getProductCategoryNumber() == null) {
            return null;
        }

        List<Map<String, Object>> dataMapList = new ArrayList<>();
        List<Map<String, Object>> columnMapList = new ArrayList<>();
        List<Map<String, Object>> groupColumnMapList = new ArrayList<>();
        Map<String, Object> resultMap = new HashMap<>();

        log.info("[台账]质检申请查询:开始");
        LambdaQueryWrapper<QualityInspectionApplication> applicationWrapper = new LambdaQueryWrapper<QualityInspectionApplication>()
                .eq(QualityInspectionApplication::getLinkId, standingBookQuery.getLinkId())
                .eq(!StrUtil.startWith(standingBookQuery.getProductCategoryNumber(), "0105"),
                        QualityInspectionApplication::getProductCategoryNumber,
                        standingBookQuery.getProductCategoryNumber())
                .likeRight(StrUtil.startWith(standingBookQuery.getProductCategoryNumber(), "0105"),
                        QualityInspectionApplication::getProductCategoryNumber,
                        standingBookQuery.getProductCategoryNumber());
        if (StrUtil.isNotBlank(standingBookQuery.getStartDate())
                && StrUtil.isNotBlank(standingBookQuery.getEndDate())) {
            if (standingBookQuery.getDateRange()) {
                LocalDate startDate = LocalDate.parse(standingBookQuery.getStartDate());
                LocalDate endDate = LocalDate.parse(standingBookQuery.getEndDate());

                LocalDateTime startDateTime = startDate.atStartOfDay();
                LocalDateTime endDateTime = endDate.atTime(23, 59, 59);

                applicationWrapper.ge(QualityInspectionApplication::getProductionDate, startDateTime);
                applicationWrapper.le(QualityInspectionApplication::getProductionDate, endDateTime);
            } else {
                LocalDate startDate = LocalDate.parse(standingBookQuery.getStartDate() + "-01");
                LocalDate endDate = LocalDate.parse(standingBookQuery.getEndDate() + "-01");

                LocalDateTime startDateTime = startDate.withDayOfMonth(1).atStartOfDay();
                LocalDateTime endDateTime = endDate.atStartOfDay().plusMonths(1).minusSeconds(1);

                applicationWrapper.ge(QualityInspectionApplication::getProductionDate, startDateTime);
                applicationWrapper.le(QualityInspectionApplication::getProductionDate, endDateTime);
            }
        } else {
            LocalDate currentDate = LocalDate.now();

            LocalDateTime startDateTime = currentDate.withDayOfMonth(1).atStartOfDay();
            LocalDateTime endDateTime = currentDate.withDayOfMonth(currentDate.lengthOfMonth()).atTime(23, 59, 59);

            applicationWrapper.ge(QualityInspectionApplication::getProductionDate, startDateTime);
            applicationWrapper.le(QualityInspectionApplication::getProductionDate, endDateTime);
        }
        List<QualityInspectionApplication> applicationList = qualityInspectionApplicationMapper
                .selectList(applicationWrapper);
        log.info("[台账]质检申请查询:结束");
        Map<String, Map<String, String>> itemMap = new HashMap<>();
        applicationList.forEach(application -> {
            Map<String, Map<String, String>> generalMap = getRequirementMap(application.getGeneralRequirement());
            Map<String, Map<String, String>> groupMap = getRequirementMap(application.getGroupRequirement());
            Map<String, Map<String, String>> internalControlMap = getRequirementMap(
                    application.getInternalControlRequirement());

            generalMap.putAll(getRequirementMap(application.getRawGeneralRequirement()));
            groupMap.putAll(getRequirementMap(application.getRawGroupRequirement()));
            internalControlMap.putAll(getRequirementMap(application.getRawInternalControlRequirement()));

            itemMap.putAll(generalMap);
            itemMap.putAll(groupMap);
            itemMap.putAll(internalControlMap);
        });
        itemMap.remove("分子式");
        itemMap.remove("分子量");
        itemMap.remove("备注");
        itemMap.remove("有效期");
        itemMap.remove("客户产品代码");
        itemMap.remove("规格依据");
        Map<String, Map<String, String>> itemMapSet = new HashMap<>();
        applicationList.forEach(application -> {
            Map<String, Map<String, String>> generalMap = getRequirementSet(application.getGeneralRequirement());
            Map<String, Map<String, String>> groupMap = getRequirementSet(application.getGroupRequirement());
            Map<String, Map<String, String>> internalControlMap = getRequirementSet(
                    application.getInternalControlRequirement());

            generalMap.putAll(getRequirementSet(application.getRawGeneralRequirement()));
            groupMap.putAll(getRequirementSet(application.getRawGroupRequirement()));
            internalControlMap.putAll(getRequirementSet(application.getRawInternalControlRequirement()));

            itemMapSet.putAll(generalMap);
            itemMapSet.putAll(groupMap);
            itemMapSet.putAll(internalControlMap);
        });
        itemMapSet.remove("分子式");
        itemMapSet.remove("分子量");
        itemMapSet.remove("备注");
        itemMapSet.remove("有效期");
        itemMapSet.remove("客户产品代码");
        itemMapSet.remove("规格依据");
        log.info("[台账]质检项目map:" + itemMap);
        log.info("[台账]质检项目mapSet:" + itemMapSet);
        log.info("[台账]质检项目数据查询:开始");
        MPJLambdaWrapper<BasicInformation> recordWrapper = new MPJLambdaWrapper<BasicInformation>()
                .selectAll(BasicInformation.class)
                .select(BasicInformationNotSaved::getMergedUnSubmittedRecordId)
                .select(QualityInspectionApplication::getApplicationDate);
        log.info("[台账]一对多查询构建:开始");
        for (Map.Entry<String, Map<String, String>> entry : itemMapSet.entrySet()) {
            String matchingName = entry.getKey();
            log.info("[台账]一对多_检测项目:" + matchingName);
            QualityInspectionHandler qualityInspectionHandler = QualityInspectionHandlerFactory
                    .getInvokeStrategy(matchingName);
            if (ObjectUtil.isNotNull(qualityInspectionHandler)) {
                // TODO 检测项目Handler类对应多个项目名称时产生重复查询BUG，临时排除，后续优化Handler类与项目一一对应
                if (!StrUtil.equals(matchingName, "可溶性硫磺含量") && !StrUtil.equals(matchingName, "热稳定性")) {
                    qualityInspectionHandler.selectCollection(recordWrapper);
                }
            }
        }
        log.info("[台账]一对多查询构建:结束");
        recordWrapper.leftJoin(BasicInformationNotSaved.class, BasicInformationNotSaved::getId,
                BasicInformation::getNotSubmittedRecordId);
        recordWrapper.leftJoin(QualityInspectionApplication.class, QualityInspectionApplication::getId,
                BasicInformation::getApplicationId);
        log.info("[台账]左连接构建:开始");
        for (Map.Entry<String, Map<String, String>> entry : itemMapSet.entrySet()) {
            String matchingName = entry.getKey();
            log.info("[台账]左连接_检测项目:" + matchingName);
            QualityInspectionHandler qualityInspectionHandler = QualityInspectionHandlerFactory
                    .getInvokeStrategy(matchingName);
            if (ObjectUtil.isNotNull(qualityInspectionHandler)) {
                qualityInspectionHandler.leftJoin(recordWrapper);
            }
        }
        log.info("[台账]左连接构建:结束");
        boolean all = StrUtil.equals(standingBookQuery.getFinalResult(), "[1,[2,3],[2,4],0]");
        boolean qualified = standingBookQuery.getFinalResult().contains("1");
        boolean unqualifiedY = standingBookQuery.getFinalResult().contains("3");
        boolean unqualifiedN = standingBookQuery.getFinalResult().contains("4");
        boolean unqualified = unqualifiedY && unqualifiedN;
        boolean failed = standingBookQuery.getFinalResult().contains("0");

        recordWrapper.isNotNull(BasicInformationNotSaved::getMergedUnSubmittedRecordId)
                .eq(BasicInformation::getLinkId, standingBookQuery.getLinkId())
                .eq(!StrUtil.startWith(standingBookQuery.getProductCategoryNumber(), "0105"),
                        BasicInformation::getProductCategoryNumber, standingBookQuery.getProductCategoryNumber())
                .likeRight(StrUtil.startWith(standingBookQuery.getProductCategoryNumber(), "0105"),
                        BasicInformation::getProductCategoryNumber, standingBookQuery.getProductCategoryNumber())
                .like(StrUtil.isNotBlank(standingBookQuery.getQualityInspectionBatch()),
                        BasicInformation::getQualityInspectionBatch, standingBookQuery.getQualityInspectionBatch())
                .like(StrUtil.isNotBlank(standingBookQuery.getProductionBatch()), BasicInformation::getProductionBatch,
                        standingBookQuery.getProductionBatch())
                .like(StrUtil.isNotBlank(standingBookQuery.getCustomerName()), BasicInformation::getCustomerName,
                        standingBookQuery.getCustomerName());
        if (StrUtil.isNotBlank(standingBookQuery.getStartDate())
                && StrUtil.isNotBlank(standingBookQuery.getEndDate())) {
            if (standingBookQuery.getDateRange()) {
                LocalDate startDate = LocalDate.parse(standingBookQuery.getStartDate());
                LocalDate endDate = LocalDate.parse(standingBookQuery.getEndDate());

                LocalDateTime startDateTime = startDate.atStartOfDay();
                LocalDateTime endDateTime = endDate.atTime(23, 59, 59);

                recordWrapper.ge(QualityInspectionApplication::getProductionDate, startDateTime);
                recordWrapper.le(QualityInspectionApplication::getProductionDate, endDateTime);
            } else {
                LocalDate startDate = LocalDate.parse(standingBookQuery.getStartDate() + "-01");
                LocalDate endDate = LocalDate.parse(standingBookQuery.getEndDate() + "-01");

                LocalDateTime startDateTime = startDate.withDayOfMonth(1).atStartOfDay();
                LocalDateTime endDateTime = endDate.atStartOfDay().plusMonths(1).minusSeconds(1);

                log.info(startDateTime.toString());
                log.info(endDateTime.toString());

                recordWrapper.ge(QualityInspectionApplication::getProductionDate, startDateTime);
                recordWrapper.le(QualityInspectionApplication::getProductionDate, endDateTime);
            }
        } else {
            LocalDate currentDate = LocalDate.now();

            LocalDateTime startDateTime = currentDate.withDayOfMonth(1).atStartOfDay();
            LocalDateTime endDateTime = currentDate.withDayOfMonth(currentDate.lengthOfMonth()).atTime(23, 59, 59);

            recordWrapper.ge(QualityInspectionApplication::getProductionDate, startDateTime);
            recordWrapper.le(QualityInspectionApplication::getProductionDate, endDateTime);
        }
        recordWrapper.eq(BasicInformation::getIsDeleted, false);
        recordWrapper.and(!StrUtil.equals(standingBookQuery.getFinalResult(), "[]"), wrapper -> wrapper
                .and(qualified, i -> i.notLike(BasicInformation::getResult, "不合格")
                        .notLike(BasicInformation::getResult, "不符合")
                        .notLike(BasicInformation::getResult, "N"))
                // .notLike(qualified, BasicInformation::getResult, "不")
                .or()
                .like(unqualified, BasicInformation::getResult, "符合")
                .or()
                .like(unqualifiedY, BasicInformation::getResult, "Y")
                .or()
                .like(unqualifiedN, BasicInformation::getResult, "N")
                .or()
                .like(failed, BasicInformation::getResult, "不合格"));
        recordWrapper.orderByAsc(BasicInformation::getSamplingDate, BasicInformation::getQualityInspectionBatch);
        List<StandingBookDataDTO> standingBookDataDTOList = basicInformationMapper
                .selectJoinList(StandingBookDataDTO.class, recordWrapper);
        /*
         * Comparator<StandingBookDataDTO> comparator = Comparator
         * .comparing(StandingBookDataDTO::getSamplingDate)
         * .thenComparing(StandingBookDataDTO::getQualityInspectionBatch)
         * .thenComparingInt(s -> s.getQualityInspectionBatch().length());
         * standingBookDataDTOList.sort(comparator);
         */
        log.info("[台账]质检项目数据查询:结束");
        log.info("[台账]台账表头&数据构建:开始");
        Set<String> itemSet = new HashSet<>();
        standingBookDataDTOList.forEach(standingBookDataDTO -> {
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("id", standingBookDataDTO.getId());
            dataMap.put("mergedUnSubmittedRecordId", standingBookDataDTO.getMergedUnSubmittedRecordId());
            dataMap.put("qualityInspectionBatch", standingBookDataDTO.getQualityInspectionBatch());
            dataMap.put("productionBatch", standingBookDataDTO.getProductionBatch());
            dataMap.put("customerName", standingBookDataDTO.getCustomerName());
            dataMap.put("productType", standingBookDataDTO.getProductType());
            dataMap.put("tankBatch", standingBookDataDTO.getTankBatch());
            if (StrUtil.isNotBlank(standingBookDataDTO.getPackagingSpecification())
                    && StrUtil.isNotBlank(standingBookDataDTO.getQuantity())) {
                dataMap.put("packagingSpecification", standingBookDataDTO.getPackagingSpecification());
                dataMap.put("quantity", standingBookDataDTO.getQuantity());
                BigDecimal packagingSpecification = new BigDecimal(standingBookDataDTO.getPackagingSpecification());
                BigDecimal quantity = new BigDecimal(standingBookDataDTO.getQuantity());
                BigDecimal stairs = packagingSpecification.multiply(quantity).divide(new BigDecimal("1000"), 3,
                        RoundingMode.HALF_UP);
                dataMap.put("stairs", stairs);
            }
            dataMap.put("samplingDate", standingBookDataDTO.getSamplingDate());
            dataMap.put("inspectorLeader", standingBookDataDTO.getAuditor1());
            dataMap.put("inspector", standingBookDataDTO.getInspector1() + "/" + standingBookDataDTO.getInspector2()
                    + "/" + standingBookDataDTO.getAuditor1() + "/" + standingBookDataDTO.getAuditor2());
            dataMap.put("result", standingBookDataDTO.getResult());
            StringBuilder samplingInspectionBuilder = new StringBuilder();

            for (Map.Entry<String, Map<String, String>> entry : itemMap.entrySet()) {
                String matchingName = entry.getKey();
                log.info("[台账]检测项目:" + matchingName);
                QualityInspectionHandler qualityInspectionHandler = QualityInspectionHandlerFactory
                        .getInvokeStrategy(StrUtil.split(matchingName, ",").get(0));
                if (ObjectUtil.isNotNull(qualityInspectionHandler)) {
                    qualityInspectionHandler.standingBookDataFillIn(standingBookDataDTO, dataMap,
                            samplingInspectionBuilder, itemSet, matchingName);
                }
            }
            // 处理抽检信息
            if (StrUtil.isNotEmpty(samplingInspectionBuilder)) {
                dataMap.put("samplingResult", samplingInspectionBuilder.substring(1));

                Map<String, List<String>> samplingDataMap = new HashMap<>();
                @SuppressWarnings("unchecked")
                List<String> samplingList = (List<String>) Convert
                        .toList(samplingInspectionBuilder.substring(1).split(";"));

                samplingList.forEach(str -> {
                    String[] split = str.split(":");
                    if (samplingDataMap.get(split[0]) != null) {
                        List<String> list = samplingDataMap.get(split[0]);
                        list.add(split[1]);
                        samplingDataMap.put(split[0], list);
                    } else {
                        List<String> samplingDataList = new ArrayList<>();
                        samplingDataList.add(split[1]);
                        samplingDataMap.put(split[0], samplingDataList);
                    }
                });
                StringBuilder samplingItemBuilder = new StringBuilder();
                StringBuilder samplingNumberBuilder = new StringBuilder();
                StringBuilder samplingDataBuilder = new StringBuilder();
                samplingDataMap.forEach((key, value) -> {
                    samplingItemBuilder.append("] [").append(key);
                    samplingNumberBuilder.append("] [").append(value.size());
                    samplingDataBuilder.append("] [");
                    StringBuilder samplingItemDataBuilder = new StringBuilder();
                    value.forEach(samplingData -> samplingItemDataBuilder.append(",").append(samplingData));
                    samplingDataBuilder.append(samplingItemDataBuilder.substring(1));
                });

                dataMap.put("samplingItem", samplingItemBuilder.append("]").substring(2));
                dataMap.put("samplingNumber", samplingNumberBuilder.append("]").substring(2));
                dataMap.put("samplingData", samplingDataBuilder.append("]").substring(2));
            }
            dataMapList.add(dataMap);
        });

        // 按年获取集团指标
        log.info("[台账]质检申请查询:开始");
        LambdaQueryWrapper<QualityInspectionApplication> requirementApplicationWrapper = new LambdaQueryWrapper<QualityInspectionApplication>()
                .eq(QualityInspectionApplication::getLinkId, standingBookQuery.getLinkId())
                .eq(!StrUtil.startWith(standingBookQuery.getProductCategoryNumber(), "0105"),
                        QualityInspectionApplication::getProductCategoryNumber,
                        standingBookQuery.getProductCategoryNumber())
                .likeRight(StrUtil.startWith(standingBookQuery.getProductCategoryNumber(), "0105"),
                        QualityInspectionApplication::getProductCategoryNumber,
                        standingBookQuery.getProductCategoryNumber());
        // 查询最近一年的质检申请
        // 当前日期作为查询的结束日期
        LocalDate endDate = LocalDate.now();
        // 计算一年前的日期作为查询的起始日期
        LocalDate startDate = endDate.minusYears(1);
        LocalDateTime startDateTime = startDate.atTime(0, 0, 0);
        LocalDateTime desiredDate = LocalDateTime.of(2023, 12, 1, 0, 0, 0);
        boolean isBeforeDesiredDate = startDateTime.isBefore(desiredDate);
        LocalDateTime endDateTime = endDate.plusDays(1).atTime(0, 0, 0).minusSeconds(1);
        requirementApplicationWrapper
                .ge(isBeforeDesiredDate, QualityInspectionApplication::getApplicationDate, desiredDate)
                .ge(!isBeforeDesiredDate, QualityInspectionApplication::getApplicationDate, startDateTime)
                .le(QualityInspectionApplication::getApplicationDate, endDateTime);
        List<QualityInspectionApplication> requirementApplicationList = qualityInspectionApplicationMapper
                .selectList(requirementApplicationWrapper);

        List<String> itemList = new ArrayList<>(itemSet);
        String generalRequirement = null;
        if (CollUtil.isNotEmpty(requirementApplicationList)) {
            generalRequirement = requirementApplicationList.get(0).getGeneralRequirement();
            // 获取通用指标时屏蔽精品M
            List<QualityInspectionApplication> reversedList = CollUtil.reverseNew(requirementApplicationList);
            for (QualityInspectionApplication application : reversedList) {
                if (!StrUtil.contains(application.getProductType(), "精品")) {
                    generalRequirement = application.getGeneralRequirement();
                    break;
                }
            }
            if (StrUtil.isEmpty(generalRequirement)) {
                generalRequirement = requirementApplicationList.get(requirementApplicationList.size() - 1)
                        .getGeneralRequirement();
            }
        }

        // 预分散体通用指标以硫磺S-80为基准
        if (StrUtil.startWith(standingBookQuery.getProductCategoryNumber(), "0105")) {
            generalRequirement = "加热减量|加热减量|1:≤0.60%;外观|外观|1:黄色颗粒;比重|比重|1:1.50±0.10;灰分|灰分|1:≤5.00%;硫含量(预分散体)|硫含量(预分散体)|1:80.00±2.00%";
        }
        Map<String, Map<String, String>> generalMap = getRequirementMap(generalRequirement);
        generalMap.remove("分子式");
        generalMap.remove("分子量");
        generalMap.remove("备注");
        generalMap.remove("有效期");
        generalMap.remove("客户产品代码");
        generalMap.remove("规格依据");
        List<String> generalItemList = new ArrayList<>();
        CollUtil.newArrayList(generalMap.keySet()).forEach(generalItem -> {
            String name;
            if (generalItem.split(",").length > 1) {
                name = generalItem.split(",")[0] + "(" + generalItem.split(",")[1] + ")";
            } else {
                name = generalItem;
            }
            generalItemList.add(name);
        });
        List<String> groupItemList = (List<String>) CollUtil.subtract(itemList, generalItemList);
        // 排序相关
        List<String> generalOrderList = new ArrayList<>();
        LambdaQueryWrapper<ProductTypeForSort> productTypeForSortLambdaQueryWrapper = new LambdaQueryWrapper<ProductTypeForSort>()
                .eq(!StrUtil.startWith(standingBookQuery.getProductCategoryNumber(), "0105"),
                        ProductTypeForSort::getProductCategoryNumber, standingBookQuery.getProductCategoryNumber())
                .eq(StrUtil.startWith(standingBookQuery.getProductCategoryNumber(), "0105"),
                        ProductTypeForSort::getProductCategoryNumber, "0105");
        List<ProductTypeForSort> productTypeForSorts = productTypeForSortMapper
                .selectList(productTypeForSortLambdaQueryWrapper);
        if (!productTypeForSorts.isEmpty()) {
            LambdaQueryWrapper<ItemPriorityForSort> itemPriorityForSortLambdaQueryWrapper = new LambdaQueryWrapper<>();
            itemPriorityForSortLambdaQueryWrapper.eq(ItemPriorityForSort::getProductTypeId,
                    productTypeForSorts.get(0).getId());
            List<ItemPriorityForSort> itemPriorityForSorts = itemPriorityForSortMapper
                    .selectList(itemPriorityForSortLambdaQueryWrapper);
            List<ItemPriorityForSort> sortedList = itemPriorityForSorts.stream()
                    .sorted(Comparator.comparingInt(ItemPriorityForSort::getPriority).reversed()
                            .thenComparing(ItemPriorityForSort::getItem))
                    .collect(Collectors.toList());
            sortedList.forEach(itemPriorityForSort -> generalOrderList.add(itemPriorityForSort.getItem()));
        }
        // 自定义比较器，优先按照orderList排序，orderList中不包括的项目置底
        Comparator<String> generalItemComparator = (o1, o2) -> {
            // 通过正则表达式提取括号前的内容
            String name1 = o1.split(",")[0];
            String name2 = o2.split(",")[0];
            String regex = "^(.*?)\\(";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher1 = pattern.matcher(name1);
            Matcher matcher2 = pattern.matcher(name2);
            String itemCategory1 = matcher1.find() ? matcher1.group(1) : name1;
            String itemCategory2 = matcher2.find() ? matcher2.group(1) : name2;
            // 如果都在 orderList 中，按照 orderList 排序；否则，未在 orderList 中的排在后面
            if (generalOrderList.contains(itemCategory1) && generalOrderList.contains(itemCategory2)) {
                return Integer.compare(generalOrderList.indexOf(itemCategory1),
                        generalOrderList.indexOf(itemCategory2));
            } else if (generalOrderList.contains(itemCategory1)) {
                return -1;
            } else if (generalOrderList.contains(itemCategory2)) {
                return 1;
            } else {
                return o1.compareTo(o2);
            }
        };
        Collections.sort(generalItemList);
        generalItemList.sort(generalItemComparator);
        // 集团指标排序相关
        List<String> groupOrderList = new ArrayList<>();
        List<CommonItemPriority> commonItemPriorityList = commonItemPriorityMapper.selectList(null);
        List<CommonItemPriority> sortedList = commonItemPriorityList.stream()
                .sorted(Comparator.comparingInt(CommonItemPriority::getPriority).reversed()
                        .thenComparing(CommonItemPriority::getItem))
                .collect(Collectors.toList());
        sortedList.forEach(itemPriorityForSort -> groupOrderList.add(itemPriorityForSort.getItem()));
        // 自定义比较器，优先按照orderList排序，orderList中不包括的项目置底
        Comparator<String> groupItemComparator = (o1, o2) -> {
            // 通过正则表达式提取括号前的内容
            String name1 = o1.split(",")[0];
            String name2 = o2.split(",")[0];
            String regex = "^(.*?)\\(";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher1 = pattern.matcher(name1);
            Matcher matcher2 = pattern.matcher(name2);
            String itemCategory1 = matcher1.find() ? matcher1.group(1) : name1;
            String itemCategory2 = matcher2.find() ? matcher2.group(1) : name2;
            // 如果都在 orderList 中，按照 orderList 排序；否则，未在 orderList 中的排在后面
            if (groupOrderList.contains(itemCategory1) && groupOrderList.contains(itemCategory2)) {
                return Integer.compare(groupOrderList.indexOf(itemCategory1), groupOrderList.indexOf(itemCategory2));
            } else if (groupOrderList.contains(itemCategory1)) {
                return -1;
            } else if (groupOrderList.contains(itemCategory2)) {
                return 1;
            } else {
                return o1.compareTo(o2);
            }
        };
        Collections.sort(groupItemList);
        groupItemList.sort(groupItemComparator);

        // 特殊处理：如果产品名称包含"粒"，将包含"油含量"的item放到最前面
        if (CollUtil.isNotEmpty(applicationList) && applicationList.get(0).getProductType().contains("粒")) {
            List<String> oilItems = new ArrayList<>();
            List<String> otherItems = new ArrayList<>();
            
            // 分离包含"油含量"的item
            for (String item : groupItemList) {
                if (item.contains("油含量")) {
                    oilItems.add(item);
                } else {
                    otherItems.add(item);
                }
            }
            
            // 重新组合list，油含量在前，其他在后
            if (!oilItems.isEmpty()) {
                groupItemList = new ArrayList<>();
                groupItemList.addAll(oilItems);
                groupItemList.addAll(otherItems);
            }
        }

        generalItemList.forEach(item -> {
            Map<String, Object> columnMap = new HashMap<>();
            columnMap.put("label", item);
            columnMapList.add(columnMap);
        });
        groupItemList.forEach(item -> {
            Map<String, Object> columnMap = new HashMap<>();
            columnMap.put("label", item);
            groupColumnMapList.add(columnMap);
        });
        log.info("[台账]台账表头&数据构建:结束");

        Map<String, Map<String, Object>> mergedMap = new HashMap<>();
        // 合并相同质检批号记录
        for (Map<String, Object> map : dataMapList) {
            // String mergedUnSubmittedRecordId =
            // map.get("mergedUnSubmittedRecordId").toString();
            String qualityInspectionBatch = map.get("qualityInspectionBatch").toString();
            qualityInspectionBatch = StrUtil.removeAll(qualityInspectionBatch, "[调整]");
            qualityInspectionBatch = StrUtil.removeAll(qualityInspectionBatch, "[放行]");
            qualityInspectionBatch = StrUtil.removeAll(qualityInspectionBatch, "[否决]");
            if (StrUtil.isNotBlank(qualityInspectionBatch)) {
                if (mergedMap.containsKey(qualityInspectionBatch)) {
                    Map<String, Object> existingMap = mergedMap.get(qualityInspectionBatch);
                    String productionBatch = (String) existingMap.get("productionBatch");
                    String newProductionBatch = (String) map.get("productionBatch");
                    String customerName = (String) existingMap.get("customerName");
                    String newCustomerName = (String) map.get("customerName");
                    String tankBatch = (String) existingMap.get("tankBatch");
                    String newTankBatch = (String) map.get("tankBatch");
                    String packagingSpecification = (String) existingMap.get("packagingSpecification");
                    String newPackagingSpecification = (String) map.get("packagingSpecification");
                    String quantity = (String) existingMap.get("quantity");
                    String newQuantity = (String) map.get("quantity");
                    BigDecimal stairs = (BigDecimal) existingMap.get("stairs");
                    BigDecimal newStairs = (BigDecimal) map.get("stairs");
                    String result = (String) existingMap.get("result");
                    String newResult = (String) map.get("result");

                    String samplingItem = (String) existingMap.get("samplingItem");
                    String samplingNumber = (String) existingMap.get("samplingNumber");
                    String samplingData = (String) existingMap.get("samplingData");

                    existingMap.putAll(map);
                    existingMap.put("productionBatch", productionBatch + "/" + newProductionBatch);
                    existingMap.put("customerName", customerName + "/" + newCustomerName);
                    existingMap.put("tankBatch", tankBatch + "/" + newTankBatch);
                    existingMap.put("packagingSpecification", packagingSpecification + "/" + newPackagingSpecification);
                    existingMap.put("quantity", quantity + "/" + newQuantity);
                    existingMap.put("stairs", NumberUtil.round(stairs.add(newStairs), 3));
                    existingMap.put("result", result + "/" + newResult);

                    if (StrUtil.isNotEmpty(samplingItem) && StrUtil.isNotEmpty((String) map.get("samplingItem"))) {
                        if (samplingItem.length() > ((String) map.get("samplingItem")).length()) {
                            existingMap.put("samplingItem", samplingItem);
                            existingMap.put("samplingNumber", samplingNumber);
                            existingMap.put("samplingData", samplingData);
                        }
                    }
                } else {
                    mergedMap.put(qualityInspectionBatch, new HashMap<>(map));
                }
            }
        }
        List<Map<String, Object>> resultDataMapList;
        if (ObjectUtil.isNotNull(standingBookQuery.getNeedToMerge()) && standingBookQuery.getNeedToMerge()) {
            resultDataMapList = new ArrayList<>(mergedMap.values());
        } else {
            resultDataMapList = dataMapList;
        }

        resultDataMapList.sort(Comparator
                .comparing((Map<String, Object> map) -> (Date) map.get("samplingDate"))
                .thenComparing(map -> StrUtil.removeAny((String) map.get("qualityInspectionBatch"), "[复]", "[放行]",
                        "[调整]", "[否决]"))
                .thenComparing(map -> ((String) map.get("qualityInspectionBatch")).length()));
        resultMap.put("dataList", resultDataMapList);
        if (CollUtil.isEmpty(columnMapList)) {
            resultMap.put("columnList", groupColumnMapList);
            List<Map<String, Object>> emptyMapList = new ArrayList<>();
            resultMap.put("groupColumnList", emptyMapList);
        } else {
            resultMap.put("columnList", columnMapList);
            resultMap.put("groupColumnList", groupColumnMapList);
        }

        log.info("[台账]台账查询:结束");
        return resultMap;
    }

    @Override
    public List<Map<String, Object>> getItemData(ItemDataQuery itemDataQuery) {
        List<Map<String, Object>> list = new ArrayList<>();

        String item = itemDataQuery.getItem().split(",")[0];

        QualityInspectionHandler invokeStrategy = QualityInspectionHandlerFactory.getInvokeStrategy(item);
        if (invokeStrategy == null) {
            // 如果有2个括号，去掉第2个
            String pattern = "(.*)(?=\\()";
            Pattern r = Pattern.compile(pattern);
            Matcher m = r.matcher(item);
            String result = "";
            if (m.find()) {
                result = m.group(0);
            }
            invokeStrategy = QualityInspectionHandlerFactory.getInvokeStrategy(result);
            if (invokeStrategy != null) {
                log.info("获取处理类：" + itemDataQuery.getItem());
                invokeStrategy.itemDataFillIn(itemDataQuery.getId(), list);
            }
        } else {
            log.info("获取处理类：" + itemDataQuery.getItem());
            invokeStrategy.itemDataFillIn(itemDataQuery.getId(), list);
        }
        return list;
    }

    @Override
    public List<Map<String, Object>> getSamplingItemData(ItemDataQuery itemDataQuery) {
        List<Map<String, Object>> list = new ArrayList<>();
        String item = itemDataQuery.getItem().split(",")[0];

        QualityInspectionHandler invokeStrategy = QualityInspectionHandlerFactory.getInvokeStrategy(item);
        if (invokeStrategy == null) {
            // 如果有2个括号，去掉第2个
            String pattern = "(.*)(?=\\()";
            Pattern r = Pattern.compile(pattern);
            Matcher m = r.matcher(item);
            String result = "";
            if (m.find()) {
                result = m.group(0);
            }
            invokeStrategy = QualityInspectionHandlerFactory.getInvokeStrategy(result);
            if (invokeStrategy != null) {
                log.info("获取处理类：" + itemDataQuery.getItem());
                invokeStrategy.itemDataFillIn(itemDataQuery.getId(), list);
            }
        } else {
            log.info("获取处理类：" + itemDataQuery.getItem());
            invokeStrategy.itemDataFillIn(itemDataQuery.getId(), list);
        }
        return list;
    }

    @Override
    public String standingBookExcelCreate(StandingBookQuery standingBookQuery) {
        // 查询用户信息
        User user = userMapper.selectById(standingBookQuery.getUserId());
        // 构建查询条件，获取目标公司全部产品类型
        MPJLambdaWrapper<BasicInformation> recordWrapper = new MPJLambdaWrapper<BasicInformation>()
                .select(BasicInformation::getProductCategoryNumber, BasicInformation::getProductNumber,
                        BasicInformation::getProductType)
                .leftJoin(BasicInformationNotSaved.class, BasicInformationNotSaved::getId,
                        BasicInformation::getNotSubmittedRecordId)
                .leftJoin(QualityInspectionApplication.class, QualityInspectionApplication::getId,
                        BasicInformation::getApplicationId)
                .isNotNull(BasicInformationNotSaved::getMergedUnSubmittedRecordId)
                .eq(BasicInformation::getLinkId, standingBookQuery.getLinkId())
                .isNotNull(BasicInformation::getProductCategoryNumber)
                .isNotNull(BasicInformation::getProductNumber)
                .notLike(BasicInformation::getProductType, "油粒")
                .apply("t.product_category_number not like {0}", "0105%");
        // 处理日期范围查询条件
        if (StrUtil.isNotBlank(standingBookQuery.getStartDate())
                && StrUtil.isNotBlank(standingBookQuery.getEndDate())) {
            if (standingBookQuery.getDateRange()) {
                // 精确日期范围查询
                LocalDate startDate = LocalDate.parse(standingBookQuery.getStartDate());
                LocalDate endDate = LocalDate.parse(standingBookQuery.getEndDate());

                LocalDateTime startDateTime = startDate.atStartOfDay();
                LocalDateTime endDateTime = endDate.atTime(23, 59, 59);

                recordWrapper.ge(QualityInspectionApplication::getProductionDate, startDateTime);
                recordWrapper.le(QualityInspectionApplication::getProductionDate, endDateTime);
            } else {
                // 按月查询
                LocalDate startDate = LocalDate.parse(standingBookQuery.getStartDate() + "-01");
                LocalDate endDate = LocalDate.parse(standingBookQuery.getEndDate() + "-01");

                LocalDateTime startDateTime = startDate.withDayOfMonth(1).atStartOfDay();
                LocalDateTime endDateTime = endDate.atStartOfDay().plusMonths(1).minusSeconds(1);

                recordWrapper.ge(QualityInspectionApplication::getProductionDate, startDateTime);
                recordWrapper.le(QualityInspectionApplication::getProductionDate, endDateTime);
            }
        } else {
            // 默认查询当前月数据
            LocalDate currentDate = LocalDate.now();

            LocalDateTime startDateTime = currentDate.withDayOfMonth(1).atStartOfDay();
            LocalDateTime endDateTime = currentDate.withDayOfMonth(currentDate.lengthOfMonth()).atTime(23, 59, 59);

            recordWrapper.ge(QualityInspectionApplication::getProductionDate, startDateTime);
            recordWrapper.le(QualityInspectionApplication::getProductionDate, endDateTime);
        }
        recordWrapper.groupBy(BasicInformation::getProductCategoryNumber);

        // 获取产品类型列表并按优先级排序
        List<BasicInformation> basicInformationList = basicInformationMapper.selectJoinList(BasicInformation.class,
                recordWrapper);
        List<String> orderList = new ArrayList<>();
        LambdaQueryWrapper<ProductTypeForStandingBook> lambdaQueryWrapper = new LambdaQueryWrapper<ProductTypeForStandingBook>()
                .isNotNull(ProductTypeForStandingBook::getPriority);
        List<ProductTypeForStandingBook> productTypeForSortList = productTypeForStandingBookMapper
                .selectList(lambdaQueryWrapper);
        List<ProductTypeForStandingBook> sortedList = productTypeForSortList.stream()
                .sorted(Comparator.comparingInt(ProductTypeForStandingBook::getPriority).reversed()
                        .thenComparing(ProductTypeForStandingBook::getProductCategoryNumber))
                .collect(Collectors.toList());
        sortedList.forEach(productTypeForSort -> orderList.add(productTypeForSort.getProductCategoryNumber()));
        log.info(orderList.toString());

        Comparator<BasicInformation> itemComparator = (o1, o2) -> {
            String productType1 = o1.getProductCategoryNumber();
            String productType2 = o2.getProductCategoryNumber();
            if (orderList.contains(productType1) && orderList.contains(productType2)) {
                return Integer.compare(orderList.indexOf(productType1), orderList.indexOf(productType2));
            } else if (orderList.contains(productType1)) {
                return -1;
            } else if (orderList.contains(productType2)) {
                return 1;
            } else {
                return productType1.compareTo(productType2);
            }
        };
        basicInformationList.sort(itemComparator);

        // 添加预分散体特殊处理
        if (standingBookQuery.getLinkId() == 3) {
            BasicInformation preDispersed = new BasicInformation();
            preDispersed.setProductCategoryNumber("0105");
            preDispersed.setProductType("预分散体");
            basicInformationList.add(preDispersed);
        }

        // 创建Excel文件目录
        String folderPath = standingBookPath + user.getId() + "_" + user.getUsername() + "\\";
        File folder = new File(folderPath);
        if (!folder.exists()) {
            boolean created = folder.mkdirs();
            if (created) {
                log.info("文件夹创建成功");
            } else {
                log.info("文件夹创建失败");
            }
        }
        String fileName = getFileName(standingBookQuery, folderPath);
        // 使用FastExcel创建Excel文件
        try (ExcelWriter excelWriter = FastExcel.write(fileName).build()) {
            for (int i = 0; i < basicInformationList.size(); i++) {
                BasicInformation basicInformation = basicInformationList.get(i);
                String productType = basicInformation.getProductType();
                log.info("当前产品类型:" + productType);

                // 设置查询参数
                standingBookQuery.setProductCategoryNumber(basicInformation.getProductCategoryNumber());
                standingBookQuery.setFinalResult("[1,[2,3],[2,4],0]");

                // 初始化表头和数据列表
                List<List<String>> writeHeadList = new ArrayList<>();
                List<List<String>> writeDataList = new ArrayList<>();

                // 添加基础表头
                List<String> qualityInspectionBatch = new ArrayList<>();
                qualityInspectionBatch.add("qualityInspectionBatch");
                writeHeadList.add(qualityInspectionBatch);
                List<String> productionBatch = new ArrayList<>();
                productionBatch.add("productionBatch");
                writeHeadList.add(productionBatch);
                // 处理特殊产品类型（预分散体）
                if (StrUtil.startWith(basicInformation.getProductCategoryNumber(), "0105")) {
                    List<String> productTypeColumn = new ArrayList<>();
                    productTypeColumn.add("productType");
                    writeHeadList.add(productTypeColumn);
                }
                List<String> tankBatch = new ArrayList<>();
                tankBatch.add("tankBatch");
                writeHeadList.add(tankBatch);

                // 按年获取集团指标
                log.info("[台账]质检申请查询:开始");
                LambdaQueryWrapper<QualityInspectionApplication> applicationWrapper = new LambdaQueryWrapper<QualityInspectionApplication>()
                        .eq(QualityInspectionApplication::getLinkId, standingBookQuery.getLinkId())
                        .eq(!StrUtil.startWith(standingBookQuery.getProductCategoryNumber(), "0105"),
                                QualityInspectionApplication::getProductCategoryNumber,
                                standingBookQuery.getProductCategoryNumber())
                        .likeRight(StrUtil.startWith(standingBookQuery.getProductCategoryNumber(), "0105"),
                                QualityInspectionApplication::getProductCategoryNumber,
                                standingBookQuery.getProductCategoryNumber());
                // 查询最近一年的质检申请
                // 当前日期作为查询的结束日期
                LocalDate endDate = LocalDate.now();
                // 计算一年前的日期作为查询的起始日期
                LocalDate startDate = endDate.minusYears(1);
                LocalDateTime startDateTime = startDate.atTime(0, 0, 0);
                LocalDateTime desiredDate = LocalDateTime.of(2023, 12, 1, 0, 0, 0);
                boolean isBeforeDesiredDate = startDateTime.isBefore(desiredDate);
                LocalDateTime endDateTime = endDate.plusDays(1).atTime(0, 0, 0).minusSeconds(1);
                applicationWrapper
                        .ge(isBeforeDesiredDate, QualityInspectionApplication::getApplicationDate, desiredDate)
                        .ge(!isBeforeDesiredDate, QualityInspectionApplication::getApplicationDate, startDateTime)
                        .le(QualityInspectionApplication::getApplicationDate, endDateTime);
                List<QualityInspectionApplication> applicationList = qualityInspectionApplicationMapper
                        .selectList(applicationWrapper);
                // 获取质检数据并处理指标
                Map<String, Map<String, String>> itemMap = new HashMap<>();
                applicationList.forEach(application -> {
                    Map<String, Map<String, String>> generalMap = getRequirementMap(
                            application.getGeneralRequirement());
                    Map<String, Map<String, String>> groupMap = getRequirementMap(application.getGroupRequirement());
                    Map<String, Map<String, String>> internalControlMap = getRequirementMap(
                            application.getInternalControlRequirement());

                    generalMap.putAll(getRequirementMap(application.getRawGeneralRequirement()));
                    groupMap.putAll(getRequirementMap(application.getRawGroupRequirement()));
                    internalControlMap.putAll(getRequirementMap(application.getRawInternalControlRequirement()));

                    itemMap.putAll(generalMap);
                    itemMap.putAll(groupMap);
                    itemMap.putAll(internalControlMap);
                });
                itemMap.remove("分子式");
                itemMap.remove("分子量");
                itemMap.remove("备注");
                itemMap.remove("有效期");
                itemMap.remove("保质期");
                itemMap.remove("客户产品代码");
                itemMap.remove("规格依据");
                String generalRequirement = null;
                if (CollUtil.isNotEmpty(applicationList)) {
                    // TODO 获取通用指标时屏蔽精品M
                    List<QualityInspectionApplication> reversedList = CollUtil.reverseNew(applicationList);
                    for (QualityInspectionApplication application : reversedList) {
                        if (!StrUtil.contains(application.getProductType(), "精品")) {
                            generalRequirement = application.getGeneralRequirement();
                            break;
                        }
                    }
                    if (StrUtil.isEmpty(generalRequirement)) {
                        generalRequirement = applicationList.get(applicationList.size() - 1).getGeneralRequirement();
                    }
                }
                // 预分散体通用指标以硫磺S-80为基准
                if (StrUtil.startWith(standingBookQuery.getProductCategoryNumber(), "0105")) {
                    generalRequirement = "加热减量|加热减量|1:≤0.60%;外观|外观|1:黄色颗粒;比重|比重|1:1.50±0.10;灰分|灰分|1:≤5.00%;硫含量(预分散体)|硫含量(预分散体)|1:80.00±2.00%";
                }
                Map<String, Map<String, String>> generalMap = getRequirementMap(generalRequirement);
                generalMap.remove("分子式");
                generalMap.remove("分子量");
                generalMap.remove("备注");
                generalMap.remove("有效期");
                generalMap.remove("保质期");
                generalMap.remove("客户产品代码");
                generalMap.remove("规格依据");
                List<String> itemList = new ArrayList<>();
                CollUtil.newArrayList(itemMap.keySet()).forEach(item -> {
                    String name;
                    if (item.split(",").length > 1) {
                        name = item.split(",")[0] + "(" + item.split(",")[1] + ")";
                    } else {
                        name = item;
                    }
                    itemList.add(name);
                });
                List<String> generalItemList = new ArrayList<>();
                CollUtil.newArrayList(generalMap.keySet()).forEach(generalItem -> {
                    String name;
                    if (generalItem.split(",").length > 1) {
                        name = generalItem.split(",")[0] + "(" + generalItem.split(",")[1] + ")";
                    } else {
                        name = generalItem;
                    }
                    generalItemList.add(name);
                });
                List<String> groupItemList = (List<String>) CollUtil.subtract(itemList, generalItemList);
                // 排序相关
                List<String> stringOrderList = new ArrayList<>();
                // 查询通用检测项目优先级
                List<CommonItemPriority> itemPriorityForSorts = commonItemPriorityMapper.selectList(null);
                // 检测项目排序
                List<CommonItemPriority> sortedStringList = itemPriorityForSorts.stream()
                        .sorted(Comparator.comparingInt(CommonItemPriority::getPriority).reversed()
                                .thenComparing(CommonItemPriority::getItem))
                        .collect(Collectors.toList());
                sortedStringList.forEach(itemPriorityForSort -> stringOrderList.add(itemPriorityForSort.getItem()));
                // 自定义比较器，优先按照orderList排序，orderList中不包括的项目置底
                Comparator<String> itemStringComparator = (o1, o2) -> {
                    // 通过正则表达式提取括号前的内容
                    String name1 = o1.split(",")[0];
                    String name2 = o2.split(",")[0];
                    String regex = "^(.*?)\\(";
                    Pattern pattern = Pattern.compile(regex);
                    Matcher matcher1 = pattern.matcher(name1);
                    Matcher matcher2 = pattern.matcher(name2);
                    String itemCategory1 = matcher1.find() ? matcher1.group(1) : name1;
                    String itemCategory2 = matcher2.find() ? matcher2.group(1) : name2;
                    // 如果都在 stringOrderList 中，按照 stringOrderList 排序；否则，未在 stringOrderList 中的排在后面
                    if (stringOrderList.contains(itemCategory1) && stringOrderList.contains(itemCategory2)) {
                        return Integer.compare(stringOrderList.indexOf(itemCategory1),
                                stringOrderList.indexOf(itemCategory2));
                    } else if (stringOrderList.contains(itemCategory1)) {
                        return -1;
                    } else if (stringOrderList.contains(itemCategory2)) {
                        return 1;
                    } else {
                        return o1.compareTo(o2);
                    }
                };
                Collections.sort(groupItemList);
                groupItemList.sort(itemStringComparator);

                // 特殊处理：如果产品名称包含"粒"，将包含"油含量"的item放到最前面
                if (productType.contains("粒")) {
                    List<String> oilItems = new ArrayList<>();
                    List<String> otherItems = new ArrayList<>();
                    
                    // 分离包含"油含量"的item
                    for (String item : groupItemList) {
                        if (item.contains("油含量")) {
                            oilItems.add(item);
                        } else {
                            otherItems.add(item);
                        }
                    }
                    
                    // 重新组合list，油含量在前，其他在后
                    if (!oilItems.isEmpty()) {
                        groupItemList = new ArrayList<>();
                        groupItemList.addAll(oilItems);
                        groupItemList.addAll(otherItems);
                    }
                }

                Map<String, Object> standingBook = getStandingBook(standingBookQuery);
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> columnList = (List<Map<String, Object>>) standingBook.get("columnList");
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> groupColumnList = (List<Map<String, Object>>) standingBook
                        .get("groupColumnList");
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> dataList = (List<Map<String, Object>>) standingBook.get("dataList");

                if (CollUtil.isNotEmpty(groupColumnList)) {
                    groupColumnList = new ArrayList<>();
                    for (String groupItem : groupItemList) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("label", groupItem);
                        groupColumnList.add(map);
                    }
                }

                columnList.forEach(map -> {
                    String label = (String) map.get("label");
                    List<String> head = new ArrayList<>();
                    head.add(label);
                    writeHeadList.add(head);
                });
                List<String> packagingSpecification = new ArrayList<>();
                packagingSpecification.add("packagingSpecification");
                writeHeadList.add(packagingSpecification);
                List<String> quantity = new ArrayList<>();
                quantity.add("quantity");
                writeHeadList.add(quantity);
                List<String> stairs = new ArrayList<>();
                stairs.add("stairs");
                writeHeadList.add(stairs);
                List<String> result = new ArrayList<>();
                result.add("result");
                writeHeadList.add(result);
                List<String> inspectorLeader = new ArrayList<>();
                inspectorLeader.add("inspectorLeader");
                writeHeadList.add(inspectorLeader);
                List<String> customerName = new ArrayList<>();
                customerName.add("customerName");
                writeHeadList.add(customerName);
                groupColumnList.forEach(map -> {
                    String label = (String) map.get("label");
                    List<String> head = new ArrayList<>();
                    head.add(label);
                    writeHeadList.add(head);
                });
                List<String> samplingItem = new ArrayList<>();
                samplingItem.add("samplingItem");
                writeHeadList.add(samplingItem);
                List<String> samplingNumber = new ArrayList<>();
                samplingNumber.add("samplingNumber");
                writeHeadList.add(samplingNumber);
                List<String> samplingData = new ArrayList<>();
                samplingData.add("samplingData");
                writeHeadList.add(samplingData);
                List<String> inspector = new ArrayList<>();
                inspector.add("inspector");
                writeHeadList.add(inspector);

                dataList.forEach(dataMap -> {
                    List<String> writeData = new ArrayList<>();
                    writeHeadList.forEach(writeHead -> {
                        if (ObjectUtil.isNotNull(dataMap.get(writeHead.get(0)))) {
                            String data = dataMap.get(writeHead.get(0)).toString();
                            writeData.add(data);
                        } else {
                            writeData.add("");
                        }
                    });
                    writeDataList.add(writeData);
                });
                // 处理表头中文显示
                writeHeadList.forEach(head -> {
                    if (StrUtil.contains(head.get(0), "qualityInspectionBatch")) {
                        head.set(0, "质检批号");
                    }
                    if (StrUtil.contains(head.get(0), "productionBatch")) {
                        head.set(0, "生产批号");
                    }
                    if (StrUtil.contains(head.get(0), "customerName")) {
                        head.set(0, "客户名称");
                    }
                    if (StrUtil.contains(head.get(0), "productType")) {
                        head.set(0, "产品类型");
                    }
                    if (StrUtil.contains(head.get(0), "tankBatch")) {
                        head.set(0, "罐次");
                    }
                    if (StrUtil.contains(head.get(0), "samplingItem")) {
                        head.set(0, "抽检项目");
                    }
                    if (StrUtil.contains(head.get(0), "samplingNumber")) {
                        head.set(0, "抽检数量");
                    }
                    if (StrUtil.contains(head.get(0), "samplingData")) {
                        head.set(0, "抽检数据");
                    }
                    if (StrUtil.contains(head.get(0), "packagingSpecification")) {
                        head.set(0, "包装规格");
                    }
                    if (StrUtil.contains(head.get(0), "quantity")) {
                        head.set(0, "数量");
                    }
                    if (StrUtil.contains(head.get(0), "stairs")) {
                        head.set(0, "吨数");
                    }
                    if (StrUtil.contains(head.get(0), "result")) {
                        head.set(0, "结论");
                    }
                    if (StrUtil.contains(head.get(0), "inspectorLeader")) {
                        head.set(0, "质检班长");
                    }
                    if (StrUtil.contains(head.get(0), "inspector")) {
                        head.set(0, "质检人员");
                    }
                });

                // 设置Excel样式
                WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
                // 垂直居中,水平居中
                contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
                contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
                contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
                contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
                contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
                // 设置 自动换行
                contentWriteCellStyle.setWrapped(false);
                // 字体策略
                WriteFont contentWriteFont = new WriteFont();
                // 字体大小，加粗，字体类型
                contentWriteFont.setFontName("黑体");
                contentWriteFont.setFontHeightInPoints((short) 12);
                contentWriteCellStyle.setWriteFont(contentWriteFont);
                // 头部样式策略
                WriteCellStyle headWriteCellStyle = new WriteCellStyle();
                headWriteCellStyle.setWrapped(true);
                // 字体策略
                WriteFont headtWriteFont = new WriteFont();
                // 字体大小，加粗，字体类型
                headtWriteFont.setFontName("黑体");
                headtWriteFont.setFontHeightInPoints((short) 14);
                headWriteCellStyle.setWriteFont(headtWriteFont);

                // 创建Excel工作表
                WriteSheet writeSheet = FastExcel
                        .writerSheet(i, productType.replaceFirst(".*?([a-zA-Z0-9])", "$1"))
                        // 字体
                        .registerWriteHandler(
                                new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle))
                        // 行高
                        .registerWriteHandler(new SimpleRowHeightStyleStrategy((short) 90, (short) 25))
                        // 单元格格式（全局）
                        .registerWriteHandler(new ExcelCellWriteHandler())
                        // 列宽
                        .registerWriteHandler(new ColumnWidthStyleStrategy())
                        // 单元格格式（列）
                        .registerWriteHandler(new ColumnDataFormatStrategy())
                        // 单元格格式（单元格）
                        .registerWriteHandler(new CellDataFormatStrategy())
                        .head(writeHeadList)
                        .build();
                // 写入数据
                excelWriter.write(writeDataList, writeSheet);
            }
        }
        return StrUtil.subAfter(fileName, "\\", true);
    }

    @Override
    public String sampleStorageStandingBookExcelCreate(StandingBookQuery standingBookQuery) {
        log.info("[样品存放台账]开始生成样品存放台账");
        
        // 查询用户信息
        User user = userMapper.selectById(standingBookQuery.getUserId());
        
        // 构建查询条件，获取所有产品类型的质检记录
        MPJLambdaWrapper<BasicInformation> recordWrapper = new MPJLambdaWrapper<BasicInformation>()
                .selectAll(BasicInformation.class)
                .leftJoin(BasicInformationNotSaved.class, BasicInformationNotSaved::getId,
                        BasicInformation::getNotSubmittedRecordId)
                .leftJoin(QualityInspectionApplication.class, QualityInspectionApplication::getId,
                        BasicInformation::getApplicationId)
                .isNotNull(BasicInformationNotSaved::getMergedUnSubmittedRecordId)
                .eq(BasicInformation::getLinkId, standingBookQuery.getLinkId())
                .isNotNull(BasicInformation::getProductCategoryNumber)
                .isNotNull(BasicInformation::getProductNumber)
                .eq(BasicInformation::getIsDeleted, false)
                // 剔除带"[复]"字样的记录
                .notLike(BasicInformation::getQualityInspectionBatch, "[复]");
        
        // 处理日期范围查询条件
        if (StrUtil.isNotBlank(standingBookQuery.getStartDate())
                && StrUtil.isNotBlank(standingBookQuery.getEndDate())) {
            if (standingBookQuery.getDateRange()) {
                // 精确日期范围查询
                LocalDate startDate = LocalDate.parse(standingBookQuery.getStartDate());
                LocalDate endDate = LocalDate.parse(standingBookQuery.getEndDate());

                LocalDateTime startDateTime = startDate.atStartOfDay();
                LocalDateTime endDateTime = endDate.atTime(23, 59, 59);

                recordWrapper.ge(QualityInspectionApplication::getProductionDate, startDateTime);
                recordWrapper.le(QualityInspectionApplication::getProductionDate, endDateTime);
            } else {
                // 按月查询
                LocalDate startDate = LocalDate.parse(standingBookQuery.getStartDate() + "-01");
                LocalDate endDate = LocalDate.parse(standingBookQuery.getEndDate() + "-01");

                LocalDateTime startDateTime = startDate.withDayOfMonth(1).atStartOfDay();
                LocalDateTime endDateTime = endDate.atStartOfDay().plusMonths(1).minusSeconds(1);

                recordWrapper.ge(QualityInspectionApplication::getProductionDate, startDateTime);
                recordWrapper.le(QualityInspectionApplication::getProductionDate, endDateTime);
            }
        } else {
            // 默认查询当前月数据
            LocalDate currentDate = LocalDate.now();

            LocalDateTime startDateTime = currentDate.withDayOfMonth(1).atStartOfDay();
            LocalDateTime endDateTime = currentDate.withDayOfMonth(currentDate.lengthOfMonth()).atTime(23, 59, 59);

            recordWrapper.ge(QualityInspectionApplication::getProductionDate, startDateTime);
            recordWrapper.le(QualityInspectionApplication::getProductionDate, endDateTime);
        }
        
        recordWrapper.orderByAsc(BasicInformation::getSamplingDate, BasicInformation::getQualityInspectionBatch);
        
        List<StandingBookDataDTO> standingBookDataDTOList = basicInformationMapper
                .selectJoinList(StandingBookDataDTO.class, recordWrapper);
                
        log.info("[样品存放台账]查询到{}条记录", standingBookDataDTOList.size());

        // 创建Excel文件目录
        String folderPath = standingBookPath + user.getId() + "_" + user.getUsername() + "\\";
        File folder = new File(folderPath);
        if (!folder.exists()) {
            boolean created = folder.mkdirs();
            if (created) {
                log.info("文件夹创建成功");
            } else {
                log.info("文件夹创建失败");
            }
        }
        
        String fileName = getSampleStorageFileName(standingBookQuery, folderPath);
        
        // 按产品分类分组数据
        Map<String, List<StandingBookDataDTO>> groupedData = groupDataByCategory(standingBookDataDTOList);
        
        // 使用FastExcel创建Excel文件
        try (ExcelWriter excelWriter = FastExcel.write(fileName).build()) {
            int sheetIndex = 0;
            
            // 为每个分类创建一个工作表
            for (Map.Entry<String, List<StandingBookDataDTO>> entry : groupedData.entrySet()) {
                String sheetName = entry.getKey();
                List<StandingBookDataDTO> sheetData = entry.getValue();
                
                log.info("[样品存放台账]创建工作表：{}，包含{}条记录", sheetName, sheetData.size());
                
                // 创建工作表
                createWorksheet(excelWriter, sheetIndex, sheetName, sheetData);
                sheetIndex++;
            }
        }
        
        log.info("[样品存放台账]样品存放台账生成完成，共创建{}个工作表", groupedData.size());
        return StrUtil.subAfter(fileName, "\\", true);
    }

    /**
     * 按产品分类分组数据
     */
    private Map<String, List<StandingBookDataDTO>> groupDataByCategory(List<StandingBookDataDTO> dataList) {
        Map<String, List<StandingBookDataDTO>> tempGroupedData = new HashMap<>();
        Map<String, String> sheetNameToCategoryNumber = new HashMap<>();
        
        // 预分散体特殊处理
        List<StandingBookDataDTO> preDispersedData = new ArrayList<>();
        List<StandingBookDataDTO> normalData = new ArrayList<>();
        
        // 分离预分散体和普通产品
        for (StandingBookDataDTO data : dataList) {
            if (StrUtil.isNotBlank(data.getProductCategoryNumber()) && 
                data.getProductCategoryNumber().startsWith("0105")) {
                preDispersedData.add(data);
            } else {
                normalData.add(data);
            }
        }
        
        // 处理预分散体
        if (CollUtil.isNotEmpty(preDispersedData)) {
            tempGroupedData.put("预分散体", preDispersedData);
            sheetNameToCategoryNumber.put("预分散体", "0105");
        }
        
        // 处理普通产品
        Map<String, String> categoryNumberToSheetName = new HashMap<>();
        
        for (StandingBookDataDTO data : normalData) {
            String categoryNumber = data.getProductCategoryNumber();
            if (StrUtil.isBlank(categoryNumber)) {
                continue;
            }
            
            String sheetName = getSheetNameForCategory(categoryNumber, categoryNumberToSheetName);
            if (StrUtil.isNotBlank(sheetName)) {
                tempGroupedData.computeIfAbsent(sheetName, k -> new ArrayList<>()).add(data);
                
                // 记录工作表名称对应的类别编号（用于排序）
                if (!sheetNameToCategoryNumber.containsKey(sheetName)) {
                    // 查询工作表对应的类别编号
                    String workSheetCategoryNumber = getWorkSheetCategoryNumber(sheetName);
                    sheetNameToCategoryNumber.put(sheetName, workSheetCategoryNumber);
                }
            }
        }
        
        // 按照categoryNumber进行排序
        Map<String, List<StandingBookDataDTO>> groupedData = new LinkedHashMap<>();
        
        // 根据categoryNumber排序工作表
        List<Map.Entry<String, String>> sortedEntries = sheetNameToCategoryNumber.entrySet()
            .stream()
            .sorted(Map.Entry.comparingByValue())
            .collect(Collectors.toList());
        
        for (Map.Entry<String, String> entry : sortedEntries) {
            String sheetName = entry.getKey();
            if (tempGroupedData.containsKey(sheetName)) {
                groupedData.put(sheetName, tempGroupedData.get(sheetName));
            }
        }
        
        return groupedData;
    }

    /**
     * 获取工作表对应的类别编号（用于排序）
     */
    private String getWorkSheetCategoryNumber(String sheetName) {
        try {
            // 查询类别名称对应的类别编号
            MaterialCategory category = materialCategoryMapper.selectOne(
                new LambdaQueryWrapper<MaterialCategory>()
                    .eq(MaterialCategory::getCategoryName, sheetName)
                    .eq(MaterialCategory::getCategoryLevel, 3)
            );
            
            if (category != null) {
                return category.getCategoryNumber();
            }
            
            // 如果level=3没找到，尝试查找其他level的记录
            category = materialCategoryMapper.selectOne(
                new LambdaQueryWrapper<MaterialCategory>()
                    .eq(MaterialCategory::getCategoryName, sheetName)
                    .orderByAsc(MaterialCategory::getCategoryLevel)
                    .last("LIMIT 1")
            );
            
            return category != null ? category.getCategoryNumber() : "999999";
            
        } catch (Exception e) {
            log.error("[样品存放台账]查询工作表类别编号时出错：{}", e.getMessage(), e);
            return "999999"; // 默认值，排在最后
        }
    }

    /**
     * 根据产品类别编号获取工作表名称
     */
    private String getSheetNameForCategory(String categoryNumber, Map<String, String> cache) {
        // 先从缓存中查找
        for (Map.Entry<String, String> entry : cache.entrySet()) {
            if (categoryNumber.startsWith(entry.getKey())) {
                return entry.getValue();
            }
        }
        
        try {
            // 1. 查询当前产品类别记录（记录a）
            MaterialCategory recordA = materialCategoryMapper.selectOne(
                new LambdaQueryWrapper<MaterialCategory>()
                    .eq(MaterialCategory::getCategoryNumber, categoryNumber)
            );
            
            if (recordA == null) {
                log.warn("[样品存放台账]未找到产品类别：{}", categoryNumber);
                return null;
            }
            
            // 2. 如果当前类别级别已经是3或更小，直接使用当前类别
            if (recordA.getCategoryLevel() <= 3) {
                cache.put(categoryNumber, recordA.getCategoryName());
                return recordA.getCategoryName();
            }
            
            // 3. 查询上级类别记录（记录b），直到level=3
            MaterialCategory targetCategory = recordA;
            while (targetCategory.getCategoryLevel() > 3) {
                int parentLevel = targetCategory.getCategoryLevel() - 1;
                String searchPrefix = targetCategory.getCategoryNumber();
                MaterialCategory parentCategory = null;
                
                // 逐步缩短类别编号，寻找父类别
                while (searchPrefix.length() > 0 && parentCategory == null) {
                    searchPrefix = searchPrefix.substring(0, searchPrefix.length() - 1);
                    
                    parentCategory = materialCategoryMapper.selectOne(
                        new LambdaQueryWrapper<MaterialCategory>()
                            .eq(MaterialCategory::getCategoryNumber, searchPrefix)
                            .eq(MaterialCategory::getCategoryLevel, parentLevel)
                    );
                }
                
                if (parentCategory == null) {
                    log.warn("[样品存放台账]未找到产品类别{}的父类别", targetCategory.getCategoryNumber());
                    break;
                }
                
                targetCategory = parentCategory;
            }
            
            // 4. 查询同级别的所有类别，并缓存映射关系（level=3的所有类别）
            List<MaterialCategory> sameParentCategories = materialCategoryMapper.selectList(
                new LambdaQueryWrapper<MaterialCategory>()
                    .likeRight(MaterialCategory::getCategoryNumber, targetCategory.getCategoryNumber())
                    .eq(MaterialCategory::getCategoryLevel, recordA.getCategoryLevel())
            );
            
            // 将同级别的所有类别都映射到同一个工作表名称
            String sheetName = targetCategory.getCategoryName();
            for (MaterialCategory category : sameParentCategories) {
                cache.put(category.getCategoryNumber(), sheetName);
            }
            
            return sheetName;
            
        } catch (Exception e) {
            log.error("[样品存放台账]查询产品类别信息时出错：{}", e.getMessage(), e);
            return "其他";
        }
    }

    /**
     * 创建工作表
     */
    private void createWorksheet(ExcelWriter excelWriter, int sheetIndex, String sheetName, 
                               List<StandingBookDataDTO> sheetData) {
        // 初始化表头和数据列表
        List<List<String>> writeHeadList = new ArrayList<>();
        List<List<String>> writeDataList = new ArrayList<>();
        
        // 创建两级表头结构（第一行为主标题，第二行为列名）
        // 质检批号列
        List<String> qualityInspectionBatchHead = new ArrayList<>();
        qualityInspectionBatchHead.add("样品存放台账");  // 第一级标题
        qualityInspectionBatchHead.add("质检批号");    // 第二级标题
        writeHeadList.add(qualityInspectionBatchHead);
        
        // 罐次列
        List<String> tankBatchHead = new ArrayList<>();
        tankBatchHead.add("样品存放台账");  // 第一级标题（会自动合并）
        tankBatchHead.add("罐次");        // 第二级标题
        writeHeadList.add(tankBatchHead);
        
        // 包装规格列
        List<String> packagingSpecificationHead = new ArrayList<>();
        packagingSpecificationHead.add("样品存放台账");  // 第一级标题（会自动合并）
        packagingSpecificationHead.add("包装规格");    // 第二级标题
        writeHeadList.add(packagingSpecificationHead);
        
        // 数量列
        List<String> quantityHead = new ArrayList<>();
        quantityHead.add("样品存放台账");  // 第一级标题（会自动合并）
        quantityHead.add("数量");        // 第二级标题
        writeHeadList.add(quantityHead);
        
        // 客户名称列
        List<String> customerNameHead = new ArrayList<>();
        customerNameHead.add("样品存放台账");  // 第一级标题（会自动合并）
        customerNameHead.add("客户名称");    // 第二级标题
        writeHeadList.add(customerNameHead);
        
        // 质检人员列
        List<String> inspectorHead = new ArrayList<>();
        inspectorHead.add("样品存放台账");  // 第一级标题（会自动合并）
        inspectorHead.add("质检人员");    // 第二级标题
        writeHeadList.add(inspectorHead);
        
        // 样品存放区域列
        List<String> sampleStorageAreaHead = new ArrayList<>();
        sampleStorageAreaHead.add("样品存放台账");    // 第一级标题（会自动合并）
        sampleStorageAreaHead.add("样品存放区域");  // 第二级标题
        writeHeadList.add(sampleStorageAreaHead);

        // 按质检批号分组合并数据
        Map<String, List<StandingBookDataDTO>> groupedByBatch = new LinkedHashMap<>();
        
        for (StandingBookDataDTO dataDTO : sheetData) {
            // 质检批号 - 去除"[放行]"字样
            String qualityInspectionBatch = dataDTO.getQualityInspectionBatch();
            if (StrUtil.isNotBlank(qualityInspectionBatch)) {
                qualityInspectionBatch = StrUtil.removeAll(qualityInspectionBatch, "[放行]");
                qualityInspectionBatch = StrUtil.removeAll(qualityInspectionBatch, "[调整]");
                qualityInspectionBatch = StrUtil.removeAll(qualityInspectionBatch, "[否决]");
            }
            
            if (StrUtil.isNotBlank(qualityInspectionBatch)) {
                groupedByBatch.computeIfAbsent(qualityInspectionBatch, k -> new ArrayList<>()).add(dataDTO);
            }
        }

        // 处理合并后的数据
        for (Map.Entry<String, List<StandingBookDataDTO>> entry : groupedByBatch.entrySet()) {
            String batchNumber = entry.getKey();
            List<StandingBookDataDTO> batchDataList = entry.getValue();
            
            List<String> writeData = new ArrayList<>();
            
            // 质检批号
            writeData.add(batchNumber);
            
            // 收集罐次、包装规格、数量、客户名称数据
            List<String> tankBatches = new ArrayList<>();
            List<String> packagingSpecs = new ArrayList<>();
            List<String> quantities = new ArrayList<>();
            List<String> customerNames = new ArrayList<>();
            
            // 质检人员和样品存放区域取第一条记录的数据
            String inspector = "";
            String sampleStorageArea = "";
            
            for (int i = 0; i < batchDataList.size(); i++) {
                StandingBookDataDTO dataDTO = batchDataList.get(i);
                
                // 收集需要合并的字段数据
                tankBatches.add(StrUtil.isNotBlank(dataDTO.getTankBatch()) ? dataDTO.getTankBatch() : "");
                packagingSpecs.add(StrUtil.isNotBlank(dataDTO.getPackagingSpecification()) ? dataDTO.getPackagingSpecification() : "");
                quantities.add(StrUtil.isNotBlank(dataDTO.getQuantity()) ? dataDTO.getQuantity() : "");
                customerNames.add(StrUtil.isNotBlank(dataDTO.getCustomerName()) ? dataDTO.getCustomerName() : "");
                
                // 质检人员和样品存放区域取第一条记录的数据
                if (i == 0) {
                    // 质检人员
                    if (StrUtil.isNotBlank(dataDTO.getInspector1()) || StrUtil.isNotBlank(dataDTO.getInspector2())
                            || StrUtil.isNotBlank(dataDTO.getAuditor1()) || StrUtil.isNotBlank(dataDTO.getAuditor2())) {
                        StringBuilder inspectorBuilder = new StringBuilder();
                        if (StrUtil.isNotBlank(dataDTO.getInspector1())) {
                            inspectorBuilder.append(dataDTO.getInspector1()).append("/");
                        }
                        if (StrUtil.isNotBlank(dataDTO.getInspector2())) {
                            inspectorBuilder.append(dataDTO.getInspector2()).append("/");
                        }
                        if (StrUtil.isNotBlank(dataDTO.getAuditor1())) {
                            inspectorBuilder.append(dataDTO.getAuditor1()).append("/");
                        }
                        if (StrUtil.isNotBlank(dataDTO.getAuditor2())) {
                            inspectorBuilder.append(dataDTO.getAuditor2()).append("/");
                        }
                        inspector = inspectorBuilder.toString();
                        if (inspector.endsWith("/")) {
                            inspector = inspector.substring(0, inspector.length() - 1);
                        }
                    }
                    
                    // 样品存放区域：工作表名称 + "样品橱柜" + month + "月份区域"
                    if (ObjectUtil.isNotNull(dataDTO.getSamplingDate())) {
                        // 获取采样日期的月份
                        LocalDate samplingDate = dataDTO.getSamplingDate().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
                        int month = samplingDate.getMonthValue();
                        sampleStorageArea = sheetName + "样品橱柜" + month + "月份区域";
                    }
                }
            }
            
            // 使用分号分隔不同的记录，使用逗号分隔字段组合
            StringBuilder tankBatchStr = new StringBuilder();
            StringBuilder packagingSpecStr = new StringBuilder();
            StringBuilder quantityStr = new StringBuilder();
            StringBuilder customerNameStr = new StringBuilder();
            
            for (int i = 0; i < tankBatches.size(); i++) {
                if (i > 0) {
                    tankBatchStr.append("/");
                    packagingSpecStr.append("/");
                    quantityStr.append("/");
                    customerNameStr.append("/");
                }
                tankBatchStr.append(tankBatches.get(i));
                packagingSpecStr.append(packagingSpecs.get(i));
                quantityStr.append(quantities.get(i));
                customerNameStr.append(customerNames.get(i));
            }
            
            // 罐次
            writeData.add(tankBatchStr.toString());
            
            // 包装规格
            writeData.add(packagingSpecStr.toString());
            
            // 数量
            writeData.add(quantityStr.toString());
            
            // 客户名称
            writeData.add(customerNameStr.toString());
            
            // 质检人员
            writeData.add(inspector);
            
            // 样品存放区域
            writeData.add(sampleStorageArea);
            
            writeDataList.add(writeData);
        }

        // 设置Excel样式
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 垂直居中,水平居中
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        // 设置 自动换行
        contentWriteCellStyle.setWrapped(false);
        // 设置缩小字体填充
        // contentWriteCellStyle.setShrinkToFit(true);
        // 字体策略
        WriteFont contentWriteFont = new WriteFont();
        // 字体大小，加粗，字体类型
        contentWriteFont.setFontName("黑体");
        contentWriteFont.setFontHeightInPoints((short) 12);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        
        // 头部样式策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setWrapped(true);
        // 设置缩小字体填充
        headWriteCellStyle.setShrinkToFit(true);
        // 字体策略
        WriteFont headtWriteFont = new WriteFont();
        // 字体大小，加粗，字体类型
        headtWriteFont.setFontName("黑体");
        headtWriteFont.setFontHeightInPoints((short) 14);
        headWriteCellStyle.setWriteFont(headtWriteFont);

        // 自定义列宽处理器
        AbstractColumnWidthStyleStrategy customColumnWidthHandler = new AbstractColumnWidthStyleStrategy() {
            @Override
            protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<WriteCellData<?>> cellDataList, 
                                        Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
                if (isHead) {
                    // 设置每列的宽度（单位为256分之一字符宽度）
                    switch (cell.getColumnIndex()) {
                        case 0: // 质检批号：25
                            writeSheetHolder.getSheet().setColumnWidth(0, 25 * 256);
                            break;
                        case 1: // 罐次：25  
                            writeSheetHolder.getSheet().setColumnWidth(1, 25 * 256);
                            break;
                        case 2: // 包装规格：12
                            writeSheetHolder.getSheet().setColumnWidth(2, 12 * 256);
                            break;
                        case 3: // 数量：12
                            writeSheetHolder.getSheet().setColumnWidth(3, 12 * 256);
                            break;
                        case 4: // 客户名称：50
                            writeSheetHolder.getSheet().setColumnWidth(4, 50 * 256);
                            break;
                        case 5: // 质检人员：30
                            writeSheetHolder.getSheet().setColumnWidth(5, 30 * 256);
                            break;
                        case 6: // 样品存放区域：40
                            writeSheetHolder.getSheet().setColumnWidth(6, 40 * 256);
                            break;
                    }
                }
            }
        };

        // 创建Excel工作表
        WriteSheet writeSheet = FastExcel
                .writerSheet(sheetIndex, sheetName)
                // 字体
                .registerWriteHandler(
                        new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle))
                // 行高
                .registerWriteHandler(new SimpleRowHeightStyleStrategy((short) 90, (short) 25))
                // 单元格格式（全局）
                .registerWriteHandler(new ExcelCellWriteHandler())
                // 自定义列宽
                .registerWriteHandler(customColumnWidthHandler)
                // 单元格格式（列）
                .registerWriteHandler(new ColumnDataFormatStrategy())
                // 单元格格式（单元格）
                .registerWriteHandler(new CellDataFormatStrategy())
                .head(writeHeadList)
                .build();
        
        // 写入数据
        excelWriter.write(writeDataList, writeSheet);
    }

    private static String getSampleStorageFileName(StandingBookQuery standingBookQuery, String folderPath) {
        String company = "";
        switch (standingBookQuery.getLinkId()) {
            case 3:
                company = "尚舜";
                break;
            case 4:
                company = "恒舜";
                break;
            case 7:
                company = "潍坊";
                break;
            case 8:
                company = "永舜";
                break;
        }
        if (StrUtil.isNotBlank(standingBookQuery.getStartDate())) {
            return folderPath + company + "_样品存放台账(" + standingBookQuery.getStartDate() + "至"
                    + standingBookQuery.getEndDate() + ").xlsx";
        } else {
            int currentYear = DateUtil.year(DateUtil.date());
            int currentMonth = DateUtil.month(DateUtil.date()) + 1;
            String yearMonth = currentYear + "-" + currentMonth;
            return folderPath + company + "_样品存放台账(" + yearMonth + "至" + yearMonth + ").xlsx";
        }
    }

    private static String getFileName(StandingBookQuery standingBookQuery, String folderPath) {
        String company = "";
        switch (standingBookQuery.getLinkId()) {
            case 3:
                company = "尚舜";
                break;
            case 4:
                company = "恒舜";
                break;
            case 7:
                company = "潍坊";
                break;
            case 8:
                company = "永舜";
                break;
        }
        if (StrUtil.isNotBlank(standingBookQuery.getStartDate())) {
            return folderPath + company + "_质检台账(" + standingBookQuery.getStartDate() + "至"
                    + standingBookQuery.getEndDate() + ").xlsx";
        } else {
            int currentYear = DateUtil.year(DateUtil.date());
            int currentMonth = DateUtil.month(DateUtil.date()) + 1;
            String yearMonth = currentYear + "-" + currentMonth;
            return folderPath + company + "_质检台账(" + yearMonth + "至" + yearMonth + ").xlsx";
        }
    }

    /**
     * 获取指标map
     *
     * @param requirement 指标字符串
     * @return 指标map
     */
    private Map<String, Map<String, String>> getRequirementMap(String requirement) {
        // 创建指标map
        Map<String, Map<String, String>> requirementMap = new HashMap<>();
        // 无对应指标
        if (requirement == null) {
            return requirementMap;
        }
        // 处理指标字符串
        String[] pairs = requirement.split(";");
        for (String pair : pairs) {
            String[] keyValue = pair.split(":");
            if (keyValue.length == 2) {
                String item = keyValue[0];
                String itemRequirement = keyValue[1];
                Map<String, String> itemMap = new HashMap<>();
                if (item.contains("|")) {
                    String[] split = item.split("\\|");
                    String[] itemNameSplit = split[1].split(",");
                    if (itemNameSplit.length > 1) {
                        itemMap.put("itemName", itemNameSplit[0] + "(" + itemNameSplit[1] + ")");
                    } else {
                        itemMap.put("itemName", split[1]);
                    }
                    itemMap.put("matchingName", split[0]);
                    // itemMap.put("itemName", split[1]);
                    itemMap.put("requirement", itemRequirement);
                    requirementMap.put(split[0], itemMap);
                } else {
                    String[] itemNameSplit = item.split(",");
                    if (itemNameSplit.length > 1) {
                        itemMap.put("itemName", itemNameSplit[0] + "(" + itemNameSplit[1] + ")");
                    } else {
                        itemMap.put("itemName", item);
                    }
                    itemMap.put("matchingName", item);
                    // itemMap.put("itemName", item);
                    itemMap.put("requirement", itemRequirement);
                    requirementMap.put(item, itemMap);
                }
            }
        }
        return requirementMap;
    }

    private Map<String, Map<String, String>> getRequirementSet(String requirement) {
        // 创建指标map
        Map<String, Map<String, String>> requirementMap = new HashMap<>();
        // 无对应指标
        if (requirement == null) {
            return requirementMap;
        }
        // 处理指标字符串
        String[] pairs = requirement.split(";");
        for (String pair : pairs) {
            String[] keyValue = pair.split(":");
            if (keyValue.length == 2) {
                String item = keyValue[0];
                String itemRequirement = keyValue[1];
                Map<String, String> itemMap = new HashMap<>();
                if (item.contains("|")) {
                    String[] split = item.split("\\|");
                    itemMap.put("matchingName", split[0].split(",")[0]);
                    itemMap.put("itemName", split[1]);
                    itemMap.put("requirement", itemRequirement);
                    requirementMap.put(split[0].split(",")[0], itemMap);
                } else {
                    itemMap.put("matchingName", item.split(",")[0]);
                    itemMap.put("itemName", item);
                    itemMap.put("requirement", itemRequirement);
                    requirementMap.put(item.split(",")[0], itemMap);
                }
            }
        }
        return requirementMap;
    }
}
