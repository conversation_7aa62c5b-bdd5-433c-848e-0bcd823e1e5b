package com.haihang.model.DO.operationLog;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description:
 * @Author: zad
 * @Create: 2023/12/27 14:56
 */
@Data
@Accessors(chain = true)
@TableName(value = "cmf_operation_log_un_submitted_record")
public class UnSubmittedRecordOperationLog {
    @TableId(value = "id")
    private Integer id;
    private Integer linkId;
    private Integer mergedRecordId;
    private String qualityInspectionBatch;
    private String productType;

    private String applicationId;
    private String productionBatch;
    private String customerName;
    private String tankBatch;
    private String packagingSpecification;
    private String quantity;

    private String operationType;
    private Integer operatorId;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date timestamp;
}
