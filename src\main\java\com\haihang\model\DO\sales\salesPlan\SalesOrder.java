package com.haihang.model.DO.sales.salesPlan;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 销售订单实体类
 * <p>
 * 该类用于管理销售订单的基本信息，包括订单编号、客户信息、订单日期、合同号等核心数据。
 * 对应数据库表 cmf_saleorder，是销售业务的主要数据载体。
 * </p>
 * 
 * @Description: 销售订单实体类
 * @Author: 系统生成
 * @Create: 2025/06/09
 * @version 1.0
 * @since 1.0
 */
@Data
@Accessors(chain = true)
@TableName(value = "cmf_saleorder")
public class SalesOrder {

    /**
     * 订单ID，主键
     */
    @TableId(value = "id")
    private Integer id;

    /**
     * 关联ID
     * <p>
     * 用于关联其他业务数据的外键字段
     * </p>
     */
    @TableField(value = "link_id")
    private Integer linkId;

    /**
     * 订单日期
     * <p>
     * 记录销售订单的创建日期，用于订单管理和统计分析
     * </p>
     */
    @TableField(value = "XSDD_DJRQ")
    private LocalDateTime orderDate;
    
    /**
     * 订单编号，唯一标识
     * <p>
     * 销售订单的唯一业务编号，用于标识和检索特定的销售订单
     * </p>
     */
    @TableField(value = "XSDD_DDBH")
    private String orderNumber;
    
    /**
     * 客户编号
     * <p>
     * 收货单位的客户编号，用于标识订单的收货方
     * </p>
     */
    @TableField(value = "XSDD_SHDKH")
    private String customerNumber;
    
    /**
     * 客户名称
     * <p>
     * 收货单位的客户名称，用于显示订单的收货方信息
     * </p>
     */
    @TableField(value = "XSDD_SHDKHMC")
    private String customerName;

    /**
     * 我方合同号
     * <p>
     * 本公司签署的合同编号，用于合同管理和订单关联
     * </p>
     */
    @TableField(value = "WF_HT")
    private String myContractNumber;

    /**
     * 对方合同号
     * <p>
     * 客户方提供的合同编号，用于双方合同信息的对应
     * </p>
     */
    @TableField(value = "DF_HT")
    private String otherContractNumber;
} 