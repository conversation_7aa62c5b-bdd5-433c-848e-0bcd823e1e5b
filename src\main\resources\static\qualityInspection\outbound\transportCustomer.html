<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <!-- 页面meta -->
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>客户信息-尚舜化工</title>
    <meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" name="viewport">
    <!-- 引入样式 -->
    <link rel="stylesheet" href="../../plugins/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="../../css/style.css">
    <link rel="icon" type="image/png" href="../../picture/logo.png">
    <style>
        /* 隐藏未完成加载的 Vue 组件 */
        [v-cloak] {
            display: none;
        }

        /* 自定义字体配置 */
        @font-face {
            font-family: "zad";
            src: url("../../font/MiSans-Medium.woff2");
        }

        /* 全局字体设置 */
        html,
        body,
        button,
        input,
        select,
        textarea,
        form {
            font-family: "zad", sans-serif !important;
        }

        /* 数据编辑输入框样式 */
        .dataEditInput .el-input__inner {
            text-align: center !important;
            font-size: 12px !important;
        }

        .inspectionDataEditInput .el-input__inner {
            text-align: center !important;
            font-size: 12px !important;
        }

        /* 防止滚动条遮挡表格内容 */
        .el-table__body-wrapper {
            z-index: 2;
        }

        .el-table__fixed {
            height: 100% !important;
        }

        .el-table__fixed-right {
            height: 100% !important;
        }

        .maxIndex1 .el-input__inner {
            background-color: #FADADD;
            /* 低值的背景颜色 */
        }

        .requirementTable .el-table__body-wrapper {
            overflow-x: hidden !important;
        }

        /* 跑马灯指示器样式 */
        .el-carousel__indicators--outside button.el-carousel__button {
            background-color: #409EFF;
            /* 未激活的指示器颜色 */
            width: 30px;
            /* 指示器宽度 */
            border-radius: 6px;
            /* 圆角 */
        }

        /* 跑马灯当前激活的指示器样式 */
        .el-carousel__indicators--outside .el-carousel__indicator.is-active button {
            background-color: #1E90FF;
            /* 激活的指示器颜色 */
        }

        /* 跑马灯箭头样式 */
        .el-carousel__arrow {
            background-color: rgba(31, 45, 61, 0.5);
            /* 箭头背景颜色 */
            color: #fff;
            /* 箭头颜色 */
            border-radius: 50%;
            /* 圆形箭头 */
            width: 36px;
            /* 箭头宽度 */
            height: 36px;
            /* 箭头高度 */
        }

        /* 跑马灯箭头悬停样式 */
        .el-carousel__arrow:hover {
            background-color: rgba(31, 45, 61, 0.8);
            /* 箭头悬停背景颜色 */
        }

        .el-dropdown-menu {
            min-width: 120px !important;
            /* 设置最小宽度 */
            padding: 5px 0 !important;
            /* 调整内边距 */
        }

        .el-dropdown-menu__item {
            font-size: 14px !important;
            /* 调整字体大小 */
            line-height: 40px !important;
            /* 调整行高 */
            padding: 0 15px !important;
            /* 调整选项内边距 */
        }

        .el-carousel {
            transition: height 0.3s ease;
        }

        @keyframes blink {
            0% {
                opacity: 1;
            }

            50% {
                opacity: 0.3;
            }

            100% {
                opacity: 1;
            }
        }

        .blink-icon {
            animation: blink 0.7s infinite;
        }

        .success-icon {
            color: #67C23A;
            /* Element UI 的成功绿色 */
            font-size: 20px;
        }

        .info-icon {
            color: #909399;
            /* Element UI 的信息灰色 */
            font-size: 20px;
        }
    </style>
</head>

<body class="hold-transition">
    <div id="app" v-loading.fullscreen.lock="fullscreenLoading" v-cloak>
        <!-- 页面主体结构 -->
        <div class="content-header">
            <h1>客户信息</h1>
        </div>

        <div class="app-container">
            <div class="box">
                <!-- 搜索区域 -->
                <div>
                    <el-row :gutter="20">
                        <!-- 集团名称搜索框 -->
                        <el-col :span="4">
                            <el-input size="small" placeholder="请输入集团名称" clearable :style="{width: '100%'}"
                                v-model.trim="main_customerQuery.groupName" @change="main_getGroupList()"
                                prefix-icon="el-icon-search">
                            </el-input>
                        </el-col>

                        <!-- 公司名称搜索框 -->
                        <el-col :span="4">
                            <el-input size="small" placeholder="请输入公司名称" clearable :style="{width: '100%'}"
                                v-model.trim="main_customerQuery.companyName" @change="main_getGroupList()"
                                prefix-icon="el-icon-search">
                            </el-input>
                        </el-col>

                        <!-- 操作按钮组 -->
                        <el-col :span="8">

                            <!-- 现有按钮 -->
                            <el-tooltip content="查询客户信息" placement="top">
                                <el-button size="small" @click="main_getGroupList()" type="primary"
                                    icon="el-icon-search" circle>
                                </el-button>
                            </el-tooltip>
                            <el-tooltip content="清空查询条件" placement="top">
                                <el-button size="small" @click="main_refreshSearch()" type="primary"
                                    icon="el-icon-refresh" circle>
                                </el-button>
                            </el-tooltip>
                            <!-- 新增集团按钮 -->
                            <el-tooltip content="新增集团" placement="top">
                                <el-button size="small" @click="main_openAddGroupDialog()" type="success"
                                    icon="el-icon-plus" circle>
                                </el-button>
                            </el-tooltip>
                        </el-col>
                    </el-row>
                </div>
                <br>

                <!-- 数据表格区域 -->
                <div>
                    <el-table :row-style="{height: '50px'}" v-loading="main_tableLoading" :cell-style="{padding: '0px'}"
                        :data="main_groupList" highlight-current-row
                        :header-cell-style="{background: '#f2f4f7', color: '#606266'}" border>
                        <el-table-column fixed type="index" align="center" width="50">
                        </el-table-column>
                        <el-table-column prop="groupName" label="集团名称" align="center" min-width="200"
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="companyCount" label="公司数量" align="center" min-width="150"
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="groupRemark" label="集团备注" align="center" min-width="300"
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="requirementGroup" label="客户指标" align="center" min-width="100">
                            <template slot-scope="scope">
                                <i :class="[
                                    scope.row.requirementGroup ? 'el-icon-check' : 'el-icon-close',
                                    scope.row.requirementGroup ? 'success-icon' : 'info-icon'
                                ]"></i>
                            </template>
                        </el-table-column>
                        <el-table-column fixed="right" label="操作" align="center" width="180" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <!-- 查看公司信息按钮 -->
                                <el-tooltip content="查看公司信息" placement="top">
                                    <el-button type="primary" size="mini" @click="main_openCompanyDialog(scope.row)"
                                        icon="el-icon-search" circle>
                                    </el-button>
                                </el-tooltip>

                                <!-- 编辑集团信息按钮 -->
                                <el-tooltip content="编辑集团信息" placement="top">
                                    <el-button type="warning" size="mini" @click="main_openGroupEditDialog(scope.row)"
                                        icon="el-icon-edit" circle>
                                    </el-button>
                                </el-tooltip>

                                <!-- 删除集团按钮 -->
                                <el-tooltip v-if="!scope.row.requirementGroup" content="删除集团" placement="top">
                                    <el-button type="danger" size="mini" @click="main_deleteGroup(scope.row)"
                                        icon="el-icon-delete" circle>
                                    </el-button>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 分页控件 -->
                    <el-pagination small style="text-align: right;" layout="prev, pager, next"
                        @current-change="main_handleCurrentChange" :current-page="main_customerQuery.current"
                        :page-size="main_customerQuery.size" :total="main_customerQuery.total">
                    </el-pagination>
                </div>

                <!-- 集团信息编辑对话框 -->
                <div>
                    <el-dialog :visible.sync="main_groupEditVisible" title="集团信息编辑" width="50%" center>
                        <!-- 添加表单 -->
                        <el-form :model="group_currentGroup" size="small" label-width="80px">
                            <el-form-item label="集团名称">
                                <el-input v-model="group_currentGroup.groupName" size="small" clearable
                                    placeholder="请输入集团名称">
                                </el-input>
                            </el-form-item>
                            <el-form-item label="集团备注">
                                <el-input v-model="group_currentGroup.groupRemark" size="small" type="textarea"
                                    :rows="8" placeholder="请输入集团备注信息">
                                </el-input>
                            </el-form-item>
                        </el-form>
                        <!-- 修改底部按钮 -->
                        <span slot="footer" class="dialog-footer">
                            <el-tooltip content="保存" placement="top">
                                <el-button type="success" size="small" icon="el-icon-check" circle
                                    @click="main_saveGroupEdit">
                                </el-button>
                            </el-tooltip>
                            <el-tooltip content="取消" placement="top">
                                <el-button type="danger" size="small" icon="el-icon-close" circle
                                    @click="main_groupEditVisible = false">
                                </el-button>
                            </el-tooltip>
                        </span>
                    </el-dialog>
                </div>

                <!-- 公司信息对话框 -->
                <div>
                    <el-dialog :visible.sync="company_dialogVisible" width="80%" @closed="handleCompanyDialogClose" center>
                        <div slot="title" style="text-align: center;">
                            <span>{{company_currentGroupName}}</span>
                            <el-divider direction="vertical"></el-divider>
                            <span>公司信息</span>
                        </div>
                        <div>
                            <el-table :row-style="{height: '50px'}" v-loading="company_tableLoading" max-height="550"
                                :cell-style="{padding: '0px'}" :data="company_list" highlight-current-row
                                :header-cell-style="{background: '#f2f4f7', color: '#606266'}" border>
                                <el-table-column fixed type="index" align="center" width="50">
                                </el-table-column>
                                <el-table-column prop="companyName" label="公司名称" align="center" min-width="200"
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column prop="companyRemark" label="公司备注" align="center" min-width="300"
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column fixed="right" label="操作" align="center" width="120">
                                    <template slot-scope="scope">
                                        <el-tooltip content="编辑公司信息" placement="top">
                                            <el-button type="warning" size="mini"
                                                @click="company_openEditDialog(scope.row)" icon="el-icon-edit" circle>
                                            </el-button>
                                        </el-tooltip>
                                        <el-tooltip v-if="!group_currentGroup.requirementGroup" content="删除公司" placement="top">
                                            <el-button type="danger" size="mini"
                                                @click="company_deleteCompany(scope.row)" icon="el-icon-delete" circle>
                                            </el-button>
                                        </el-tooltip>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                        <!-- 将按钮移动到底部插槽 -->
                        <span slot="footer" class="dialog-footer">
                            <el-tooltip content="新增公司" placement="top">
                                <el-button type="success" size="small" @click="company_openAddDialog" 
                                    icon="el-icon-plus" circle>
                                </el-button>
                            </el-tooltip>
                        </span>
                    </el-dialog>

                    <!-- 公司编辑对话框 -->
                    <el-dialog :visible.sync="company_editVisible" title="公司信息编辑" width="50%" center>
                        <el-form :model="company_currentCompany" size="small" label-width="80px">
                            <el-form-item label="公司名称">
                                {{ company_currentCompany.companyName }}
                            </el-form-item>
                            <el-form-item label="公司备注">
                                <el-input v-model="company_currentCompany.companyRemark" size="small" type="textarea"
                                    :rows="8" placeholder="请输入公司备注信息">
                                </el-input>
                            </el-form-item>
                        </el-form>
                        <span slot="footer" class="dialog-footer">
                            <el-tooltip content="保存" placement="top">
                                <el-button type="success" size="small" icon="el-icon-check" circle
                                    @click="company_saveEdit">
                                </el-button>
                            </el-tooltip>
                            <el-tooltip content="取消" placement="top">
                                <el-button type="danger" size="small" icon="el-icon-close" circle
                                    @click="company_editVisible = false">
                                </el-button>
                            </el-tooltip>
                        </span>
                    </el-dialog>
                </div>

                <!-- 新增集团对话框 -->
                <el-dialog :visible.sync="main_addGroupVisible" title="新增集团" width="50%" center>
                    <el-form :model="group_newGroup" :rules="group_rules" ref="groupForm" size="small"
                        label-width="120px">
                        <el-form-item label="集团名称" prop="groupName">
                            <el-input v-model="group_newGroup.groupName" size="small" clearable placeholder="请输入集团名称">
                            </el-input>
                        </el-form-item>
                        <el-form-item label="特殊指标客户">
                            <el-switch v-model="group_newGroup.requirementGroup" active-color="#67C23A"
                                inactive-color="#F56C6C">
                            </el-switch>
                        </el-form-item>
                        <el-form-item label="集团备注">
                            <el-input v-model="group_newGroup.groupRemark" size="small" type="textarea" :rows="8"
                                placeholder="请输入集团备注信息">
                            </el-input>
                        </el-form-item>
                    </el-form>
                    <span slot="footer" class="dialog-footer">
                        <el-tooltip content="保存" placement="top">
                            <el-button type="success" size="small" icon="el-icon-check" circle
                                @click="main_saveNewGroup('groupForm')">
                            </el-button>
                        </el-tooltip>
                        <el-tooltip content="取消" placement="top">
                            <el-button type="danger" size="small" icon="el-icon-close" circle
                                @click="main_addGroupVisible = false">
                            </el-button>
                        </el-tooltip>
                    </span>
                </el-dialog>

                <!-- 新增公司对话框 -->
                <el-dialog :visible.sync="company_addVisible" title="新增公司" width="50%" center>
                    <el-form :model="company_newCompany" :rules="company_rules" ref="companyForm" 
                        size="small" label-width="120px">
                        <el-form-item label="选择公司" prop="companyName">
                            <el-select
                                v-model="company_newCompany.companyNumbers"
                                multiple
                                collapse-tags
                                filterable
                                remote
                                reserve-keyword
                                placeholder="请输入关键字"
                                :remote-method="company_remoteSearch"
                                :loading="company_searchLoading"
                                style="width: 100%">
                                <el-option
                                    v-for="item in company_searchOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-form>
                    <span slot="footer" class="dialog-footer">
                        <el-tooltip content="保存" placement="top">
                            <el-button type="success" size="small" icon="el-icon-check" 
                                circle @click="company_saveNewCompany('companyForm')">
                            </el-button>
                        </el-tooltip>
                        <el-tooltip content="取消" placement="top">
                            <el-button type="danger" size="small" icon="el-icon-close" 
                                circle @click="company_addVisible = false">
                            </el-button>
                        </el-tooltip>
                    </span>
                </el-dialog>
            </div>
        </div>
    </div>

    <!-- Vue 相关脚本 -->
    <script src="../../js/vue.js"></script>
    <script src="../../js/index.js"></script>
    <script src="../../js/axios.min.js"></script>
    <script type="text/javascript" src="../../js/jquery.min.js"></script>
    <link rel="stylesheet" href="../../element-ui/lib/theme-chalk/index.css">
    <script>
        let vue = new Vue({
            el: '#app',
            data: {
                // 全局状态
                fullscreenLoading: false, // 全屏加载状态
                user: "", // 当前登录用户信息

                // 客户信息列表相关数据
                main_customerQuery: {
                    current: 1,    // 当前页码
                    size: 10,      // 每页显示记录数
                    total: 0,      // 总记录数
                    groupName: "",  // 集团名称搜索条件
                    companyName: "" // 公司名称搜索条件
                },

                // 集团相关数据
                main_groupList: [],         // 集团列表数据
                main_tableLoading: false,   // 表格加载状态
                main_groupEditVisible: false, // 集团编辑对话框显示状态

                // 公司相关数据
                company_dialogVisible: false,  // 公司信息对话框显示状态
                company_currentGroupName: '', // 当前选中的集团名称
                company_list: [],             // 公司列表数据
                company_tableLoading: false,  // 公司表格加载状态
                company_editVisible: false,   // 公司编辑对话框显示状态
                company_currentCompany: {},   // 当前编辑的公司信息

                group_currentGroup: {},
                main_addGroupVisible: false, // 新增集团对话框显示状态
                group_newGroup: {
                    groupName: '',
                    groupRemark: '',
                    requirementGroup: false,
                    operatorId: '',
                    operatorName: ''
                },
                group_rules: {
                    groupName: [
                        { required: true, message: '请输入集团名称', trigger: 'blur' }
                    ]
                },

                // 新增公司相关数据
                company_addVisible: false,      // 新增公司对话框显示状态
                company_searchLoading: false,   // 搜索加载状态
                company_searchOptions: [],      // 搜索结果选项
                company_newCompany: {           // 新公司数据
                    companyNumbers: [],
                    groupId: '',
                    operatorId: '',
                    operatorName: ''
                },
                company_rules: {               // 表单验证规则
                    companyNumbers: [
                        { required: true, message: '请至少选择一个公司', trigger: 'change' }
                    ]
                }
            },

            created() {
                let url = decodeURI(location.search).slice(1);
                // 创建空对象存储参数
                let obj = {};
                // 再通过 & 将每一个参数单独分割出来
                let paramsArr = url.split('&')
                for (let i = 0, len = paramsArr.length; i < len; i++) {
                    // 再通过 = 将每一个参数分割为 key:value 的形式
                    let arr = paramsArr[i].split('=')
                    obj[arr[0]] = arr[1];
                }
                let param = {
                    id: obj.uid,
                    password: obj.pwd
                };
                axios.post("/user/verification", param).then((res) => {
                    if (res.data.flag) {
                        this.user = res.data.data;
                        this.main_getGroupList();
                    }
                });
            },

            methods: {
                main_getGroupList() {
                    this.main_tableLoading = true;
                    axios.post("/transportReportCustomer/getGroupPage", this.main_customerQuery).then((res) => {
                        if (res.data.flag) {
                            this.main_customerQuery.current = Number(res.data.data.current);
                            this.main_customerQuery.size = Number(res.data.data.size);
                            this.main_customerQuery.total = Number(res.data.data.total);
                            this.main_groupList = res.data.data.records;
                        } else {
                            this.main_groupList = [];
                            this.$message.error("客户信息查询失败");
                        }
                    }).finally(() => {
                        this.main_tableLoading = false;
                    });
                },

                main_handleCurrentChange(current) {
                    this.main_customerQuery.current = current;
                    this.main_getGroupList()
                },

                main_refreshSearch() {
                    this.main_customerQuery.groupName = "";
                    this.main_customerQuery.companyName = "";
                    this.main_getGroupList();
                },

                // 查看公司信息
                main_openCompanyDialog(row) {
                    this.group_currentGroup = JSON.parse(JSON.stringify(row)); // 深拷贝避免直接修改原数据
                    this.group_currentGroup.operatorId = this.user.id;
                    this.group_currentGroup.operatorName = this.user.username;
                    this.company_currentGroupName = row.groupName;
                    this.company_dialogVisible = true;
                    this.company_tableLoading = true;

                    // 调用后端接口获取公司列表
                    axios.post("/transportReportCustomer/getCompanyList", row).then(res => {
                        if (res.data.flag) {
                            this.company_list = res.data.data;
                        } else {
                            this.$message.error("获取公司信息失败");
                            this.company_list = [];
                        }
                    }).finally(() => {
                        this.company_tableLoading = false;
                    });
                },

                // 编辑集团信息
                main_openGroupEditDialog(row) {
                    this.group_currentGroup = JSON.parse(JSON.stringify(row)); // 深拷贝避免直接修改原数据
                    this.group_currentGroup.operatorId = this.user.id;
                    this.group_currentGroup.operatorName = this.user.username;
                    this.main_groupEditVisible = true;
                },

                // 保存集团信息
                main_saveGroupEdit() {
                    axios.post("/transportReportCustomer/updateGroup", this.group_currentGroup).then(res => {
                        if (res.data.flag) {
                            this.$message.success("保存成功");
                            this.main_groupEditVisible = false;
                            this.main_getGroupList(); // 刷新列表
                        } else {
                            this.$message.error("保存失败");
                        }
                    });
                },

                // 删除集团
                main_deleteGroup(row) {
                    this.$confirm('确认删除该集团?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        // 添加用户信息到删除请求中
                        const deleteData = Object.assign({}, row, { user: this.user });
                        axios.post("/transportReportCustomer/deleteGroup", deleteData).then(res => {
                            if (res.data.flag) {
                                this.$message.success("删除成功");
                                this.main_getGroupList(); // 刷新列表
                            } else {
                                this.$message.error("删除失败");
                            }
                        });
                    }).catch(() => {
                        this.$message.info("已取消删除");
                    });
                },

                // 打开公司编辑对话框
                company_openEditDialog(row) {
                    this.company_currentCompany = JSON.parse(JSON.stringify(row));
                    this.company_editVisible = true;
                },

                // 保存公司信息
                company_saveEdit() {
                    axios.post("/transportReportCustomer/updateCompany", this.company_currentCompany).then(res => {
                        if (res.data.flag) {
                            this.$message.success("保存成功");
                            this.company_editVisible = false;
                            this.company_tableLoading = true;
                            // 调用后端接口获取公司列表
                            axios.post("/transportReportCustomer/getCompanyList", this.group_currentGroup).then(res => {
                                if (res.data.flag) {
                                    this.company_list = res.data.data;
                                } else {
                                    this.$message.error("获取公司信息失败");
                                    this.company_list = [];
                                }
                            }).finally(() => {
                                this.company_tableLoading = false;
                            });
                        } else {
                            this.$message.error("保存失败");
                        }
                    });
                },

                // 删除公司
                company_deleteCompany(row) {
                    this.$confirm('确认删除该公司?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        axios.post("/transportReportCustomer/deleteCompany", row).then(res => {
                            if (res.data.flag) {
                                this.$message.success("删除成功");
                                // 调用后端接口获取公司列表
                                axios.post("/transportReportCustomer/getCompanyList", this.group_currentGroup).then(res => {
                                    if (res.data.flag) {
                                        this.company_list = res.data.data;
                                    } else {
                                        this.$message.error("获取公司信息失败");
                                        this.company_list = [];
                                    }
                                }).finally(() => {
                                    this.company_tableLoading = false;
                                });
                            } else {
                                this.$message.error("删除失败");
                            }
                        });
                    }).catch(() => {
                        this.$message.info("已取消删除");
                    });
                },

                // 打开新增集团对话框
                main_openAddGroupDialog() {
                    this.group_newGroup = {
                        groupName: '',
                        groupRemark: '',
                        requirementGroup: false,
                        operatorId: this.user.id,
                        operatorName: this.user.username
                    };
                    this.main_addGroupVisible = true;
                },

                // 保存新集团
                main_saveNewGroup(formName) {
                    this.$refs[formName].validate((valid) => {
                        if (valid) {
                            axios.post("/transportReportCustomer/addGroup", this.group_newGroup).then(res => {
                                if (res.data.flag) {
                                    this.$message.success("新增集团成功");
                                    this.main_addGroupVisible = false;
                                    this.main_getGroupList(); // 刷新列表
                                } else {
                                    this.$message.error("新增集团失败");
                                }
                            });
                        } else {
                            return false;
                        }
                    });
                },

                // 处理公司对话框关闭事件
                handleCompanyDialogClose() {
                    this.main_getGroupList(); // 刷新集团列表
                },

                // 打开新增公司对话框
                company_openAddDialog() {
                    this.company_newCompany = {
                        companyNumbers: [],
                        requirementGroup: this.group_currentGroup.requirementGroup,
                        groupNumber: this.group_currentGroup.groupNumber,
                        groupId: this.group_currentGroup.groupId,
                        operatorId: this.user.id,
                        operatorName: this.user.username
                    };
                    this.company_searchOptions = [];
                    this.company_addVisible = true;
                },

                // 远程搜索公司
                company_remoteSearch(query) {
                    if (query !== '') {
                        this.company_searchLoading = true;
                        // 调用后端接口进行搜索
                        let param = {
                            companyName: query,
                        };
                        axios.post("/transportReportCustomer/searchCompanies", param).then(res => {
                            if (res.data.flag) {
                                this.company_searchOptions = res.data.data;
                            } else {
                                this.$message.error("搜索公司失败");
                                this.company_searchOptions = [];
                            }
                        }).finally(() => {
                            this.company_searchLoading = false;
                        });
                    } else {
                        this.company_searchOptions = [];
                    }
                },

                // 保存新公司
                company_saveNewCompany(formName) {
                    this.$refs[formName].validate((valid) => {
                        if (valid) {
                            // 构造要发送给后端的数据
                            const companiesData = {
                                companyNumbers: this.company_newCompany.companyNumbers,
                                requirementGroup: this.group_currentGroup.requirementGroup,
                                groupNumber: this.group_currentGroup.groupNumber,
                                groupId: this.group_currentGroup.groupId,
                                operatorId: this.company_newCompany.operatorId,
                                operatorName: this.company_newCompany.operatorName
                            };
                            
                            axios.post("/transportReportCustomer/addCompanies", companiesData).then(res => {
                                if (res.data.flag) {
                                    this.$message.success("新增公司成功");
                                    this.company_addVisible = false;
                                    // 刷新公司列表
                                    this.company_tableLoading = true;
                                    axios.post("/transportReportCustomer/getCompanyList", this.group_currentGroup).then(res => {
                                        if (res.data.flag) {
                                            this.company_list = res.data.data;
                                        } else {
                                            this.$message.error("获取公司信息失败");
                                            this.company_list = [];
                                        }
                                    }).finally(() => {
                                        this.company_tableLoading = false;
                                    });
                                } else {
                                    this.$message.error("新增公司失败");
                                }
                            });
                        } else {
                            return false;
                        }
                    });
                }
            }
        })
    </script>

</html>