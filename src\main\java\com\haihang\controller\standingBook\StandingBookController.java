package com.haihang.controller.standingBook;

import com.haihang.common.R;
import com.haihang.model.DO.user.User;
import com.haihang.model.Query.record.ItemDataQuery;
import com.haihang.model.Query.standingBook.StandingBookQuery;
import com.haihang.service.record.common.StandingBookService;
import com.haihang.service.user.UserService;
import com.haihang.utils.common.FileUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/standingBook")
@RequiredArgsConstructor
public class StandingBookController {

    private final StandingBookService standingBookService;
    private final UserService userService;
    @Resource
    private HttpServletResponse response;
    @Resource
    private HttpServletRequest request;
    @Value("${path-config.standingBook-path}")
    private String standingBookPath;

    /**
     * 获取已提交记录检测台账
     *
     * @param standingBookQuery 台账Query
     * @return 台账数据map
     */
    @GetMapping("/getAll")
    public R getStandingBook(StandingBookQuery standingBookQuery) {
        return new R(true, standingBookService.getStandingBook(standingBookQuery));
    }

    /**
     * 获取质检项目数据
     *
     * @param itemDataQuery 项目数据Query
     * @return 项目数据集合
     */
    @PostMapping("/getItemData")
    public R getItemData(@RequestBody ItemDataQuery itemDataQuery) {
        return new R(true, standingBookService.getItemData(itemDataQuery));
    }

    /**
     * 获取抽检检测项目数据
     *
     * @param itemDataQuery 检测项目数据Query
     * @return 检测项目数据集合
     */
    @PostMapping("/getSamplingItemData")
    public R getSamplingItemData(@RequestBody ItemDataQuery itemDataQuery) {
        return new R(true, standingBookService.getSamplingItemData(itemDataQuery));
    }

    /**
     * 获取已提交记录中的全部产品类型
     *
     * @return 产品类型集合
     */
    @GetMapping("/getProductTypes/{linkId}/{isSemiFinished}/{userId}")
    public R getProductTypes(@PathVariable String isSemiFinished, @PathVariable String userId, @PathVariable String linkId) {
        return new R(true, standingBookService.getProductTypeList(linkId, isSemiFinished, userId));
    }

    /**
     * 质检台账Excel创建
     *
     * @param standingBookQuery 质检台账ExcelQuery
     * @return 质检台账Excel文件名
     */
    @GetMapping("/standingBookExcelCreate")
    public R standingBookExcelCreate(StandingBookQuery standingBookQuery) {
        return new R(true, standingBookService.standingBookExcelCreate(standingBookQuery));
    }

    /**
     * 样品存放台账Excel创建
     *
     * @param standingBookQuery 台账查询参数
     * @return 样品存放台账Excel文件名
     */
    @GetMapping("/sampleStorageStandingBookExcelCreate")
    public R sampleStorageStandingBookExcelCreate(StandingBookQuery standingBookQuery) {
        try {
            String fileName = standingBookService.sampleStorageStandingBookExcelCreate(standingBookQuery);
            return new R(true, fileName);
        } catch (Exception e) {
            return new R(false, "样品存放台账生成失败：" + e.getMessage());
        }
    }

    /**
     * 质检台账Excel下载
     *
     * @param fileName 质检台账Excel文件名称
     * @param userId 用户id
     */
    @GetMapping("/standingBookExcelCreateDownload/{userId}/{fileName}")
    public void reportDownload(@PathVariable String fileName, @PathVariable String userId) {
        User user = userService.getById(userId);
        File file = new File(standingBookPath + user.getId() + "_" + user.getUsername() + "\\" + fileName);
        FileUtil.downloadFile(file, request, response, fileName);
    }
}
