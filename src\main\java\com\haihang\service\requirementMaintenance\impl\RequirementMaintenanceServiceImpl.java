package com.haihang.service.requirementMaintenance.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.haihang.mapper.common.CommonCompanyMapper;
import com.haihang.mapper.common.CompanyMapper;
import com.haihang.mapper.common.GroupMapper;
import com.haihang.mapper.common.ProductTypeForSortMapper;
import com.haihang.mapper.operationLog.RequirementDeleteOperationLogMapper;
import com.haihang.mapper.operationLog.RequirementOperationLogMapper;
import com.haihang.mapper.record.ItemPriorityForSortMapper;
import com.haihang.mapper.requirementMaintenance.*;
import com.haihang.model.DO.common.CommonCompany;
import com.haihang.model.DO.common.Company;
import com.haihang.model.DO.common.Group;
import com.haihang.model.DO.common.ProductTypeForSort;
import com.haihang.model.DO.operationLog.RequirementDeleteOperationLog;
import com.haihang.model.DO.operationLog.RequirementOperationLog;
import com.haihang.model.DO.record.ItemPriorityForSort;
import com.haihang.model.DO.requirementMaintenance.*;
import com.haihang.model.DO.user.User;
import com.haihang.model.DTO.elementUI.SelectLabelDTO;
import com.haihang.model.DTO.requirementMaintenance.*;
import com.haihang.model.Query.operationLog.OperationLogQuery;
import com.haihang.model.Query.requirementMaintenance.ProductRequirementQuery;
import com.haihang.model.Query.requirementMaintenance.RequirementMaintenanceQuery;
import com.haihang.model.VO.operationLog.RequirementOperationLogVO;
import com.haihang.service.requirementMaintenance.RequirementMaintenanceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: zad
 * @Create: 2023/11/6 9:12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RequirementMaintenanceServiceImpl implements RequirementMaintenanceService {
    private final GroupMapper groupMapper;
    private final CompanyMapper companyMapper;
    private final CommonCompanyMapper commonCompanyMapper;
    private final GeneralQualityStandardMapper generalQualityStandardMapper;
    private final GroupQualityStandardMapper groupQualityStandardMapper;
    private final InspectionItemMapper inspectionItemMapper;
    private final ProductTypeForSortMapper productTypeForSortMapper;
    private final ItemPriorityForSortMapper itemPriorityForSortMapper;
    private final MaterialInformationMapper materialInformationMapper;
    private final RequirementOperationLogMapper requirementOperationLogMapper;
    private final MaterialCategoryMapper materialCategoryMapper;
    private final RequirementDeleteOperationLogMapper requirementDeleteOperationLogMapper;

    @Override
    public IPage<RequirementGroupDTO> getGroupPage(int currentPage, int pageSize, RequirementMaintenanceQuery requirementMaintenanceQuery) {
        MPJLambdaWrapper<Group> requirementGroupWrapper = new MPJLambdaWrapper<Group>()
                .selectAs(Group::getGroupNumber, RequirementGroupDTO::getGroupNumber)
                .selectAs(Group::getGroupName, RequirementGroupDTO::getGroupName)
                .selectCount(Company::getGroupNumber, RequirementGroupDTO::getCompanyCount)
                .leftJoin(Company.class, Company::getGroupNumber, Group::getGroupNumber)
                .like(StrUtil.isNotBlank(requirementMaintenanceQuery.getGroupNumber()), Group::getGroupNumber, requirementMaintenanceQuery.getGroupNumber());
        StrUtil.split(requirementMaintenanceQuery.getGroupName(), " ").forEach(groupName -> requirementGroupWrapper.like(StrUtil.isNotBlank(requirementMaintenanceQuery.getGroupName()), Group::getGroupName, groupName));
        StrUtil.split(requirementMaintenanceQuery.getCompanyName(), " ").forEach(companyName -> requirementGroupWrapper.like(StrUtil.isNotBlank(requirementMaintenanceQuery.getCompanyName()), Company::getCompanyName, companyName));
        requirementGroupWrapper.groupBy(Group::getGroupNumber);
        return groupMapper.selectJoinPage(new Page<>(currentPage, pageSize), RequirementGroupDTO.class, requirementGroupWrapper);
    }

    @Override
    public List<RequirementProductDTO> getProductList(RequirementMaintenanceQuery requirementMaintenanceQuery) {
        if (requirementMaintenanceQuery.getGroup()) {
            MPJLambdaWrapper<GroupQualityStandard> requirementGroupWrapper = new MPJLambdaWrapper<GroupQualityStandard>()
                    .selectAs(GroupQualityStandard::getId, RequirementProductDTO::getId)
                    .selectAs(GroupQualityStandard::getGroupNumber, RequirementProductDTO::getGroupNumber)
                    .selectAs(GroupQualityStandard::getProductCategoryName, RequirementProductDTO::getProductCategory)
                    .selectAs(GroupQualityStandard::getProductCategoryNumber, RequirementProductDTO::getProductCategoryNumber)
                    .selectAs(GroupQualityStandard::getProductName, RequirementProductDTO::getProductType)
                    .selectAs(GroupQualityStandard::getProductNumber, RequirementProductDTO::getProductTypeNumber)
                    .selectAs(GroupQualityStandard::getRequirementCategory, RequirementProductDTO::getRequirementCategory)
                    .select(GroupQualityStandard::getDefaultStatus)
                    // TODO 子查询无法添加执行条件
                    //.selectFunc("COUNT(DISTINCT %s)", arg -> arg.accept(GroupQualityStandard::getRequirementCategory), RequirementProductDTO::getCategoryCount)
                    /*.selectSub(GroupQualityStandard.class, w -> w.selectFunc("COUNT(DISTINCT %s)", arg -> arg.accept(GroupQualityStandard::getRequirementCategory), GroupQualityStandard::getRequirementCategory)
                            .eq(GroupQualityStandard::getGroupNumber, GroupQualityStandard::getGroupNumber)
                            .eq(GroupQualityStandard::getProductNumber, GroupQualityStandard::getProductNumber)
                            .eq(GroupQualityStandard::getProductCategoryNumber, GroupQualityStandard::getProductCategoryNumber)
                            .groupBy(GroupQualityStandard::getProductCategoryNumber), RequirementProductDTO::getCategoryCount)*/
                    .eq(GroupQualityStandard::getGroupNumber, requirementMaintenanceQuery.getGroupNumber())
                    .eq(GroupQualityStandard::getDefaultStatus, true)
                    .like(StrUtil.isNotBlank(requirementMaintenanceQuery.getProductCategory()), GroupQualityStandard::getProductCategoryName, requirementMaintenanceQuery.getProductCategory())
                    .like(StrUtil.isNotBlank(requirementMaintenanceQuery.getProductType()), GroupQualityStandard::getProductName, requirementMaintenanceQuery.getProductType())
                    .groupBy(GroupQualityStandard::getProductNumber, GroupQualityStandard::getProductCategoryNumber);
            List<RequirementProductDTO> requirementProductList = groupQualityStandardMapper.selectJoinList(RequirementProductDTO.class, requirementGroupWrapper);
            // TODO 子查询无法添加执行条件
            requirementProductList.forEach(requirementProductDTO -> {
                MPJLambdaWrapper<GroupQualityStandard> categoryCountWrapper = new MPJLambdaWrapper<GroupQualityStandard>()
                        .selectAs(GroupQualityStandard::getRequirementCategory, RequirementProductDTO::getRequirementCategory)
                        .selectFunc("COUNT(DISTINCT %s)", arg -> arg.accept(GroupQualityStandard::getRequirementCategory), RequirementProductDTO::getCategoryCount)
                        .eq(GroupQualityStandard::getGroupNumber, requirementProductDTO.getGroupNumber())
                        .eq(GroupQualityStandard::getProductCategoryNumber, requirementProductDTO.getProductCategoryNumber())
                        .eq(ObjectUtil.isNotNull(requirementProductDTO.getProductTypeNumber()), GroupQualityStandard::getProductNumber, requirementProductDTO.getProductTypeNumber())
                        .isNull(ObjectUtil.isNull(requirementProductDTO.getProductTypeNumber()), GroupQualityStandard::getProductNumber)
                        .groupBy(GroupQualityStandard::getProductNumber, GroupQualityStandard::getProductCategoryNumber);
                List<RequirementProductDTO> requirementProductDTOList = groupQualityStandardMapper.selectJoinList(RequirementProductDTO.class, categoryCountWrapper);
                if (requirementProductDTOList.size() == 1) {
                    requirementProductDTO.setCategoryCount(requirementProductDTOList.get(0).getCategoryCount());
                }
            });
            return CollUtil.sort(requirementProductList, Comparator.comparing(RequirementProductDTO::getProductCategory));
        } else {
            MPJLambdaWrapper<GeneralQualityStandard> requirementGroupWrapper = new MPJLambdaWrapper<GeneralQualityStandard>()
                    .selectAs(GeneralQualityStandard::getId, RequirementProductDTO::getId)
                    .selectAs(GeneralQualityStandard::getProductCategoryName, RequirementProductDTO::getProductCategory)
                    .selectAs(GeneralQualityStandard::getProductCategoryNumber, RequirementProductDTO::getProductCategoryNumber)
                    .selectAs(GeneralQualityStandard::getProductName, RequirementProductDTO::getProductType)
                    .selectAs(GeneralQualityStandard::getProductNumber, RequirementProductDTO::getProductTypeNumber)
                    .like(StrUtil.isNotBlank(requirementMaintenanceQuery.getProductCategory()), GeneralQualityStandard::getProductCategoryName, requirementMaintenanceQuery.getProductCategory())
                    .like(StrUtil.isNotBlank(requirementMaintenanceQuery.getProductType()), GeneralQualityStandard::getProductName, requirementMaintenanceQuery.getProductType())
                    .groupBy(GeneralQualityStandard::getProductNumber, GeneralQualityStandard::getProductCategoryNumber);
            List<RequirementProductDTO> requirementProductList = generalQualityStandardMapper.selectJoinList(RequirementProductDTO.class, requirementGroupWrapper);
            return CollUtil.sort(requirementProductList, Comparator.comparing(RequirementProductDTO::getProductCategory));
        }
    }

    @Override
    public List<RequirementGroupDTO> getGroupList() {
        MPJLambdaWrapper<Group> requirementGroupWrapper = new MPJLambdaWrapper<Group>()
                .selectAs(Group::getGroupNumber, RequirementGroupDTO::getGroupNumber)
                .selectAs(Group::getGroupName, RequirementGroupDTO::getGroupName);
        return groupMapper.selectJoinList(RequirementGroupDTO.class, requirementGroupWrapper);
    }

    @Override
    public List<SelectLabelDTO> getItemLabel() {
        List<InspectionItem> inspectionItems = inspectionItemMapper.selectList(null);
        List<SelectLabelDTO> selectLabelDTOList = new ArrayList<>();
        inspectionItems.forEach(inspectionItem -> {
            SelectLabelDTO selectLabelDTO = new SelectLabelDTO();
            selectLabelDTO.setLabel(inspectionItem.getItem());
            selectLabelDTO.setValue(inspectionItem.getItem());
            selectLabelDTOList.add(selectLabelDTO);
        });
        return selectLabelDTOList;
    }

    @Override
    public List<ProductRequirementDTO> getProductRequirement(ProductRequirementQuery productRequirementQuery) {
        List<ProductRequirementDTO> productRequirementDTOList;
        if (productRequirementQuery.getGroup()) {
            MPJLambdaWrapper<GroupQualityStandard> wrapper = new MPJLambdaWrapper<GroupQualityStandard>()
                    .selectAll(GroupQualityStandard.class)
                    .eq(GroupQualityStandard::getGroupNumber, productRequirementQuery.getRequirementProductDTO().getGroupNumber())
                    .eq(GroupQualityStandard::getProductCategoryNumber, productRequirementQuery.getRequirementProductDTO().getProductCategoryNumber());
            if (StrUtil.isBlank(productRequirementQuery.getRequirementProductDTO().getProductTypeNumber())) {
                wrapper.isNull(GroupQualityStandard::getProductNumber)
                        .isNull(GroupQualityStandard::getProductName);
            } else {
                wrapper.eq(GroupQualityStandard::getProductNumber, productRequirementQuery.getRequirementProductDTO().getProductTypeNumber());
            }
            if (StrUtil.isBlank(productRequirementQuery.getRequirementCategory())) {
                wrapper.eq(GroupQualityStandard::getDefaultStatus, true);
            } else {
                wrapper.eq(GroupQualityStandard::getRequirementCategory, productRequirementQuery.getRequirementCategory());
            }
            productRequirementDTOList = groupQualityStandardMapper.selectJoinList(ProductRequirementDTO.class, wrapper);
        } else {
            MPJLambdaWrapper<GeneralQualityStandard> wrapper = new MPJLambdaWrapper<GeneralQualityStandard>()
                    .selectAll(GeneralQualityStandard.class)
                    .eq(GeneralQualityStandard::getProductCategoryNumber, productRequirementQuery.getRequirementProductDTO().getProductCategoryNumber());
            if (StrUtil.isBlank(productRequirementQuery.getRequirementProductDTO().getProductTypeNumber())) {
                wrapper.isNull(GeneralQualityStandard::getProductNumber)
                        .isNull(GeneralQualityStandard::getProductName);
            } else {
                wrapper.eq(GeneralQualityStandard::getProductNumber, productRequirementQuery.getRequirementProductDTO().getProductTypeNumber());
            }
            productRequirementDTOList = generalQualityStandardMapper.selectJoinList(ProductRequirementDTO.class, wrapper);
        }
        // 排序相关
        List<String> orderList = new ArrayList<>();
        LambdaQueryWrapper<ProductTypeForSort> productTypeForSortLambdaQueryWrapper = new LambdaQueryWrapper<>();
        productTypeForSortLambdaQueryWrapper.eq(ProductTypeForSort::getProductCategoryNumber, productRequirementQuery.getRequirementProductDTO().getProductCategoryNumber());
        List<ProductTypeForSort> productTypeForSorts = productTypeForSortMapper.selectList(productTypeForSortLambdaQueryWrapper);
        if (!productTypeForSorts.isEmpty()) {
            LambdaQueryWrapper<ItemPriorityForSort> itemPriorityForSortLambdaQueryWrapper = new LambdaQueryWrapper<>();
            itemPriorityForSortLambdaQueryWrapper.eq(ItemPriorityForSort::getProductTypeId, productTypeForSorts.get(0).getId());
            List<ItemPriorityForSort> itemPriorityForSorts = itemPriorityForSortMapper.selectList(itemPriorityForSortLambdaQueryWrapper);
            List<ItemPriorityForSort> sortedList = itemPriorityForSorts.stream()
                    .sorted(Comparator.comparingInt(ItemPriorityForSort::getPriority).reversed()
                            .thenComparing(ItemPriorityForSort::getItem))
                    .collect(Collectors.toList());
            sortedList.forEach(itemPriorityForSort -> orderList.add(itemPriorityForSort.getItem()));
        }
        log.info(orderList.toString());
        // 自定义比较器，优先按照orderList排序，orderList中不包括的项目置底
        Comparator<ProductRequirementDTO> itemComparator = (o1, o2) -> {
            String item1 = o1.getQualityInspectionItem().split(",")[0];
            String item2 = o2.getQualityInspectionItem().split(",")[0];
            // 通过正则表达式提取括号前的内容
            String regex = "^(.*?)\\(";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher1 = pattern.matcher(item1);
            Matcher matcher2 = pattern.matcher(item2);
            String itemCategory1 = matcher1.find() ? matcher1.group(1) : item1;
            String itemCategory2 = matcher2.find() ? matcher2.group(1) : item2;
            // 如果都在 orderList 中，按照 orderList 排序；否则，未在 orderList 中的排在后面
            if (orderList.contains(itemCategory1) && orderList.contains(itemCategory2)) {
                return Integer.compare(orderList.indexOf(itemCategory1), orderList.indexOf(itemCategory2));
            } else if (orderList.contains(itemCategory1)) {
                return -1;
            } else if (orderList.contains(itemCategory2)) {
                return 1;
            } else {
                return item1.compareTo(item2);
            }
        };
        productRequirementDTOList.sort(itemComparator);
        return productRequirementDTOList;
    }

    @Override
    public List<CompanyInformationDTO> getGroupCompany(String groupNumber) {
        MPJLambdaWrapper<Company> wrapper = new MPJLambdaWrapper<Company>()
                .selectAll(Company.class)
                .eq(Company::getGroupNumber, groupNumber);
        return companyMapper.selectJoinList(CompanyInformationDTO.class, wrapper);
    }

    @Override
    public List<SelectLabelDTO> getCompanyOptions(String companyName) {
        LambdaQueryWrapper<CommonCompany> commonCompanyWrapper = new LambdaQueryWrapper<>();
        StrUtil.split(companyName, " ").forEach(name -> commonCompanyWrapper.like(CommonCompany::getCompanyName, name));
        List<CommonCompany> commonCompanyList = commonCompanyMapper.selectList(commonCompanyWrapper);
        List<Company> companyList = companyMapper.selectList(null);
        List<String> companyNumberList = companyList.stream()
                .map(Company::getCompanyNumber)
                .collect(Collectors.toList());
        Map<String, CommonCompany> commonCompanyMap = new HashMap<>();
        CollUtil.toMap(commonCompanyList, commonCompanyMap, CommonCompany::getCompanyNumber);
        companyNumberList.forEach(companyNumber -> MapUtil.removeAny(commonCompanyMap, companyNumber));
        List<SelectLabelDTO> selectLabelDTOList = new ArrayList<>();
        commonCompanyMap.forEach((key, value) -> {
            SelectLabelDTO selectLabelDTO = new SelectLabelDTO();
            selectLabelDTO.setLabel(value.getCompanyName());
            selectLabelDTO.setValue(value.getCompanyNumber());
            selectLabelDTOList.add(selectLabelDTO);
        });
        return selectLabelDTOList;
    }

    @Override
    public List<CompanyInformationDTO> companyAdd(CompanyOperationDTO companyOperationDTO) {
        List<Company> companyList = new ArrayList<>();

        LambdaQueryWrapper<Group> groupWrapper = new LambdaQueryWrapper<Group>()
                .eq(Group::getGroupNumber, companyOperationDTO.getCompanyAddDTO().getGroupNumber());
        Group group = groupMapper.selectOne(groupWrapper);

        companyOperationDTO.getCompanyAddDTO().getCompanyNumberList().forEach(companyNumber -> {
            CommonCompany commonCompany = commonCompanyMapper.selectOne(new LambdaQueryWrapper<CommonCompany>().eq(CommonCompany::getCompanyNumber, companyNumber));
            Company company = new Company();
            company.setGroupNumber(group.getGroupNumber());
            company.setCompanyNumber(commonCompany.getCompanyNumber());
            company.setCompanyName(commonCompany.getCompanyName());
            company.setOperator(companyOperationDTO.getUser().getUsername());
            companyMapper.insert(company);
            companyList.add(company);

            RequirementOperationLog requirementOperationLog = new RequirementOperationLog();
            requirementOperationLog.setGroupNumber(group.getGroupNumber());
            requirementOperationLog.setGroupName(group.getGroupName());
            requirementOperationLog.setCompanyNumber(company.getCompanyNumber());
            requirementOperationLog.setCompanyName(company.getCompanyName());
            requirementOperationLog.setOperationType("添加");
            requirementOperationLog.setOperatorId(companyOperationDTO.getUser().getId());
            requirementOperationLog.setOperator(companyOperationDTO.getUser().getUsername());

            requirementOperationLogMapper.insert(requirementOperationLog);
        });

        return companyList.stream()
                .map(company -> BeanUtil.toBean(company, CompanyInformationDTO.class))
                .collect(Collectors.toList());
    }

    @Override
    public boolean groupAdd(GroupAddDTO groupAddDTO) {
        List<Group> flagGroupList = groupMapper.selectList(null);
        AtomicBoolean atomicflag = new AtomicBoolean(true);
        flagGroupList.forEach(group -> groupAddDTO.getGroupNameList().forEach(groupName -> {
            if (StrUtil.equals(groupName, group.getGroupName())) {
                atomicflag.set(false);
            }
        }));
        if (!atomicflag.get()) {
            return false;
        }
        groupAddDTO.getGroupNameList().forEach(groupName -> {
            boolean flag = true;
            int i = 1;
            while (flag) {
                // 查询现有集团（过滤预分散体车间）
                List<Group> groupList = groupMapper.selectList(new LambdaQueryWrapper<Group>().notLike(Group::getGroupName, "预分散体车间").orderByAsc(Group::getGroupNumber));
                Group group = groupList.get(groupList.size() - 1);
                String groupNumber = Convert.toStr(Convert.toInt(group.getGroupNumber()) + i);
                LambdaQueryWrapper<Group> groupWrapper = new LambdaQueryWrapper<>();
                groupWrapper.eq(Group::getGroupNumber, groupNumber);
                List<Group> selectList = groupMapper.selectList(groupWrapper);
                if (CollUtil.isEmpty(selectList)) {
                    Group newGroup = new Group();
                    newGroup.setGroupNumber(groupNumber);
                    newGroup.setGroupName(groupName);
                    newGroup.setOperator(groupAddDTO.getUser().getUsername());
                    groupMapper.insert(newGroup);
                    flag = false;
                } else {
                    i++;
                }
            }
        });
        return true;
    }

    @Override
    public boolean companyDelete(CompanyOperationDTO companyOperationDTO) {
        LambdaQueryWrapper<Group> groupWrapper = new LambdaQueryWrapper<Group>()
                .eq(Group::getGroupNumber, companyOperationDTO.getCompanyInformationDTO().getGroupNumber());
        Group group = groupMapper.selectOne(groupWrapper);
        LambdaQueryWrapper<CommonCompany> commonCompanyWrapper = new LambdaQueryWrapper<CommonCompany>()
                .eq(CommonCompany::getCompanyNumber, companyOperationDTO.getCompanyInformationDTO().getCompanyNumber());
        CommonCompany company = commonCompanyMapper.selectOne(commonCompanyWrapper);

        RequirementOperationLog requirementOperationLog = new RequirementOperationLog();
        requirementOperationLog.setGroupNumber(group.getGroupNumber());
        requirementOperationLog.setGroupName(group.getGroupName());
        requirementOperationLog.setCompanyNumber(company.getCompanyNumber());
        requirementOperationLog.setCompanyName(company.getCompanyName());
        requirementOperationLog.setOperationType("删除");
        requirementOperationLog.setOperatorId(companyOperationDTO.getUser().getId());
        requirementOperationLog.setOperator(companyOperationDTO.getUser().getUsername());

        requirementOperationLogMapper.insert(requirementOperationLog);

        LambdaQueryWrapper<Company> companyWrapper = new LambdaQueryWrapper<Company>()
                .eq(Company::getGroupNumber, companyOperationDTO.getCompanyInformationDTO().getGroupNumber())
                .eq(Company::getCompanyNumber, companyOperationDTO.getCompanyInformationDTO().getCompanyNumber())
                .eq(Company::getCompanyName, companyOperationDTO.getCompanyInformationDTO().getCompanyName());
        return companyMapper.delete(companyWrapper) > 0;
    }

    @Override
    public List<RequirementCategoryDTO> getRequirementCategory(RequirementProductDTO requirementProductDTO) {
        MPJLambdaWrapper<GroupQualityStandard> groupQualityStandardWrapper = new MPJLambdaWrapper<GroupQualityStandard>()
                .selectAs(GroupQualityStandard::getRequirementCategory, RequirementCategoryDTO::getOldRequirementCategory)
                .selectAs(GroupQualityStandard::getRequirementCategory, RequirementCategoryDTO::getNewRequirementCategory)
                .selectAs(GroupQualityStandard::getDefaultStatus, RequirementCategoryDTO::getOldDefaultStatus)
                .selectAs(GroupQualityStandard::getDefaultStatus, RequirementCategoryDTO::getNewDefaultStatus)
                .selectCollection(CommonCompany.class, RequirementCategoryDTO::getCompanyList)
                .leftJoin(CommonCompany.class, on -> on.apply("FIND_IN_SET(t1.zwwldw_dwbh, t.kh_bh_list) > 0"))
                .eq(GroupQualityStandard::getGroupNumber, requirementProductDTO.getGroupNumber())
                .eq(GroupQualityStandard::getProductCategoryNumber, requirementProductDTO.getProductCategoryNumber())
                .eq(ObjectUtil.isNotNull(requirementProductDTO.getProductTypeNumber()), GroupQualityStandard::getProductNumber, requirementProductDTO.getProductTypeNumber())
                .isNull(ObjectUtil.isNull(requirementProductDTO.getProductTypeNumber()), GroupQualityStandard::getProductNumber);
        return groupQualityStandardMapper.selectJoinList(RequirementCategoryDTO.class, groupQualityStandardWrapper);
    }

    @Override
    public List<SelectLabelDTO> getRequirementCategorySelectLabel(ProductRequirementQuery productRequirementQuery) {
        MPJLambdaWrapper<GroupQualityStandard> groupQualityStandardWrapper = new MPJLambdaWrapper<GroupQualityStandard>()
                .eq(GroupQualityStandard::getGroupNumber, productRequirementQuery.getRequirementProductDTO().getGroupNumber())
                .eq(GroupQualityStandard::getProductCategoryNumber, productRequirementQuery.getRequirementProductDTO().getProductCategoryNumber())
                .eq(ObjectUtil.isNotNull(productRequirementQuery.getRequirementProductDTO().getProductTypeNumber()), GroupQualityStandard::getProductNumber, productRequirementQuery.getRequirementProductDTO().getProductTypeNumber())
                .isNull(ObjectUtil.isNull(productRequirementQuery.getRequirementProductDTO().getProductTypeNumber()), GroupQualityStandard::getProductNumber)
                .groupBy(GroupQualityStandard::getRequirementCategory);
        List<SelectLabelDTO> selectLabelDTOList = new ArrayList<>();
        groupQualityStandardMapper.selectList(groupQualityStandardWrapper).forEach(groupQualityStandard -> {
            SelectLabelDTO selectLabelDTO = new SelectLabelDTO();
            selectLabelDTO.setLabel(groupQualityStandard.getRequirementCategory());
            selectLabelDTO.setValue(groupQualityStandard.getRequirementCategory());
            selectLabelDTOList.add(selectLabelDTO);
        });
        return selectLabelDTOList;
    }

    @Override
    public boolean requirementItemEdit(RequirementItemHandleDTO requirementItemHandleDTO) {
        boolean defaultStatus = false;
        // 集团指标
        if (requirementItemHandleDTO.getGroupRequirement()) {
            // 判断当前类别是否为默认类别
            Optional<ProductRequirementDTO> existingDtoInListOpt = requirementItemHandleDTO.getProductRequirementDTOList().stream()
                .filter(dto -> ObjectUtil.isNotNull(dto.getId()))
                .findFirst();

            if (existingDtoInListOpt.isPresent()) {
                // 如果列表中的任何项目是现有项目（具有ID），
                // 则其类别的默认状态从该项目的记录中推断。
                GroupQualityStandard existingStandardRecord = groupQualityStandardMapper.selectById(existingDtoInListOpt.get().getId());
                if (existingStandardRecord != null) {
                    defaultStatus = existingStandardRecord.getDefaultStatus();
                }
                // 如果未找到ID对应的记录，defaultStatus 保持为 false（初始值）。
            } else {
                // 列表中的所有项目都是新项目。
                // 判断目标类别 (requirementItemHandleDTO.getRequirementCategory())
                // 是否是此产品的实际默认类别。
                RequirementProductDTO productCtx = requirementItemHandleDTO.getRequirementProductDTO();
                String targetCategory = requirementItemHandleDTO.getRequirementCategory();

                if (productCtx != null && StrUtil.isNotBlank(productCtx.getGroupNumber()) &&
                    StrUtil.isNotBlank(productCtx.getProductCategoryNumber()) && StrUtil.isNotBlank(targetCategory)) {

                    MPJLambdaWrapper<GroupQualityStandard> defaultCategoryQuery = new MPJLambdaWrapper<GroupQualityStandard>()
                        .eq(GroupQualityStandard::getGroupNumber, productCtx.getGroupNumber())
                        .eq(GroupQualityStandard::getProductCategoryNumber, productCtx.getProductCategoryNumber())
                        .eq(GroupQualityStandard::getDefaultStatus, true); // 查找标记为产品上下文默认值的标准

                    if (StrUtil.isBlank(productCtx.getProductTypeNumber())) {
                        defaultCategoryQuery.isNull(GroupQualityStandard::getProductNumber);
                    } else {
                        defaultCategoryQuery.eq(GroupQualityStandard::getProductNumber, productCtx.getProductTypeNumber());
                    }

                    List<GroupQualityStandard> defaultStandards = groupQualityStandardMapper.selectJoinList(GroupQualityStandard.class, defaultCategoryQuery);

                    if (CollUtil.isNotEmpty(defaultStandards)) {
                        GroupQualityStandard actualDefaultStandard = defaultStandards.get(0); // 假设只有一个唯一的默认值
                        if (actualDefaultStandard != null && StrUtil.equals(actualDefaultStandard.getRequirementCategory(), targetCategory)) {
                            // 正在添加新项目的类别是产品的默认类别。
                            defaultStatus = true;
                        }
                        // 否则：新项目属于非默认类别，或者 actualDefaultStandard 为 null，因此 defaultStatus 保持为 false。
                    }
                    // 否则：此产品未明确定义默认类别，因此 defaultStatus 保持为 false。
                }
                // 否则：productContext 或目标类别信息不足，defaultStatus 保持为 false。
            }
        }

        // 遍历产品检测项目DTO
        for (ProductRequirementDTO requirementDTO : requirementItemHandleDTO.getProductRequirementDTOList()) {
            // 产品检测项目DTOid不为空，判断为修改
            if (ObjectUtil.isNotNull(requirementDTO.getId())) {
                // 集团指标
                if (requirementItemHandleDTO.getGroupRequirement()) {
                    GroupQualityStandard groupQualityStandard = BeanUtil.copyProperties(requirementDTO, GroupQualityStandard.class);
                    groupQualityStandard.setOperator(requirementItemHandleDTO.getUser().getUsername());
                    groupQualityStandardMapper.updateById(groupQualityStandard);
                    // 通用指标
                } else {
                    GeneralQualityStandard generalQualityStandard = BeanUtil.copyProperties(requirementDTO, GeneralQualityStandard.class);
                    generalQualityStandard.setOperator(requirementItemHandleDTO.getUser().getUsername());
                    generalQualityStandardMapper.updateById(generalQualityStandard);
                }
                // 产品检测项目DTOid为空，判断为新增
            } else {
                // 集团指标
                if (requirementItemHandleDTO.getGroupRequirement()) {
                    GroupQualityStandard groupQualityStandard = BeanUtil.copyProperties(requirementDTO, GroupQualityStandard.class);
                    groupQualityStandard.setGroupNumber(requirementItemHandleDTO.getRequirementProductDTO().getGroupNumber());
                    groupQualityStandard.setProductCategoryNumber(requirementItemHandleDTO.getRequirementProductDTO().getProductCategoryNumber());
                    groupQualityStandard.setProductCategoryName(StrUtil.cleanBlank(requirementItemHandleDTO.getRequirementProductDTO().getProductCategory()));
                    groupQualityStandard.setProductNumber(requirementItemHandleDTO.getRequirementProductDTO().getProductTypeNumber());
                    groupQualityStandard.setProductName(StrUtil.cleanBlank(requirementItemHandleDTO.getRequirementProductDTO().getProductType()));
                    groupQualityStandard.setRequirementCategory(requirementItemHandleDTO.getRequirementCategory());
                    groupQualityStandard.setDefaultStatus(defaultStatus);
                    groupQualityStandard.setOperator(requirementItemHandleDTO.getUser().getUsername());

                    // 新增项目时，继承同类别下已有关联客户编号列表
                    MPJLambdaWrapper<GroupQualityStandard> existingInCategoryWrapper = new MPJLambdaWrapper<GroupQualityStandard>()
                        .select(GroupQualityStandard::getCompanyNumberList)
                        .eq(GroupQualityStandard::getGroupNumber, requirementItemHandleDTO.getRequirementProductDTO().getGroupNumber())
                        .eq(GroupQualityStandard::getProductCategoryNumber, requirementItemHandleDTO.getRequirementProductDTO().getProductCategoryNumber())
                        .eq(GroupQualityStandard::getRequirementCategory, requirementItemHandleDTO.getRequirementCategory());

                    if (StrUtil.isBlank(requirementItemHandleDTO.getRequirementProductDTO().getProductTypeNumber())) {
                        existingInCategoryWrapper.isNull(GroupQualityStandard::getProductNumber);
                    } else {
                        existingInCategoryWrapper.eq(GroupQualityStandard::getProductNumber, requirementItemHandleDTO.getRequirementProductDTO().getProductTypeNumber());
                    }
                    existingInCategoryWrapper.last("LIMIT 1"); // 只需要一个样本来获取列表

                    GroupQualityStandard siblingStandard = groupQualityStandardMapper.selectOne(existingInCategoryWrapper);
                    if (siblingStandard != null && StrUtil.isNotBlank(siblingStandard.getCompanyNumberList())) {
                        groupQualityStandard.setCompanyNumberList(siblingStandard.getCompanyNumberList());
                    }

                    groupQualityStandardMapper.insert(groupQualityStandard);
                    // 通用指标
                } else {
                    GeneralQualityStandard generalQualityStandard = BeanUtil.copyProperties(requirementDTO, GeneralQualityStandard.class);
                    generalQualityStandard.setProductCategoryNumber(requirementItemHandleDTO.getRequirementProductDTO().getProductCategoryNumber());
                    generalQualityStandard.setProductCategoryName(StrUtil.cleanBlank(requirementItemHandleDTO.getRequirementProductDTO().getProductCategory()));
                    generalQualityStandard.setProductNumber(requirementItemHandleDTO.getRequirementProductDTO().getProductTypeNumber());
                    generalQualityStandard.setProductName(StrUtil.cleanBlank(requirementItemHandleDTO.getRequirementProductDTO().getProductType()));
                    generalQualityStandard.setOperator(requirementItemHandleDTO.getUser().getUsername());
                    generalQualityStandardMapper.insert(generalQualityStandard);
                }
            }
        }
        // 已删除检测项目列表非空
        if (CollUtil.isNotEmpty(requirementItemHandleDTO.getDeletedProductRequirementDTOList())) {
            // 遍历已删除检测项目列表
            requirementItemHandleDTO.getDeletedProductRequirementDTOList().forEach(productRequirementDTO -> {
                // 已删除检测项目id不为空，判断为已存在检测项目
                if (ObjectUtil.isNotNull(productRequirementDTO.getId())) {
                    // 集团指标
                    if (requirementItemHandleDTO.getGroupRequirement()) {
                        // 先从数据库查询完整的记录信息
                        GroupQualityStandard existingStandard = groupQualityStandardMapper.selectById(productRequirementDTO.getId());
                        if (existingStandard != null) {
                            // 记录删除日志
                            logSingleGroupQualityStandardDeletion(existingStandard, requirementItemHandleDTO.getUser().getUsername());
                            // 执行删除操作
                            groupQualityStandardMapper.deleteById(productRequirementDTO.getId());
                        }
                        // 通用指标
                    } else {
                        // 先从数据库查询完整的记录信息
                        GeneralQualityStandard existingStandard = generalQualityStandardMapper.selectById(productRequirementDTO.getId());
                        if (existingStandard != null) {
                            // 记录删除日志
                            logSingleGeneralQualityStandardDeletion(existingStandard, requirementItemHandleDTO.getUser().getUsername());
                            // 执行删除操作
                            generalQualityStandardMapper.deleteById(productRequirementDTO.getId());
                        }
                    }
                }
            });
        }
        return true;
    }

    @Override
    public boolean requirementItemCopy(RequirementItemHandleDTO requirementItemHandleDTO) {
        // 复制至其他集团
        if (requirementItemHandleDTO.getCopyToGroup()) {
            List<Group> groupList = groupMapper.selectList(null);
            AtomicBoolean flag = new AtomicBoolean(true);
            groupList.forEach(group -> requirementItemHandleDTO.getGroupNameList().forEach(groupName -> {
                if (StrUtil.equals(groupName, group.getGroupName())) {
                    flag.set(false);
                }
            }));
            if (!flag.get()) {
                return false;
            }
            requirementCopyToGroup(requirementItemHandleDTO);
        } else {
            // 复制至其他产品
            requirementCopyToProduct(requirementItemHandleDTO);
        }
        return true;
    }

    @Override
    public boolean groupDelete(RequirementGroupDTO requirementGroupDTO) {
        LambdaQueryWrapper<Group> groupWrapper = new LambdaQueryWrapper<>();
        groupWrapper.eq(Group::getGroupNumber, requirementGroupDTO.getGroupNumber());
        groupMapper.delete(groupWrapper);
        LambdaQueryWrapper<Company> companyWrapper = new LambdaQueryWrapper<>();
        companyWrapper.eq(Company::getGroupNumber, requirementGroupDTO.getGroupNumber());
        companyMapper.delete(companyWrapper);
        LambdaQueryWrapper<GroupQualityStandard> groupQualityStandardWrapper = new LambdaQueryWrapper<>();
        groupQualityStandardWrapper.eq(GroupQualityStandard::getGroupNumber, requirementGroupDTO.getGroupNumber());
        // 查询要删除的集团质量标准，记录删除日志
        List<GroupQualityStandard> toDeleteStandards = groupQualityStandardMapper.selectList(groupQualityStandardWrapper);
        String operator = (requirementGroupDTO.getUser() != null) ? requirementGroupDTO.getUser().getUsername() : "未知用户";
        logGroupQualityStandardDeletion(toDeleteStandards, operator); // 使用真实用户信息
        groupQualityStandardMapper.delete(groupQualityStandardWrapper);
        return true;
    }

    @Override
    public boolean categoryDelete(CategoryDeleteDTO categoryDeleteDTO) {
        LambdaQueryWrapper<GroupQualityStandard> wrapper = new LambdaQueryWrapper<GroupQualityStandard>()
                .eq(GroupQualityStandard::getGroupNumber, categoryDeleteDTO.getRequirementProductDTO().getGroupNumber())
                .eq(GroupQualityStandard::getProductCategoryNumber, categoryDeleteDTO.getRequirementProductDTO().getProductCategoryNumber());
        if (StrUtil.isBlank(categoryDeleteDTO.getRequirementProductDTO().getProductTypeNumber())) {
            wrapper.isNull(GroupQualityStandard::getProductNumber)
                    .isNull(GroupQualityStandard::getProductName);
        } else {
            wrapper.eq(GroupQualityStandard::getProductNumber, categoryDeleteDTO.getRequirementProductDTO().getProductTypeNumber());
        }
        wrapper.eq(GroupQualityStandard::getRequirementCategory, categoryDeleteDTO.getRequirementCategoryDTO().getOldRequirementCategory());
        // 查询要删除的集团质量标准，记录删除日志
        List<GroupQualityStandard> toDeleteStandards = groupQualityStandardMapper.selectList(wrapper);
        String operator = (categoryDeleteDTO.getUser() != null) ? categoryDeleteDTO.getUser().getUsername() : "未知用户";
        logGroupQualityStandardDeletion(toDeleteStandards, operator); // 使用真实用户信息
        groupQualityStandardMapper.delete(wrapper);
        return true;
    }

    @Override
    public boolean productDelete(RequirementProductDTO requirementProductDTO) {
        if (requirementProductDTO.getGroupNumber() != null) {
            LambdaQueryWrapper<GroupQualityStandard> wrapper = new LambdaQueryWrapper<GroupQualityStandard>()
                    .eq(GroupQualityStandard::getGroupNumber, requirementProductDTO.getGroupNumber())
                    .eq(GroupQualityStandard::getProductCategoryNumber, requirementProductDTO.getProductCategoryNumber());
            if (StrUtil.isBlank(requirementProductDTO.getProductTypeNumber())) {
                wrapper.isNull(GroupQualityStandard::getProductNumber)
                        .isNull(GroupQualityStandard::getProductName);
            } else {
                wrapper.eq(GroupQualityStandard::getProductNumber, requirementProductDTO.getProductTypeNumber());
            }
            // 查询要删除的集团质量标准，记录删除日志
            List<GroupQualityStandard> toDeleteGroupStandards = groupQualityStandardMapper.selectList(wrapper);
            String operator = (requirementProductDTO.getUser() != null) ? requirementProductDTO.getUser().getUsername() : "未知用户";
            logGroupQualityStandardDeletion(toDeleteGroupStandards, operator); // 使用真实用户信息
            groupQualityStandardMapper.delete(wrapper);
        } else {
            LambdaQueryWrapper<GeneralQualityStandard> wrapper = new LambdaQueryWrapper<GeneralQualityStandard>()
                    .eq(GeneralQualityStandard::getProductCategoryNumber, requirementProductDTO.getProductCategoryNumber());
            if (StrUtil.isBlank(requirementProductDTO.getProductTypeNumber())) {
                wrapper.isNull(GeneralQualityStandard::getProductNumber)
                        .isNull(GeneralQualityStandard::getProductName);
            } else {
                wrapper.eq(GeneralQualityStandard::getProductNumber, requirementProductDTO.getProductTypeNumber());
            }
            // 查询要删除的通用质量标准，记录删除日志
            List<GeneralQualityStandard> toDeleteGeneralStandards = generalQualityStandardMapper.selectList(wrapper);
            String operator = (requirementProductDTO.getUser() != null) ? requirementProductDTO.getUser().getUsername() : "未知用户";
            logGeneralQualityStandardDeletion(toDeleteGeneralStandards, operator); // 使用真实用户信息
            generalQualityStandardMapper.delete(wrapper);
        }

        return true;
    }

    @Override
    public boolean requirementBatchEdit(RequirementItemHandleDTO requirementItemHandleDTO) {
        // 集团指标
        if (requirementItemHandleDTO.getGroupRequirement()) {
            requirementItemHandleDTO.getProductRequirementDTOList().forEach(productRequirementDTO -> requirementItemHandleDTO.getRequirementProductDTOList().forEach(requirementProductDTO -> {
                // 查询现有指标
                MPJLambdaWrapper<GroupQualityStandard> wrapper = new MPJLambdaWrapper<GroupQualityStandard>()
                        .selectAll(GroupQualityStandard.class)
                        .eq(GroupQualityStandard::getGroupNumber, requirementItemHandleDTO.getGroupNumber())
                        .eq(GroupQualityStandard::getProductCategoryNumber, requirementProductDTO.getProductCategoryNumber());
                if (StrUtil.isBlank(requirementProductDTO.getProductTypeNumber())) {
                    wrapper.isNull(GroupQualityStandard::getProductNumber)
                            .isNull(GroupQualityStandard::getProductName);
                } else {
                    wrapper.eq(GroupQualityStandard::getProductNumber, requirementProductDTO.getProductTypeNumber());
                }
                wrapper.eq(GroupQualityStandard::getQualityInspectionItem, productRequirementDTO.getQualityInspectionItem());
                List<GroupQualityStandard> groupQualityStandardList = groupQualityStandardMapper.selectJoinList(GroupQualityStandard.class, wrapper);
                // 查询现有类别指标
                MPJLambdaWrapper<GroupQualityStandard> categoryWrapper = new MPJLambdaWrapper<GroupQualityStandard>()
                        .selectAll(GroupQualityStandard.class)
                        .eq(GroupQualityStandard::getGroupNumber, requirementItemHandleDTO.getGroupNumber())
                        .eq(GroupQualityStandard::getProductCategoryNumber, requirementProductDTO.getProductCategoryNumber());
                if (StrUtil.isBlank(requirementProductDTO.getProductTypeNumber())) {
                    categoryWrapper.isNull(GroupQualityStandard::getProductNumber)
                            .isNull(GroupQualityStandard::getProductName);
                } else {
                    categoryWrapper.eq(GroupQualityStandard::getProductNumber, requirementProductDTO.getProductTypeNumber());
                }
                categoryWrapper.groupBy(GroupQualityStandard::getRequirementCategory);
                List<GroupQualityStandard> categoryList = groupQualityStandardMapper.selectJoinList(GroupQualityStandard.class, categoryWrapper);
                if (CollUtil.isEmpty(groupQualityStandardList)) {
                    categoryList.forEach(category -> {
                        BeanUtil.copyProperties(productRequirementDTO, category, "id");
                        category.setId(null);
                        category.setOperator(requirementItemHandleDTO.getUser().getUsername());
                        groupQualityStandardMapper.insert(category);
                    });
                } else {
                    if (categoryList.size() != groupQualityStandardList.size()) {
                        categoryList.forEach(groupQualityStandard -> {
                            BeanUtil.copyProperties(productRequirementDTO, groupQualityStandard, "id");
                            groupQualityStandard.setOperator(requirementItemHandleDTO.getUser().getUsername());
                            groupQualityStandardMapper.updateById(groupQualityStandard);
                        });
                    } else {
                        groupQualityStandardList.forEach(groupQualityStandard -> {
                            BeanUtil.copyProperties(productRequirementDTO, groupQualityStandard, "id");
                            groupQualityStandard.setOperator(requirementItemHandleDTO.getUser().getUsername());
                            groupQualityStandardMapper.updateById(groupQualityStandard);
                        });
                    }
                }
            }));
        } else {
            requirementItemHandleDTO.getProductRequirementDTOList().forEach(productRequirementDTO -> requirementItemHandleDTO.getRequirementProductDTOList().forEach(requirementProductDTO -> {
                MPJLambdaWrapper<GeneralQualityStandard> wrapper = new MPJLambdaWrapper<GeneralQualityStandard>()
                        .selectAll(GeneralQualityStandard.class)
                        .eq(GeneralQualityStandard::getProductCategoryNumber, requirementProductDTO.getProductCategoryNumber());
                if (StrUtil.isBlank(requirementProductDTO.getProductTypeNumber())) {
                    wrapper.isNull(GeneralQualityStandard::getProductNumber)
                            .isNull(GeneralQualityStandard::getProductName);
                } else {
                    wrapper.eq(GeneralQualityStandard::getProductNumber, requirementProductDTO.getProductTypeNumber());
                }
                wrapper.eq(GeneralQualityStandard::getQualityInspectionItem, productRequirementDTO.getQualityInspectionItem());
                List<GeneralQualityStandard> generalQualityStandardList = generalQualityStandardMapper.selectJoinList(GeneralQualityStandard.class, wrapper);
                if (CollUtil.isEmpty(generalQualityStandardList)) {
                    GeneralQualityStandard generalQualityStandard = BeanUtil.copyProperties(productRequirementDTO, GeneralQualityStandard.class, "id");
                    generalQualityStandard.setProductCategoryNumber(requirementProductDTO.getProductCategoryNumber());
                    generalQualityStandard.setProductCategoryName(requirementProductDTO.getProductCategory());
                    generalQualityStandard.setProductNumber(requirementProductDTO.getProductTypeNumber());
                    generalQualityStandard.setProductName(StrUtil.equals(requirementProductDTO.getProductType(), "所有产品") ? null : requirementProductDTO.getProductType());
                    generalQualityStandard.setShowStatus(true);
                    generalQualityStandard.setOperator(requirementItemHandleDTO.getUser().getUsername());
                    generalQualityStandardMapper.insert(generalQualityStandard);
                } else {
                    generalQualityStandardList.forEach(generalQualityStandard -> {
                        BeanUtil.copyProperties(productRequirementDTO, generalQualityStandard, "id");
                        generalQualityStandard.setOperator(requirementItemHandleDTO.getUser().getUsername());
                        generalQualityStandardMapper.updateById(generalQualityStandard);
                    });
                }
            }));
        }
        return true;
    }

    @Override
    public List<MaterialInformationDTO> getMaterialInformationList() {
        List<MaterialInformationDTO> resultList = new ArrayList<>();

        // 1. 查询所有物料信息
        List<MaterialInformation> allMaterialInformations = materialInformationMapper.selectList(null);
        if (CollUtil.isEmpty(allMaterialInformations)) {
            return resultList; // 没有物料信息，直接返回空列表
        }

        // 2. 提取物料信息中的所有 categoryNumber
        Set<String> categoryNumbers = allMaterialInformations.stream()
                .map(MaterialInformation::getCategoryNumber)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

        if (CollUtil.isEmpty(categoryNumbers)) {
            // 理论上如果allMaterialInformations不为空，categoryNumbers不应为空，除非数据问题
            // 但作为防御，如果提取不到有效的categoryNumber，可以考虑如何处理
            // 例如，将所有物料视为无类别的，或者直接返回（当前选择直接返回）
            return resultList;
        }

        // 3. 根据提取到的 categoryNumber 查询对应的 MaterialCategory
        List<MaterialCategory> materialCategories = materialCategoryMapper.selectList(
                new LambdaQueryWrapper<MaterialCategory>().in(MaterialCategory::getCategoryNumber, categoryNumbers)
        );
        Map<String, MaterialCategory> categoryMap = materialCategories.stream()
                .collect(Collectors.toMap(MaterialCategory::getCategoryNumber, category -> category, (o1, o2) -> o1)); // 防重复key

        // 4. 将物料信息按类别编号分组
        Map<String, List<MaterialInformation>> materialsByCategoryNumber = allMaterialInformations.stream()
                .filter(material -> StrUtil.isNotBlank(material.getCategoryNumber())) // 再次过滤以防万一
                .collect(Collectors.groupingBy(MaterialInformation::getCategoryNumber));

        // 5. 构建层级结构的 DTO
        for (Map.Entry<String, List<MaterialInformation>> entry : materialsByCategoryNumber.entrySet()) {
            String currentCategoryNumber = entry.getKey();
            List<MaterialInformation> materialsInThisCategory = entry.getValue();

            MaterialCategory currentCategory = categoryMap.get(currentCategoryNumber);
            if (currentCategory == null) {
                // 如果根据categoryNumber找不到对应的Category，可以选择跳过或记录日志
                log.warn("找不到物料类别编号 {} 对应的物料类别信息，跳过此类别下的物料。", currentCategoryNumber);
                continue;
            }

            MaterialInformationDTO categoryDto = new MaterialInformationDTO();
            categoryDto.setCategoryNumber(currentCategory.getCategoryNumber());
            categoryDto.setCategoryName(currentCategory.getCategoryName());
            categoryDto.setMaterialNumber(null); // 类别级别，物料编号为空
            categoryDto.setMaterialName(null);   // 类别级别，物料名称为空
            categoryDto.setMaterialCount(materialsInThisCategory.size()); // 该类别下的物料数量

            List<MaterialInformationDTO> children = new ArrayList<>();
            if (CollUtil.isNotEmpty(materialsInThisCategory)) {
                for (MaterialInformation material : materialsInThisCategory) {
                    MaterialInformationDTO materialDto = new MaterialInformationDTO();
                    materialDto.setCategoryNumber(material.getCategoryNumber());
                    materialDto.setCategoryName(currentCategory.getCategoryName()); // 从父类别获取名称
                    materialDto.setMaterialNumber(material.getMaterialNumber());
                    materialDto.setMaterialName(material.getMaterialName());
                    materialDto.setMaterialCount(null); // 单个物料，materialCount可设为null或0
                    materialDto.setSpecification(material.getPackagingSpecification());
                    // materialDto.setChildrenDTOList(null); // 子节点通常没有更深层的子节点
                    children.add(materialDto);
                }
            }
            categoryDto.setChildrenDTOList(children);
            resultList.add(categoryDto);
        }

        // 按类别名称排序
        CollUtil.sort(resultList, Comparator.comparing(MaterialInformationDTO::getCategoryName, Comparator.nullsLast(String::compareTo)));

        return resultList;
    }

    @Override
    public boolean requirementProductCreat(RequirementItemHandleDTO requirementItemHandleDTO) {
        if (requirementItemHandleDTO.getGroupRequirement()) {
            requirementItemHandleDTO.getMaterialInformationDTOList().forEach(materialInformationDTO -> {
                LambdaQueryWrapper<GroupQualityStandard> originalRequirementWrapper = new LambdaQueryWrapper<GroupQualityStandard>()
                        .eq(GroupQualityStandard::getGroupNumber, requirementItemHandleDTO.getGroupNumber())
                        .eq(GroupQualityStandard::getProductCategoryNumber, materialInformationDTO.getCategoryNumber());
                if (StrUtil.isBlank(materialInformationDTO.getMaterialNumber())) {
                    originalRequirementWrapper.isNull(GroupQualityStandard::getProductNumber)
                            .isNull(GroupQualityStandard::getProductName);
                } else {
                    originalRequirementWrapper.eq(GroupQualityStandard::getProductNumber, materialInformationDTO.getMaterialNumber());
                }
                // 查询要删除的集团质量标准，记录删除日志
                List<GroupQualityStandard> toDeleteGroupStandards = groupQualityStandardMapper.selectList(originalRequirementWrapper);
                logGroupQualityStandardDeletion(toDeleteGroupStandards, requirementItemHandleDTO.getUser().getUsername());
                groupQualityStandardMapper.delete(originalRequirementWrapper);
                requirementItemHandleDTO.getProductRequirementDTOList().forEach(productRequirementDTO -> {
                    GroupQualityStandard groupQualityStandard = BeanUtil.copyProperties(productRequirementDTO, GroupQualityStandard.class);
                    groupQualityStandard.setId(null);
                    groupQualityStandard.setGroupNumber(requirementItemHandleDTO.getGroupNumber());
                    groupQualityStandard.setProductCategoryNumber(materialInformationDTO.getCategoryNumber());
                    groupQualityStandard.setProductCategoryName(materialInformationDTO.getCategoryName());
                    if (!StrUtil.isBlank(materialInformationDTO.getMaterialNumber())) {
                        groupQualityStandard.setProductNumber(materialInformationDTO.getMaterialNumber());
                        groupQualityStandard.setProductName(materialInformationDTO.getMaterialName());
                    }
                    groupQualityStandard.setRequirementCategory("类别A");
                    groupQualityStandard.setDefaultStatus(true);
                    groupQualityStandard.setOperator(requirementItemHandleDTO.getUser().getUsername());
                    groupQualityStandardMapper.insert(groupQualityStandard);
                });
            });
        } else {
            requirementItemHandleDTO.getMaterialInformationDTOList().forEach(materialInformationDTO -> {
                LambdaQueryWrapper<GeneralQualityStandard> originalRequirementWrapper = new LambdaQueryWrapper<GeneralQualityStandard>()
                        .eq(GeneralQualityStandard::getProductCategoryNumber, materialInformationDTO.getCategoryNumber());
                if (StrUtil.isBlank(materialInformationDTO.getMaterialNumber())) {
                    originalRequirementWrapper.isNull(GeneralQualityStandard::getProductNumber)
                            .isNull(GeneralQualityStandard::getProductName);
                } else {
                    originalRequirementWrapper.eq(GeneralQualityStandard::getProductNumber, materialInformationDTO.getMaterialNumber());
                }
                // 查询要删除的通用质量标准，记录删除日志
                List<GeneralQualityStandard> toDeleteGeneralStandards = generalQualityStandardMapper.selectList(originalRequirementWrapper);
                logGeneralQualityStandardDeletion(toDeleteGeneralStandards, requirementItemHandleDTO.getUser().getUsername());
                generalQualityStandardMapper.delete(originalRequirementWrapper);
                requirementItemHandleDTO.getProductRequirementDTOList().forEach(productRequirementDTO -> {
                    GeneralQualityStandard generalQualityStandard = BeanUtil.copyProperties(productRequirementDTO, GeneralQualityStandard.class);
                    generalQualityStandard.setId(null);
                    generalQualityStandard.setProductCategoryNumber(materialInformationDTO.getCategoryNumber());
                    generalQualityStandard.setProductCategoryName(materialInformationDTO.getCategoryName());
                    if (!StrUtil.isBlank(materialInformationDTO.getMaterialNumber())) {
                        generalQualityStandard.setProductNumber(materialInformationDTO.getMaterialNumber());
                        generalQualityStandard.setProductName(materialInformationDTO.getMaterialName());
                    }
                    generalQualityStandard.setOperator(requirementItemHandleDTO.getUser().getUsername());
                    generalQualityStandardMapper.insert(generalQualityStandard);
                });
            });
        }
        return true;
    }

    @Override
    public boolean requirementProductCopy(RequirementItemHandleDTO requirementItemHandleDTO) {
        // 复制集团指标
        if (requirementItemHandleDTO.getGroupRequirement()) {
            // 指标复制至其他集团
            if (requirementItemHandleDTO.getCopyToGroup()) {
                // 遍历集团编号list
                requirementItemHandleDTO.getGroupNmberList().forEach(groupNumber -> {
                    // 遍历物料信息list
                    requirementItemHandleDTO.getMaterialInformationDTOList().forEach(materialInformationDTO -> {
                        // 删除原有指标
                        LambdaQueryWrapper<GroupQualityStandard> originalRequirementWrapper = new LambdaQueryWrapper<GroupQualityStandard>()
                                .eq(GroupQualityStandard::getProductCategoryNumber, materialInformationDTO.getCategoryNumber())
                                .eq(GroupQualityStandard::getGroupNumber, groupNumber);
                        if (StrUtil.isBlank(materialInformationDTO.getMaterialNumber())) {
                            originalRequirementWrapper.isNull(GroupQualityStandard::getProductNumber)
                                    .isNull(GroupQualityStandard::getProductName);
                        } else {
                            originalRequirementWrapper.eq(GroupQualityStandard::getProductNumber, materialInformationDTO.getMaterialNumber());
                        }
                        // 查询要删除的集团质量标准，记录删除日志
                        List<GroupQualityStandard> toDeleteGroupStandards = groupQualityStandardMapper.selectList(originalRequirementWrapper);
                        logGroupQualityStandardDeletion(toDeleteGroupStandards, requirementItemHandleDTO.getUser().getUsername());
                        groupQualityStandardMapper.delete(originalRequirementWrapper);
                        // 遍历产品指标list
                        requirementItemHandleDTO.getProductRequirementDTOList().forEach(productRequirementDTO -> {
                            GroupQualityStandard groupQualityStandard = BeanUtil.copyProperties(productRequirementDTO, GroupQualityStandard.class);
                            groupQualityStandard.setId(null);
                            groupQualityStandard.setGroupNumber(groupNumber);
                            groupQualityStandard.setProductCategoryNumber(materialInformationDTO.getCategoryNumber());
                            groupQualityStandard.setProductCategoryName(materialInformationDTO.getCategoryName());
                            if (!StrUtil.isBlank(materialInformationDTO.getMaterialNumber())) {
                                groupQualityStandard.setProductNumber(materialInformationDTO.getMaterialNumber());
                                groupQualityStandard.setProductName(materialInformationDTO.getMaterialName());
                            }
                            groupQualityStandard.setRequirementCategory("类别A");
                            groupQualityStandard.setDefaultStatus(true);
                            groupQualityStandard.setOperator(requirementItemHandleDTO.getUser().getUsername());
                            log.info(productRequirementDTO.getItemName());
                            log.info(groupQualityStandard.getItemName());
                            groupQualityStandardMapper.insert(groupQualityStandard);
                            log.info(groupQualityStandard.toString());
                        });
                    });
                });
                // 指标复制至本集团
            } else {
                // 遍历物料信息list
                requirementItemHandleDTO.getMaterialInformationDTOList().forEach(materialInformationDTO -> {
                    // 删除原有指标
                    LambdaQueryWrapper<GroupQualityStandard> originalRequirementWrapper = new LambdaQueryWrapper<GroupQualityStandard>()
                            .eq(GroupQualityStandard::getProductCategoryNumber, materialInformationDTO.getCategoryNumber())
                            .eq(GroupQualityStandard::getGroupNumber, requirementItemHandleDTO.getGroupNumber());
                    if (StrUtil.isBlank(materialInformationDTO.getMaterialNumber())) {
                        originalRequirementWrapper.isNull(GroupQualityStandard::getProductNumber)
                                .isNull(GroupQualityStandard::getProductName);
                    } else {
                        originalRequirementWrapper.eq(GroupQualityStandard::getProductNumber, materialInformationDTO.getMaterialNumber());
                    }
                    // 查询要删除的集团质量标准，记录删除日志
                    List<GroupQualityStandard> toDeleteGroupStandards = groupQualityStandardMapper.selectList(originalRequirementWrapper);
                    logGroupQualityStandardDeletion(toDeleteGroupStandards, requirementItemHandleDTO.getUser().getUsername());
                    groupQualityStandardMapper.delete(originalRequirementWrapper);
                    // 遍历产品指标list
                    requirementItemHandleDTO.getProductRequirementDTOList().forEach(productRequirementDTO -> {
                        GroupQualityStandard groupQualityStandard = BeanUtil.copyProperties(productRequirementDTO, GroupQualityStandard.class);
                        groupQualityStandard.setId(null);
                        groupQualityStandard.setGroupNumber(requirementItemHandleDTO.getGroupNumber());
                        groupQualityStandard.setProductCategoryNumber(materialInformationDTO.getCategoryNumber());
                        groupQualityStandard.setProductCategoryName(materialInformationDTO.getCategoryName());
                        if (!StrUtil.isBlank(materialInformationDTO.getMaterialNumber())) {
                            groupQualityStandard.setProductNumber(materialInformationDTO.getMaterialNumber());
                            groupQualityStandard.setProductName(materialInformationDTO.getMaterialName());
                        }
                        groupQualityStandard.setRequirementCategory("类别A");
                        groupQualityStandard.setDefaultStatus(true);
                        groupQualityStandard.setOperator(requirementItemHandleDTO.getUser().getUsername());
                        groupQualityStandardMapper.insert(groupQualityStandard);
                    });
                });
            }
        } else {
            // 复制通用指标
            // 指标复制至其他集团
            if (requirementItemHandleDTO.getCopyToGroup()) {
                // 遍历集团编号list
                requirementItemHandleDTO.getGroupNmberList().forEach(groupNumber -> {
                    // 遍历物料信息list
                    requirementItemHandleDTO.getMaterialInformationDTOList().forEach(materialInformationDTO -> {
                        // 删除原有指标
                        LambdaQueryWrapper<GroupQualityStandard> originalRequirementWrapper = new LambdaQueryWrapper<GroupQualityStandard>()
                                .eq(GroupQualityStandard::getProductCategoryNumber, materialInformationDTO.getCategoryNumber())
                                .eq(GroupQualityStandard::getGroupNumber, groupNumber);
                        if (StrUtil.isBlank(materialInformationDTO.getMaterialNumber())) {
                            originalRequirementWrapper.isNull(GroupQualityStandard::getProductNumber)
                                    .isNull(GroupQualityStandard::getProductName);
                        } else {
                            originalRequirementWrapper.eq(GroupQualityStandard::getProductNumber, materialInformationDTO.getMaterialNumber());
                        }
                        // 查询要删除的集团质量标准，记录删除日志
                        List<GroupQualityStandard> toDeleteGroupStandards = groupQualityStandardMapper.selectList(originalRequirementWrapper);
                        logGroupQualityStandardDeletion(toDeleteGroupStandards, requirementItemHandleDTO.getUser().getUsername());
                        groupQualityStandardMapper.delete(originalRequirementWrapper);
                        // 遍历产品指标list
                        requirementItemHandleDTO.getProductRequirementDTOList().forEach(productRequirementDTO -> {
                            GroupQualityStandard groupQualityStandard = BeanUtil.copyProperties(productRequirementDTO, GroupQualityStandard.class);
                            groupQualityStandard.setId(null);
                            groupQualityStandard.setGroupNumber(groupNumber);
                            groupQualityStandard.setProductCategoryNumber(materialInformationDTO.getCategoryNumber());
                            groupQualityStandard.setProductCategoryName(materialInformationDTO.getCategoryName());
                            if (!StrUtil.isBlank(materialInformationDTO.getMaterialNumber())) {
                                groupQualityStandard.setProductNumber(materialInformationDTO.getMaterialNumber());
                                groupQualityStandard.setProductName(materialInformationDTO.getMaterialName());
                            }
                            groupQualityStandard.setItemName(productRequirementDTO.getQualityInspectionItem());
                            groupQualityStandard.setRequirementCategory("类别A");
                            groupQualityStandard.setDefaultStatus(true);
                            groupQualityStandard.setOperator(requirementItemHandleDTO.getUser().getUsername());
                            groupQualityStandardMapper.insert(groupQualityStandard);
                        });
                    });
                });
            } else {
                // 指标复制至通用指标
                // 遍历物料信息list
                requirementItemHandleDTO.getMaterialInformationDTOList().forEach(materialInformationDTO -> {
                    // 删除原有指标
                    LambdaQueryWrapper<GeneralQualityStandard> originalRequirementWrapper = new LambdaQueryWrapper<GeneralQualityStandard>()
                            .eq(GeneralQualityStandard::getProductCategoryNumber, materialInformationDTO.getCategoryNumber());
                    if (StrUtil.isBlank(materialInformationDTO.getMaterialNumber())) {
                        originalRequirementWrapper.isNull(GeneralQualityStandard::getProductNumber)
                                .isNull(GeneralQualityStandard::getProductName);
                    } else {
                        originalRequirementWrapper.eq(GeneralQualityStandard::getProductNumber, materialInformationDTO.getMaterialNumber());
                    }
                    // 查询要删除的通用质量标准，记录删除日志
                    List<GeneralQualityStandard> toDeleteGeneralStandards = generalQualityStandardMapper.selectList(originalRequirementWrapper);
                    logGeneralQualityStandardDeletion(toDeleteGeneralStandards, requirementItemHandleDTO.getUser().getUsername());
                    generalQualityStandardMapper.delete(originalRequirementWrapper);
                    // 遍历产品指标list
                    requirementItemHandleDTO.getProductRequirementDTOList().forEach(productRequirementDTO -> {
                        GeneralQualityStandard generalQualityStandard = BeanUtil.copyProperties(productRequirementDTO, GeneralQualityStandard.class);
                        generalQualityStandard.setId(null);
                        generalQualityStandard.setProductCategoryNumber(materialInformationDTO.getCategoryNumber());
                        generalQualityStandard.setProductCategoryName(materialInformationDTO.getCategoryName());
                        if (!StrUtil.isBlank(materialInformationDTO.getMaterialNumber())) {
                            generalQualityStandard.setProductNumber(materialInformationDTO.getMaterialNumber());
                            generalQualityStandard.setProductName(materialInformationDTO.getMaterialName());
                        }
                        generalQualityStandard.setOperator(requirementItemHandleDTO.getUser().getUsername());
                        generalQualityStandardMapper.insert(generalQualityStandard);
                    });
                });
            }
        }
        return true;
    }

    @Override
    public boolean categoryEdit(ProductCategoryEditDTO productCategoryEditDTO) {
        productCategoryEditDTO.getRequirementCategoryDTOList().forEach(requirementCategoryDTO -> {
            MPJLambdaWrapper<GroupQualityStandard> wrapper = new MPJLambdaWrapper<GroupQualityStandard>()
                    .selectAll(GroupQualityStandard.class)
                    .eq(GroupQualityStandard::getGroupNumber, productCategoryEditDTO.getRequirementProductDTO().getGroupNumber())
                    .eq(GroupQualityStandard::getProductCategoryNumber, productCategoryEditDTO.getRequirementProductDTO().getProductCategoryNumber());
            if (StrUtil.isBlank(productCategoryEditDTO.getRequirementProductDTO().getProductTypeNumber())) {
                wrapper.isNull(GroupQualityStandard::getProductNumber)
                        .isNull(GroupQualityStandard::getProductName);
            } else {
                wrapper.eq(GroupQualityStandard::getProductNumber, productCategoryEditDTO.getRequirementProductDTO().getProductTypeNumber());
            }
            wrapper.eq(GroupQualityStandard::getRequirementCategory, requirementCategoryDTO.getOldRequirementCategory());
            List<GroupQualityStandard> groupQualityStandardList = groupQualityStandardMapper.selectJoinList(GroupQualityStandard.class, wrapper);
            if (CollUtil.isNotEmpty(groupQualityStandardList)) {
                groupQualityStandardList.forEach(groupQualityStandard -> {
                    if (!(Objects.equals(requirementCategoryDTO.getOldRequirementCategory(), requirementCategoryDTO.getNewRequirementCategory()) && Objects.equals(requirementCategoryDTO.getOldDefaultStatus(), requirementCategoryDTO.getNewDefaultStatus()))) {
                        groupQualityStandard.setRequirementCategory(requirementCategoryDTO.getNewRequirementCategory());
                        groupQualityStandard.setDefaultStatus(requirementCategoryDTO.getNewDefaultStatus());
                        groupQualityStandard.setOperator(productCategoryEditDTO.getUser().getUsername());
                        groupQualityStandardMapper.updateById(groupQualityStandard);
                    }
                });
            }
        });
        return true;
    }

    @Override
    public boolean requirementCategoryCreat(RequirementItemHandleDTO requirementItemHandleDTO) {
        requirementItemHandleDTO.getProductRequirementDTOList().forEach(productRequirementDTO -> {
            GroupQualityStandard groupQualityStandard = BeanUtil.copyProperties(productRequirementDTO, GroupQualityStandard.class, "id");
            groupQualityStandard.setGroupNumber(requirementItemHandleDTO.getRequirementProductDTO().getGroupNumber());
            groupQualityStandard.setProductCategoryNumber(requirementItemHandleDTO.getRequirementProductDTO().getProductCategoryNumber());
            groupQualityStandard.setProductCategoryName(requirementItemHandleDTO.getRequirementProductDTO().getProductCategory());
            groupQualityStandard.setProductNumber(requirementItemHandleDTO.getRequirementProductDTO().getProductTypeNumber());
            groupQualityStandard.setProductName(requirementItemHandleDTO.getRequirementProductDTO().getProductType());
            groupQualityStandard.setRequirementCategory(requirementItemHandleDTO.getNewCategory());
            groupQualityStandard.setDefaultStatus(false);
            groupQualityStandard.setOperator(requirementItemHandleDTO.getUser().getUsername());
            groupQualityStandardMapper.insert(groupQualityStandard);
        });
        return true;
    }

    @Override
    public IPage<RequirementOperationLogVO> getRequirementOperationLog(int currentPage, int pageSize, OperationLogQuery operationLogQuery) {
        MPJLambdaWrapper<RequirementOperationLog> logWrapper = new MPJLambdaWrapper<RequirementOperationLog>()
                .selectAll(RequirementOperationLog.class)
                .selectAs(User::getUsername, RequirementOperationLogVO::getOperator)
                .selectAs(RequirementOperationLog::getTimestamp, RequirementOperationLogVO::getOperationTime).leftJoin(User.class, User::getId, RequirementOperationLog::getOperatorId)
                .like(RequirementOperationLog::getGroupNumber, operationLogQuery.getGroupNumber())
                .like(RequirementOperationLog::getGroupName, operationLogQuery.getGroupName())
                .like(RequirementOperationLog::getCompanyNumber, operationLogQuery.getCompanyNumber())
                .like(RequirementOperationLog::getCompanyName, operationLogQuery.getCompanyName())
                .ge(Strings.isNotEmpty(operationLogQuery.getStartDate()), RequirementOperationLog::getTimestamp, operationLogQuery.getStartDate())
                .le(Strings.isNotEmpty(operationLogQuery.getEndDate()), RequirementOperationLog::getTimestamp, operationLogQuery.getEndDate() + " 23:59:59")
                .orderByDesc(RequirementOperationLog::getTimestamp);
        return requirementOperationLogMapper.selectJoinPage(new Page<>(currentPage, pageSize), RequirementOperationLogVO.class, logWrapper);
    }

    @Override
    public List<SelectLabelDTO> getLinkCompanyAddOptions(ProductRequirementQuery productRequirementQuery) {
        LambdaQueryWrapper<Company> wrapper = new LambdaQueryWrapper<Company>()
                .eq(Company::getGroupNumber, productRequirementQuery.getRequirementProductDTO().getGroupNumber());
        StrUtil.split(productRequirementQuery.getQueryString(), " ").forEach(name -> wrapper.like(Company::getCompanyName, name));
        List<Company> companyList = companyMapper.selectList(wrapper);
        List<RequirementCategoryDTO> requirementCategoryDTOList = getRequirementCategory(productRequirementQuery.getRequirementProductDTO());
        List<Company> notLinkedCompanyList = CollUtil.newArrayList(companyList);
        companyList.forEach(company -> requirementCategoryDTOList.forEach(requirementCategoryDTO -> requirementCategoryDTO.getCompanyList().forEach(commonCompany -> {
            if (StrUtil.equals(company.getCompanyNumber(), commonCompany.getCompanyNumber())) {
                notLinkedCompanyList.remove(company);
            }
        })));
        List<SelectLabelDTO> selectLabelDTOList = new ArrayList<>();
        notLinkedCompanyList.forEach(company -> {
            SelectLabelDTO selectLabelDTO = new SelectLabelDTO();
            selectLabelDTO.setLabel(company.getCompanyName());
            selectLabelDTO.setValue(company.getCompanyNumber());
            selectLabelDTOList.add(selectLabelDTO);
        });
        return selectLabelDTOList;
    }

    @Override
    public List<CompanyInformationDTO> linkCompanyAdd(CompanyOperationDTO companyOperationDTO) {
        MPJLambdaWrapper<GroupQualityStandard> wrapper = new MPJLambdaWrapper<GroupQualityStandard>()
                .selectAll(GroupQualityStandard.class)
                .eq(GroupQualityStandard::getGroupNumber, companyOperationDTO.getRequirementProductDTO().getGroupNumber())
                .eq(GroupQualityStandard::getProductCategoryNumber, companyOperationDTO.getRequirementProductDTO().getProductCategoryNumber());
        if (StrUtil.isBlank(companyOperationDTO.getRequirementProductDTO().getProductTypeNumber())) {
            wrapper.isNull(GroupQualityStandard::getProductNumber)
                    .isNull(GroupQualityStandard::getProductName);
        } else {
            wrapper.eq(GroupQualityStandard::getProductNumber, companyOperationDTO.getRequirementProductDTO().getProductTypeNumber());
        }
        wrapper.eq(GroupQualityStandard::getRequirementCategory, companyOperationDTO.getRequirementCategoryDTO().getOldRequirementCategory());
        List<GroupQualityStandard> groupQualityStandardList = groupQualityStandardMapper.selectJoinList(GroupQualityStandard.class, wrapper);
        StringBuilder companyNumberStringBuilder;
        String companyNumberList = groupQualityStandardList.get(0).getCompanyNumberList();
        if (StrUtil.isBlank(companyNumberList)) {
            companyNumberStringBuilder = new StringBuilder();
        } else {
            companyNumberStringBuilder = new StringBuilder(companyNumberList + ",");
        }
        companyOperationDTO.getCompanyAddDTO().getCompanyNumberList().forEach(companyNumber -> companyNumberStringBuilder.append(companyNumber).append(","));
        String companyNumberString = companyNumberStringBuilder.toString();
        String newCompanyNumberList = StrUtil.sub(companyNumberString, 0, companyNumberString.length() - 1);
        groupQualityStandardList.forEach(groupQualityStandard -> {
            groupQualityStandard.setCompanyNumberList(newCompanyNumberList);
            groupQualityStandard.setOperator(companyOperationDTO.getUser().getUsername());
            groupQualityStandardMapper.updateById(groupQualityStandard);
        });
        MPJLambdaWrapper<Company> companyWrapper = new MPJLambdaWrapper<Company>()
                .selectAs(Company::getGroupNumber, CompanyInformationDTO::getGroupNumber)
                .selectAs(Company::getCompanyNumber, CompanyInformationDTO::getCompanyNumber)
                .selectAs(Company::getCompanyName, CompanyInformationDTO::getCompanyName)
                .in(Company::getCompanyNumber, CollUtil.newArrayList(newCompanyNumberList.split(",")));
        return companyMapper.selectJoinList(CompanyInformationDTO.class, companyWrapper);
    }

    @Override
    public boolean linkCompanyDelete(CompanyOperationDTO companyOperationDTO) {
        MPJLambdaWrapper<GroupQualityStandard> wrapper = new MPJLambdaWrapper<GroupQualityStandard>()
                .selectAll(GroupQualityStandard.class)
                .eq(GroupQualityStandard::getGroupNumber, companyOperationDTO.getRequirementProductDTO().getGroupNumber())
                .eq(GroupQualityStandard::getProductCategoryNumber, companyOperationDTO.getRequirementProductDTO().getProductCategoryNumber());
        if (StrUtil.isBlank(companyOperationDTO.getRequirementProductDTO().getProductTypeNumber())) {
            wrapper.isNull(GroupQualityStandard::getProductNumber)
                    .isNull(GroupQualityStandard::getProductName);
        } else {
            wrapper.eq(GroupQualityStandard::getProductNumber, companyOperationDTO.getRequirementProductDTO().getProductTypeNumber());
        }
        wrapper.eq(GroupQualityStandard::getRequirementCategory, companyOperationDTO.getRequirementCategoryDTO().getOldRequirementCategory());
        List<GroupQualityStandard> groupQualityStandardList = groupQualityStandardMapper.selectJoinList(GroupQualityStandard.class, wrapper);
        List<String> oldCompanyNumberList = CollUtil.newArrayList(groupQualityStandardList.get(0).getCompanyNumberList().split(","));
        List<String> newCompanyNumberList = new ArrayList<>();
        oldCompanyNumberList.forEach(companyNumber -> {
            if (!StrUtil.equals(companyNumber, companyOperationDTO.getCompanyInformationDTO().getCompanyNumber())) {
                newCompanyNumberList.add(companyNumber);
            }
        });
        StringBuilder companyNumberStringBuilder = new StringBuilder();
        newCompanyNumberList.forEach(companyNumber -> companyNumberStringBuilder.append(companyNumber).append(","));
        String companyNumberString = companyNumberStringBuilder.toString();
        String newCompanyNumberListString = StrUtil.sub(companyNumberString, 0, companyNumberString.length() - 1);
        groupQualityStandardList.forEach(groupQualityStandard -> {
            groupQualityStandard.setCompanyNumberList(newCompanyNumberListString);
            groupQualityStandard.setOperator(companyOperationDTO.getUser().getUsername());
            groupQualityStandardMapper.updateById(groupQualityStandard);
        });
        return true;
    }

    private void requirementCopyToGroup(RequirementItemHandleDTO requirementItemHandleDTO) {
        List<Group> newGroupList = new ArrayList<>();
        if (requirementItemHandleDTO.getGroupRequirement()) {
            if (requirementItemHandleDTO.getCreatGroup()) {
                requirementItemHandleDTO.getGroupNameList().forEach(groupName -> {
                    boolean flag = true;
                    int i = 1;
                    while (flag) {
                        List<Group> groupList = groupMapper.selectList(new LambdaQueryWrapper<Group>().notLike(Group::getGroupName, "预分散体车间").orderByAsc(Group::getGroupNumber));
                        Group group = groupList.get(groupList.size() - 1);
                        String groupNumber = Convert.toStr(Convert.toInt(group.getGroupNumber()) + i);
                        LambdaQueryWrapper<Group> groupWrapper = new LambdaQueryWrapper<>();
                        groupWrapper.eq(Group::getGroupNumber, groupNumber);
                        List<Group> selectList = groupMapper.selectList(groupWrapper);
                        if (CollUtil.isEmpty(selectList)) {
                            Group newGroup = new Group();
                            newGroup.setGroupNumber(groupNumber);
                            newGroup.setGroupName(groupName);
                            newGroup.setOperator(requirementItemHandleDTO.getUser().getUsername());
                            groupMapper.insert(newGroup);
                            newGroupList.add(newGroup);
                            flag = false;
                        } else {
                            i++;
                        }
                    }
                });
            }
            requirementItemHandleDTO.getRequirementProductDTOList().forEach(requirementProductDTO -> {
                MPJLambdaWrapper<GroupQualityStandard> wrapper = new MPJLambdaWrapper<GroupQualityStandard>()
                        .selectAll(GroupQualityStandard.class)
                        .eq(GroupQualityStandard::getGroupNumber, requirementProductDTO.getGroupNumber())
                        .eq(GroupQualityStandard::getProductCategoryNumber, requirementProductDTO.getProductCategoryNumber());
                if (StrUtil.isBlank(requirementProductDTO.getProductTypeNumber())) {
                    wrapper.isNull(GroupQualityStandard::getProductNumber)
                            .isNull(GroupQualityStandard::getProductName);
                } else {
                    wrapper.eq(GroupQualityStandard::getProductNumber, requirementProductDTO.getProductTypeNumber());
                }
                List<GroupQualityStandard> groupQualityStandardList = groupQualityStandardMapper.selectJoinList(GroupQualityStandard.class, wrapper);
                if (requirementItemHandleDTO.getCreatGroup()) {
                    newGroupList.forEach(group -> groupQualityStandardList.forEach(groupQualityStandard -> {
                        groupQualityStandard.setId(null);
                        groupQualityStandard.setGroupNumber(group.getGroupNumber());
                        groupQualityStandard.setCompanyNumberList(null);
                        groupQualityStandard.setOperator(requirementItemHandleDTO.getUser().getUsername());
                        groupQualityStandardMapper.insert(groupQualityStandard);
                    }));
                } else {
                    requirementItemHandleDTO.getGroupNmberList().forEach(groupNumber -> {
                        LambdaQueryWrapper<GroupQualityStandard> originalRequirementWrapper = new LambdaQueryWrapper<GroupQualityStandard>()
                                .eq(GroupQualityStandard::getGroupNumber, groupNumber)
                                .eq(GroupQualityStandard::getProductCategoryNumber, requirementProductDTO.getProductCategoryNumber());
                        if (StrUtil.isBlank(requirementProductDTO.getProductTypeNumber())) {
                            originalRequirementWrapper.isNull(GroupQualityStandard::getProductNumber)
                                    .isNull(GroupQualityStandard::getProductName);
                        } else {
                            originalRequirementWrapper.eq(GroupQualityStandard::getProductNumber, requirementProductDTO.getProductTypeNumber());
                        }
                        // 查询要删除的集团质量标准，记录删除日志
                        List<GroupQualityStandard> toDeleteGroupStandards = groupQualityStandardMapper.selectList(originalRequirementWrapper);
                        logGroupQualityStandardDeletion(toDeleteGroupStandards, requirementItemHandleDTO.getUser().getUsername());
                        groupQualityStandardMapper.delete(originalRequirementWrapper);

                        groupQualityStandardList.forEach(groupQualityStandard -> {
                            groupQualityStandard.setId(null);
                            groupQualityStandard.setGroupNumber(groupNumber);
                            groupQualityStandard.setCompanyNumberList(null);
                            groupQualityStandard.setOperator(requirementItemHandleDTO.getUser().getUsername());
                            groupQualityStandardMapper.insert(groupQualityStandard);
                        });
                    });
                }
            });
        } else {
            if (requirementItemHandleDTO.getCreatGroup()) {
                requirementItemHandleDTO.getGroupNameList().forEach(groupName -> {
                    boolean flag = true;
                    int i = 1;
                    while (flag) {
                        List<Group> groupList = groupMapper.selectList(new LambdaQueryWrapper<Group>().notLike(Group::getGroupName, "预分散体车间").orderByAsc(Group::getGroupNumber));
                        Group group = groupList.get(groupList.size() - 1);
                        String groupNumber = Convert.toStr(Convert.toInt(group.getGroupNumber()) + i);
                        LambdaQueryWrapper<Group> groupWrapper = new LambdaQueryWrapper<>();
                        groupWrapper.eq(Group::getGroupNumber, groupNumber);
                        List<Group> selectList = groupMapper.selectList(groupWrapper);
                        if (CollUtil.isEmpty(selectList)) {
                            Group newGroup = new Group();
                            newGroup.setGroupNumber(groupNumber);
                            newGroup.setGroupName(groupName);
                            newGroup.setOperator(requirementItemHandleDTO.getUser().getUsername());
                            groupMapper.insert(newGroup);
                            newGroupList.add(newGroup);
                            flag = false;
                        } else {
                            i++;
                        }
                    }
                });
            }
            requirementItemHandleDTO.getRequirementProductDTOList().forEach(requirementProductDTO -> {
                MPJLambdaWrapper<GeneralQualityStandard> wrapper = new MPJLambdaWrapper<GeneralQualityStandard>()
                        .selectAll(GeneralQualityStandard.class)
                        .eq(GeneralQualityStandard::getProductCategoryNumber, requirementProductDTO.getProductCategoryNumber());
                if (StrUtil.isBlank(requirementProductDTO.getProductTypeNumber())) {
                    wrapper.isNull(GeneralQualityStandard::getProductNumber)
                            .isNull(GeneralQualityStandard::getProductName);
                } else {
                    wrapper.eq(GeneralQualityStandard::getProductNumber, requirementProductDTO.getProductTypeNumber());
                }
                List<GeneralQualityStandard> generalQualityStandardList = generalQualityStandardMapper.selectJoinList(GeneralQualityStandard.class, wrapper);
                if (requirementItemHandleDTO.getCreatGroup()) {
                    newGroupList.forEach(group -> generalQualityStandardList.forEach(generalQualityStandard -> {
                        GroupQualityStandard groupQualityStandard = BeanUtil.copyProperties(generalQualityStandard, GroupQualityStandard.class);
                        groupQualityStandard.setId(null);
                        groupQualityStandard.setGroupNumber(group.getGroupNumber());
                        groupQualityStandard.setItemName(generalQualityStandard.getQualityInspectionItem());
                        groupQualityStandard.setRequirementCategory("类别A");
                        groupQualityStandard.setDefaultStatus(true);
                        groupQualityStandard.setOperator(requirementItemHandleDTO.getUser().getUsername());
                        groupQualityStandardMapper.insert(groupQualityStandard);
                    }));
                } else {
                    requirementItemHandleDTO.getGroupNmberList().forEach(groupNumber -> {
                        LambdaQueryWrapper<GroupQualityStandard> originalRequirementWrapper = new LambdaQueryWrapper<GroupQualityStandard>()
                                .eq(GroupQualityStandard::getGroupNumber, groupNumber)
                                .eq(GroupQualityStandard::getProductCategoryNumber, requirementProductDTO.getProductCategoryNumber());
                        if (StrUtil.isBlank(requirementProductDTO.getProductTypeNumber())) {
                            originalRequirementWrapper.isNull(GroupQualityStandard::getProductNumber)
                                    .isNull(GroupQualityStandard::getProductName);
                        } else {
                            originalRequirementWrapper.eq(GroupQualityStandard::getProductNumber, requirementProductDTO.getProductTypeNumber());
                        }
                        // 查询要删除的集团质量标准，记录删除日志
                        List<GroupQualityStandard> toDeleteGroupStandards = groupQualityStandardMapper.selectList(originalRequirementWrapper);
                        logGroupQualityStandardDeletion(toDeleteGroupStandards, requirementItemHandleDTO.getUser().getUsername());
                        groupQualityStandardMapper.delete(originalRequirementWrapper);

                        generalQualityStandardList.forEach(generalQualityStandard -> {
                            GroupQualityStandard groupQualityStandard = BeanUtil.copyProperties(generalQualityStandard, GroupQualityStandard.class);
                            groupQualityStandard.setId(null);
                            groupQualityStandard.setGroupNumber(groupNumber);
                            groupQualityStandard.setItemName(generalQualityStandard.getQualityInspectionItem());
                            groupQualityStandard.setRequirementCategory("类别A");
                            groupQualityStandard.setDefaultStatus(true);
                            groupQualityStandard.setOperator(requirementItemHandleDTO.getUser().getUsername());
                            groupQualityStandardMapper.insert(groupQualityStandard);
                        });
                    });
                }
            });
        }
    }

    private void requirementCopyToProduct(RequirementItemHandleDTO requirementItemHandleDTO) {

    }

    /**
     * 记录通用质量标准删除日志
     * @param standards 要删除的通用质量标准列表
     * @param operator 删除操作人员
     */
    private void logGeneralQualityStandardDeletion(List<GeneralQualityStandard> standards, String operator) {
        if (CollUtil.isEmpty(standards)) {
            return;
        }
        
        standards.forEach(standard -> {
            RequirementDeleteOperationLog deleteLog = new RequirementDeleteOperationLog()
                    .setOriginalIndicatorId(standard.getId())
                    .setStandardType(1) // 1:通用质量标准
                    .setProductNumber(standard.getProductNumber())
                    .setProductName(standard.getProductName())
                    .setProductCategoryNumber(standard.getProductCategoryNumber())
                    .setProductCategoryName(standard.getProductCategoryName())
                    .setItem(standard.getQualityInspectionItem())
                    .setDeleteTime(LocalDateTime.now())
                    .setDeleteOperator(operator);
            
            requirementDeleteOperationLogMapper.insert(deleteLog);
        });
    }

    /**
     * 记录集团质量标准删除日志
     * @param standards 要删除的集团质量标准列表
     * @param operator 删除操作人员
     */
    private void logGroupQualityStandardDeletion(List<GroupQualityStandard> standards, String operator) {
        if (CollUtil.isEmpty(standards)) {
            return;
        }
        
        standards.forEach(standard -> {
            RequirementDeleteOperationLog deleteLog = new RequirementDeleteOperationLog()
                    .setOriginalIndicatorId(standard.getId())
                    .setStandardType(2) // 2:集团质量标准
                    .setGroupNumber(standard.getGroupNumber())
                    .setProductNumber(standard.getProductNumber())
                    .setProductName(standard.getProductName())
                    .setProductCategoryNumber(standard.getProductCategoryNumber())
                    .setProductCategoryName(standard.getProductCategoryName())
                    .setItem(standard.getQualityInspectionItem())
                    .setDeleteTime(LocalDateTime.now())
                    .setDeleteOperator(operator);
            
            requirementDeleteOperationLogMapper.insert(deleteLog);
        });
    }

    /**
     * 记录单个通用质量标准删除日志
     * @param standard 要删除的通用质量标准
     * @param operator 删除操作人员
     */
    private void logSingleGeneralQualityStandardDeletion(GeneralQualityStandard standard, String operator) {
        RequirementDeleteOperationLog deleteLog = new RequirementDeleteOperationLog()
                .setOriginalIndicatorId(standard.getId())
                .setStandardType(1) // 1:通用质量标准
                .setProductNumber(standard.getProductNumber())
                .setProductName(standard.getProductName())
                .setProductCategoryNumber(standard.getProductCategoryNumber())
                .setProductCategoryName(standard.getProductCategoryName())
                .setItem(standard.getQualityInspectionItem())
                .setDeleteTime(LocalDateTime.now())
                .setDeleteOperator(operator);
        
        requirementDeleteOperationLogMapper.insert(deleteLog);
    }

    /**
     * 记录单个集团质量标准删除日志
     * @param standard 要删除的集团质量标准
     * @param operator 删除操作人员
     */
    private void logSingleGroupQualityStandardDeletion(GroupQualityStandard standard, String operator) {
        RequirementDeleteOperationLog deleteLog = new RequirementDeleteOperationLog()
                .setOriginalIndicatorId(standard.getId())
                .setStandardType(2) // 2:集团质量标准
                .setGroupNumber(standard.getGroupNumber())
                .setProductNumber(standard.getProductNumber())
                .setProductName(standard.getProductName())
                .setProductCategoryNumber(standard.getProductCategoryNumber())
                .setProductCategoryName(standard.getProductCategoryName())
                .setItem(standard.getQualityInspectionItem())
                .setDeleteTime(LocalDateTime.now())
                .setDeleteOperator(operator);
        
        requirementDeleteOperationLogMapper.insert(deleteLog);
    }
}
