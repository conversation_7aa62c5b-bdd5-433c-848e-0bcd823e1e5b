package com.haihang.model.DO.operationLog;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 质量指标删除记录
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName(value = "cmf_operation_log_requirement_delete")
public class RequirementDeleteOperationLog {
    /**
     * 删除记录id
     */
    @TableId(value = "id")
    private Integer id;
    
    /**
     * 原指标id
     */
    private Integer originalIndicatorId;
    
    /**
     * 标准类型 (1:通用质量标准 2:集团质量标准)
     */
    private Integer standardType;
    
    /**
     * 集团编号（仅集团标准有效）
     */
    private String groupNumber;
    
    /**
     * 产品编号
     */
    private String productNumber;
    
    /**
     * 产品名称
     */
    private String productName;
    
    /**
     * 产品类别编号
     */
    private String productCategoryNumber;
    
    /**
     * 产品类别名称
     */
    private String productCategoryName;
    
    /**
     * 被删除的质检项目名称
     */
    private String item;
    
    /**
     * 删除时间
     */
    private LocalDateTime deleteTime;
    
    /**
     * 删除人员
     */
    private String deleteOperator;
} 