package com.haihang.model.DTO.requirementMaintenance;

import java.util.List;

import com.haihang.model.DO.user.User;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description:
 * @Author: zad
 * @Create: 2023/11/24 13:44
 */
@Data
@Accessors(chain = true)
public class MaterialInformationDTO {
    private String categoryNumber;
    private String categoryName;
    private String materialNumber;
    private String materialName;
    private Integer materialCount;
    private String specification;

    private User user;

    private List<MaterialInformationDTO> childrenDTOList;
}
