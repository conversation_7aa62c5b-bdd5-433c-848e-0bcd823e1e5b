<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <!-- 页面meta -->
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>客户审批管理-尚舜化工</title>
    <meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" name="viewport">
    <!-- 引入样式 -->
    <link rel="stylesheet" href="../../plugins/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="../../css/style.css">
    <link rel="icon" type="image/png" href="../../picture/logo.png">
    <style>
        [v-cloak] {
            display: none;
        }

        /* 字体设置 */
        @font-face {
            font-family: "zad";
            src: url("../../font/MiSans-Medium.woff2");
        }

        /* 字体应用组件 */
        html,
        body,
        button,
        input,
        select,
        textarea,
        form {
            font-family: "zad", sans-serif !important;
        }

        /* 防止滚动条遮挡表格内容 */
        .el-table__body-wrapper {
            z-index: 2;
        }

        .el-table__fixed {
            height: 100% !important;
        }

        .el-table__fixed-right {
            height: 100% !important;
        }

        .el-notification {
            z-index: 99999 !important;
        }

        /* 状态图标样式 */
        .status-pending {
            color: #909399;
        }

        .status-approved {
            color: #67C23A;
            font-weight: bold;
        }

        .status-rejected {
            color: #F56C6C;
            font-weight: bold;
        }

        /* 检测数据合格性样式 */
        .inspection-qualified {
            color: #67C23A;
            background-color: #f0f9ff;
        }

        .inspection-unqualified {
            color: #F56C6C;
            background-color: #fef0f0;
            font-weight: bold;
        }
    </style>
</head>

<body class="hold-transition">
    <div id="app" v-loading.fullscreen.lock="fullscreenLoading" v-cloak>
        <!--标题-->
        <div class="content-header">
            <h1>客户审批管理</h1>
        </div>
        <!--内容-->
        <div class="app-container">
            <div class="box">
                <br>
                <!--查询条件-->
                <div>
                    <el-row :gutter="20">
                        <!--所属公司-->
                        <el-col :span="3">
                            <el-select size="small" v-model="main_customerApprovalQuery.linkId" filterable multiple
                                collapse-tags default-first-option :reserve-keyword="true" placeholder="请选择公司"
                                :style="{width: '100%'}" value="" @change="main_getCustomerApprovalList()" clearable>
                                <el-option v-for="item in main_linkIdOptionList" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-col>
                        <!--审批状态-->
                        <el-col :span="3">
                            <el-select size="small" v-model="main_customerApprovalQuery.status" placeholder="请选择审批状态"
                                :style="{width: '100%'}" value="" @change="main_getCustomerApprovalList()" clearable>
                                <el-option v-for="item in main_statusOptionList" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-col>
                        <!--操作按钮-->
                        <el-col :span="6">
                            <el-tooltip content="查询客户审批列表" placement="top">
                                <el-button size="small" @click="main_getCustomerApprovalList()" type="primary"
                                    icon="el-icon-search" circle>
                                </el-button>
                            </el-tooltip>
                            <el-tooltip content="清空查询条件" placement="top">
                                <el-button size="small" @click="main_refreshSearch()" type="primary"
                                    icon="el-icon-refresh" circle>
                                </el-button>
                            </el-tooltip>
                        </el-col>
                    </el-row>
                    <br>
                    <el-row :gutter="20">
                        <!--客户名称-->
                        <el-col :span="3">
                            <el-input size="small" placeholder="请输入客户名称" clearable :style="{width: '100%'}"
                                v-model.trim="main_customerApprovalQuery.customerName"
                                @change="main_getCustomerApprovalList()" prefix-icon="el-icon-search">
                            </el-input>
                        </el-col>
                        <!--产品名称-->
                        <el-col :span="3">
                            <el-input size="small" placeholder="请输入产品名称" clearable :style="{width: '100%'}"
                                v-model.trim="main_customerApprovalQuery.productName"
                                @change="main_getCustomerApprovalList()" prefix-icon="el-icon-search">
                            </el-input>
                        </el-col>
                        <!--生产批号-->
                        <el-col :span="3">
                            <el-input size="small" placeholder="请输入生产批号" clearable :style="{width: '100%'}"
                                v-model.trim="main_customerApprovalQuery.productionBatch"
                                @change="main_getCustomerApprovalList()" prefix-icon="el-icon-search">
                            </el-input>
                        </el-col>
                        <!--质检批号-->
                        <el-col :span="3">
                            <el-input size="small" placeholder="请输入质检批号" clearable :style="{width: '100%'}"
                                v-model.trim="main_customerApprovalQuery.qualityInspectionBatch"
                                @change="main_getCustomerApprovalList()" prefix-icon="el-icon-search">
                            </el-input>
                        </el-col>
                        <el-col :span="4">
                            <el-date-picker size="small" placement="bottom-start" unlink-panels
                                v-model="main_customerApprovalQuery.queryDate" type="daterange" start-placeholder="开始日期"
                                end-placeholder="结束日期" value-format="yyyy-MM-dd" :style="{width: '100%'}"
                                @change="main_getCustomerApprovalList()" clearable>
                            </el-date-picker>
                        </el-col>
                    </el-row>
                </div>
                <br>
                <!--审批列表-->
                <div>
                    <el-table :row-style="{height: '50px'}" v-loading="main_tableLoading" :cell-style="{padding: '0px'}"
                        :data="main_customerApprovalList" highlight-current-row
                        :header-cell-style="{background: '#f2f4f7', color: '#606266'}" border>
                        <el-table-column fixed type="index" align="center" width="50">
                        </el-table-column>
                        <el-table-column prop="companyName" label="所属公司" align="center" width="100"
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="customerName" label="客户名称" align="center" width="200"
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="productName" label="产品名称" align="center" width="200"
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="productionBatch" label="生产批号" align="center" width="150"
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="qualityInspectionBatch" label="质检批号" align="center" width="150"
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="quantity" label="数量" align="center" width="100" show-overflow-tooltip>
                        </el-table-column>
                        <!-- <el-table-column prop="ourContractNumber" label="我方合同号" align="center" width="150"
                            show-overflow-tooltip>
                        </el-table-column> -->
                        <el-table-column prop="addUserName" label="申请人" align="center" width="100"
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="addTime" label="申请时间" align="center" width="200" show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="approver" label="审批人" align="center" width="100" show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="approvalTime" label="审批时间" align="center" width="200"
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column fixed="right" prop="status" label="状态" align="center" width="80"
                            show-overflow-tooltip>
                            <template slot-scope="scope">
                                <i v-if="scope.row.status === 0" class="el-icon-remove status-pending"
                                    style="font-size: 24px" title="待审批"></i>
                                <i v-if="scope.row.status === 1" class="el-icon-success status-approved"
                                    style="font-size: 24px" title="已通过"></i>
                                <i v-if="scope.row.status === 2" class="el-icon-error status-rejected"
                                    style="font-size: 24px" title="已否决"></i>
                            </template>
                        </el-table-column>
                        <el-table-column fixed="right" label="操作" align="center" width="80" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <el-tooltip content="查看详情" placement="top">
                                    <el-button type="primary" size="mini" @click="main_openDetailDialog(scope.row)"
                                        icon="el-icon-search" circle></el-button>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                    </el-table>
                    <!--分页组件-->
                    <el-pagination small style="text-align: right;" layout="prev, pager, next"
                        @current-change="main_handleCurrentChange" :current-page="main_customerApprovalQuery.current"
                        :page-size="main_customerApprovalQuery.size" :total="main_customerApprovalQuery.total">
                    </el-pagination>
                </div>

                <!--详情对话框-->
                <div>
                    <el-dialog :visible.sync="detail_dialogVisible" width="90%" top="5vh" center>
                        <template slot="title">
                            <div>
                                <span v-if="detail_data">{{ detail_data.customerName }}</span>
                                <el-divider direction="vertical"></el-divider>
                                <span v-if="detail_data">{{ detail_data.productName }}</span>
                            </div>
                        </template>
                        <div v-if="detail_data">
                            <!-- 检测指标 -->
                            <div v-if="detail_data.requirementData && detail_data.requirementData.length > 0">
                                <el-divider>检测指标</el-divider>
                                <el-table size="small" :row-style="{height: '35px'}" :cell-style="{padding: '0px'}"
                                    :data="detail_data.requirementData" highlight-current-row
                                    :header-cell-style="{background: '#f2f4f7', color: '#606266'}" border>
                                    <!-- 空列，与检测数据表格的固定列对齐 -->
                                    <el-table-column label="" align="center" width="120" fixed>
                                    </el-table-column>
                                    <el-table-column label="" align="center" width="120" fixed>
                                    </el-table-column>
                                    <el-table-column label="" align="center" width="120" fixed>
                                    </el-table-column>
                                    <el-table-column label="" align="center" width="100" fixed>
                                    </el-table-column>
                                    <!-- 检测指标列 -->
                                    <el-table-column
                                        v-for="(item, index) in detail_data.requirementColumns"
                                        :key="index" :label="item.label" align="center" :property="item.label" resizable
                                        :show-overflow-tooltip=true>
                                    </el-table-column>
                                    <!-- 空列，与检测数据表格的右侧固定列对齐 -->
                                    <el-table-column label="" align="center" width="80" fixed="right">
                                    </el-table-column>
                                    <el-table-column label="" align="center" width="80" fixed="right">
                                    </el-table-column>
                                </el-table>
                            </div>
                            
                            <!-- 检测数据 -->
                            <div v-if="detail_data.qualityInspectionData && detail_data.qualityInspectionData.length > 0">
                                <el-divider>检测数据</el-divider>
                                <el-table size="small" :row-style="{height: '35px'}" :cell-style="{padding: '0px'}"
                                    :data="detail_data.qualityInspectionData" highlight-current-row
                                    :header-cell-style="{background: '#f2f4f7', color: '#606266'}" border>
                                    <el-table-column label="质检批号" property="qualityInspectionBatch" align="center"
                                        resizable width="120" fixed :show-overflow-tooltip=true>
                                    </el-table-column>
                                    <el-table-column label="生产批号" property="productionBatch" align="center" resizable
                                        width="120" fixed :show-overflow-tooltip=true>
                                    </el-table-column>
                                    <el-table-column label="客户名称" property="customerName" align="center" resizable
                                        width="120" fixed :show-overflow-tooltip=true>
                                    </el-table-column>
                                    <el-table-column label="检测数量" property="quantity" align="center" resizable
                                        width="100" fixed :show-overflow-tooltip=true>
                                    </el-table-column>
                                    <!-- 动态检测数据列 -->
                                    <el-table-column
                                        v-for="(item, index) in detail_data.requirementColumns"
                                        :key="index" :label="item.label" align="center" :property="item.label" resizable
                                        :show-overflow-tooltip=true>
                                        <template slot-scope="scope">
                                            <span :class="getInspectionResultClass(scope.row[item.label], item.label)">
                                                {{ scope.row[item.label] || '-' }}
                                            </span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="结论" property="result" align="center" resizable width="80"
                                        fixed="right" :show-overflow-tooltip=true>
                                    </el-table-column>
                                    <!-- 抽检数据列 -->
                                    <el-table-column fixed="right" label="抽检数据" align="center" resizable width="80">
                                        <template slot-scope="scope">
                                            <el-popover v-if="scope.row.samplingData && scope.row.samplingData.length > 0"
                                                placement="top" width="400" trigger="hover">
                                                <el-table :header-cell-style="{background: '#f2f4f7', color: '#606266'}"
                                                    :data="scope.row.samplingData" size="mini" border>
                                                    <el-table-column prop="matchingName" label="抽检项目" min-width="100">
                                                    </el-table-column>
                                                    <el-table-column prop="value" label="检测数据" min-width="100">
                                                        <template slot-scope="props">
                                                            {{ props.row.value || '-' }}
                                                        </template>
                                                    </el-table-column>
                                                </el-table>
                                                <i slot="reference" class="el-icon-search"
                                                    style="cursor: pointer; color: #409EFF;"></i>
                                            </el-popover>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </div>
                        <span slot="footer" class="dialog-footer">
                            <el-tooltip v-if="detail_data && detail_data.status === 0" content="审批通过" placement="top">
                                <el-button type="success" size="small" 
                                    @click="main_approveCustomerApproval(detail_data, 1)" icon="el-icon-check" circle>
                                </el-button>
                            </el-tooltip>
                            <el-tooltip v-if="detail_data && detail_data.status === 0" content="审批否决" placement="top">
                                <el-button type="danger" size="small" 
                                    @click="main_approveCustomerApproval(detail_data, 2)" icon="el-icon-close" circle>
                                </el-button>
                            </el-tooltip>
                        </span>
                    </el-dialog>
                </div>
            </div>
        </div>
    </div>
</body>
<!-- 引入组件库 -->
<script src="../../js/vue.js"></script>
<script src="../../js/index.js"></script>
<script src="../../js/axios.min.js"></script>
<script type="text/javascript" src="../../js/jquery.min.js"></script>
<link rel="stylesheet" href="../../element-ui/lib/theme-chalk/index.css">
<script>
    let vue = new Vue({
        el: '#app',
        data: {
            /*通用*/
            fullscreenLoading: false,
            user: "",

            /*主列表*/
            main_customerApprovalQuery: {
                current: 1,
                size: 10,
                total: 0,
                linkId: [],
                customerName: "",
                productName: "",
                productionBatch: "",
                qualityInspectionBatch: "",
                status: "",
                ourContractNumber: "",
                queryDate: ["", ""],
                startDate: "",
                endDate: "",
            },
            main_customerApprovalList: [],
            main_tableLoading: false,
            main_linkIdOptionList: [],
            main_statusOptionList: [],

            /*详情对话框*/
            detail_dialogVisible: false,
            detail_data: null,
        },

        created() {
            let url = decodeURI(location.search).slice(1);
            let obj = {};
            let paramsArr = url.split('&')
            for (let i = 0, len = paramsArr.length; i < len; i++) {
                let arr = paramsArr[i].split('=')
                obj[arr[0]] = arr[1];
            }
            let param = {
                id: obj.uid,
                password: obj.pwd
            };
            axios.post("/user/verification", param).then((res) => {
                if (res.data.flag) {
                    this.user = res.data.data;
                    this.main_getCustomerApprovalList();
                    this.main_getLinkIdOptionList();
                    this.main_getStatusOptionList();
                }
            });
        },

        methods: {
            // 主页面_获取客户审批列表
            main_getCustomerApprovalList() {
                this.main_tableLoading = true;
                if (this.main_customerApprovalQuery.queryDate == null) {
                    this.main_customerApprovalQuery.queryDate = ["", ""];
                }
                this.main_customerApprovalQuery.startDate = this.main_customerApprovalQuery.queryDate[0];
                this.main_customerApprovalQuery.endDate = this.main_customerApprovalQuery.queryDate[1];

                axios.post("/outbound/customerApproval/getCustomerApprovalPage", this.main_customerApprovalQuery).then((res) => {
                    if (res.data.flag) {
                        this.main_customerApprovalQuery.current = Number(res.data.data.current);
                        this.main_customerApprovalQuery.size = Number(res.data.data.size);
                        this.main_customerApprovalQuery.total = Number(res.data.data.total);
                        this.main_customerApprovalList = res.data.data.records;
                    } else {
                        this.main_customerApprovalList = [];
                        this.$message.error("客户审批列表查询失败");
                    }
                }).finally(() => {
                    this.main_tableLoading = false;
                });
            },

            // 主页面_重置查询条件
            main_refreshSearch() {
                this.main_customerApprovalQuery = {
                    current: 1,
                    size: 10,
                    total: 0,
                    linkId: [],
                    customerName: "",
                    productName: "",
                    productionBatch: "",
                    qualityInspectionBatch: "",
                    status: "",
                    ourContractNumber: "",
                    queryDate: ["", ""],
                    startDate: "",
                    endDate: "",
                };
                this.main_getCustomerApprovalList();
            },

            // 主页面_页面切换
            main_handleCurrentChange(current) {
                this.main_customerApprovalQuery.current = current;
                this.main_getCustomerApprovalList();
            },

            // 主页面_打开详情对话框
            main_openDetailDialog(row) {
                axios.get("/outbound/customerApproval/getCustomerApprovalDetailWithInspectionData/" + row.id).then((res) => {
                    if (res.data.flag) {
                        this.detail_data = res.data.data;
                        this.detail_dialogVisible = true;
                    } else {
                        this.$message.error("获取详情失败");
                    }
                });
            },

            // 主页面_审批客户申请
            main_approveCustomerApproval(row, status) {
                let statusText = status === 1 ? "通过" : "否决";
                this.$confirm(`确定${statusText}该客户审批申请？`, "提示", { type: "warning" }).then(() => {
                    let params = {
                        id: row.id,
                        status: status,
                        approver: this.user.username
                    };

                    axios.post("/outbound/customerApproval/approveCustomerApproval", params).then((res) => {
                        if (res.data.flag) {
                            this.$message.success(res.data.msg);
                            this.main_getCustomerApprovalList();
                            this.detail_dialogVisible = false; // 审批成功后关闭详情弹窗
                        } else {
                            this.$message.error(res.data.msg);
                        }
                    });
                }).catch(() => {
                    this.$message.info("操作取消");
                });
            },

            // 主页面_获取公司选项列表
            main_getLinkIdOptionList() {
                this.main_linkIdOptionList = [
                    { label: "尚舜", value: 3 },
                    { label: "恒舜", value: 4 },
                    { label: "潍坊", value: 7 },
                    { label: "永舜", value: 8 }
                ];
            },

            // 主页面_获取状态选项列表
            main_getStatusOptionList() {
                this.main_statusOptionList = [
                    { label: "待审批", value: 0 },
                    { label: "已通过", value: 1 },
                    { label: "已否决", value: 2 }
                ];
            },

            // 获取检测结果类
            getInspectionResultClass(value, itemLabel) {
                if (!value || value === '' || value === '-') {
                    return '';
                }

                // 如果没有指标数据，无法判断
                if (!this.detail_data.requirementData || this.detail_data.requirementData.length === 0) {
                    return '';
                }

                // 查找对应的指标要求
                let requirement = '';
                for (let reqData of this.detail_data.requirementData) {
                    if (reqData[itemLabel]) {
                        requirement = reqData[itemLabel];
                        break;
                    }
                }

                if (!requirement) {
                    return '';
                }

                // 调用合格性判断方法
                if (this.utils_isQualified(itemLabel, requirement, value) < 1) {
                    return 'inspection-unqualified'; // 不合格标红
                } else {
                    return ''; // 合格时不标记任何样式
                }
            },

            // 工具_判断检测项目是否合格（复制自出库传递单页面）
            utils_isQualified(item, requirement, result) {
                try {
                    // 参数有效性检查
                    if (!item || item.trim() === '') {
                        console.warn('Invalid item parameter:', item);
                        return 0;
                    }

                    if (!requirement || requirement.trim() === '') {
                        console.warn('Invalid requirement parameter for item:', item, requirement);
                        return 0;
                    }

                    if (result === undefined || result === null || result.toString().trim() === '') {
                        console.warn('Invalid result parameter for item:', item, result);
                        return 0;
                    }

                    // 将requirement转换为字符串并去除首尾空格
                    requirement = String(requirement).trim();

                    // 特殊情况处理
                    if (requirement.includes("暂无") ||
                        requirement === "/" ||
                        requirement === "%" ||
                        requirement === "℃" ||
                        requirement === "N" ||
                        requirement === "CN" ||
                        requirement === "g" ||
                        requirement.includes("全通过") ||
                        requirement.includes("无黑点杂质")) {
                        return 1;
                    }

                    // 检查是否包含数字
                    if (!/[0-9]/.test(requirement)) {
                        return 1;
                    }

                    // 确保result可以被转换为数字，先去除首尾空格
                    const resultBigDecimal = Number(String(result).trim());
                    if (isNaN(resultBigDecimal)) {
                        console.warn('Result cannot be converted to number:', result);
                        return 0;
                    }

                    const min = Number.MIN_VALUE;
                    const max = Number.MAX_VALUE;

                    const pattern = /(?<symbol>[≥≤><＞＜=])?(?<value>\d+(\.\d+)?)((?<rangeSeparator>[±-])(?<maxValue>\d+(\.\d+)?))?(?<unit>[\p{L}\p{S}\p{Zs}\p{P}]*)/u;
                    const matcher = pattern.exec(requirement);

                    if (!matcher) {
                        console.warn('Pattern matching failed for requirement:', requirement);
                        return 0;
                    }

                    let { symbol, rangeSeparator, value, maxValue, unit } = matcher.groups;

                    // 数值转换检查
                    let valueNum = Number(value);
                    if (isNaN(valueNum)) {
                        console.warn('Value cannot be converted to number:', value);
                        return 0;
                    }

                    let maxValueNum = maxValue ? Number(maxValue) : Number.MAX_VALUE;
                    if (isNaN(maxValueNum)) {
                        console.warn('MaxValue cannot be converted to number:', maxValue);
                        return 0;
                    }

                    if (!symbol && !rangeSeparator) {
                        return Math.abs(valueNum - resultBigDecimal) < 0.00001 ? 1 : 0;
                    }

                    if (!symbol) {
                        if (rangeSeparator === "-") {
                            const centerValue = (valueNum + maxValueNum) / 2;
                            const deviation = Math.abs(maxValueNum - valueNum) / 2;
                            valueNum = centerValue;
                            maxValueNum = deviation;
                            symbol = "±";
                        } else if (rangeSeparator === "±") {
                            symbol = "±";
                        }
                    }

                    let newMin = min;
                    let newMax = max;

                    switch (symbol) {
                        case "≥":
                            newMin = valueNum;
                            break;
                        case "＞":
                        case ">":
                            newMin = valueNum + 0.00001;
                            break;
                        case "≤":
                            newMax = valueNum;
                            newMin = 0;
                            break;
                        case "＜":
                        case "<":
                            newMax = valueNum - 0.00001;
                            newMin = 0;
                            break;
                        case "±":
                            newMin = valueNum - maxValueNum;
                            newMax = valueNum + maxValueNum;
                            break;
                        default:
                            newMin = valueNum;
                            newMax = valueNum;
                            break;
                    }

                    // 特殊处理颗粒强度
                    if (item && item.includes("颗粒强度") &&
                        (requirement.includes("g") || requirement.includes("CN"))) {
                        newMin = Math.max(newMin, 10);
                    }

                    const greaterOrEqual = resultBigDecimal >= newMin;
                    const lessOrEqual = resultBigDecimal <= newMax;

                    return (greaterOrEqual && lessOrEqual) ? 1 : 0;
                } catch (error) {
                    console.error('Error in utils_isQualified for item:', item, error);
                    return 0;
                }
            },
        }
    })
</script>

</html>