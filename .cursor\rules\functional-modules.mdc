---
description: 
globs: 
alwaysApply: false
---
# 功能模块进阶分析

以下按业务模块梳理 Controller → Service → Mapper → Model 之间的对应关系，帮助快速定位并理解代码。

## 1. application 质量申请模块
- 控制层: [controller/application](mdc:src/main/java/com/haihang/controller/application)
  - 质量检验申请 `QualityInspectionApplicationController.java`
  - 申请统计 `QualityInspectionApplicationCountController.java`
  - 半成品申请 `SemiFinishedApplicationController.java`
  - 选项下拉 `ApplicationOptionController.java`
- 服务层: [service/application](mdc:src/main/java/com/haihang/service/application)
  - 公共逻辑位于 *common* 子包，成品/半成品分别在 *finished*、*semiFinished* 子包
- 数据访问: [mapper/application](mdc:src/main/java/com/haihang/mapper/application)
- 数据模型:
  - DO: [model/DO/application](mdc:src/main/java/com/haihang/model/DO/application)
  - DTO/VO: [model/DTO/application](mdc:src/main/java/com/haihang/model/DTO/application)

## 2. outbound 出库管理模块
- 控制层: [controller/outbound](mdc:src/main/java/com/haihang/controller/outbound)
  - 出库主单 `OutboundController`
  - 客户审批、调拨记录、运输报告分别由对应 Controller 处理
- 服务层: [service/outbound](mdc:src/main/java/com/haihang/service/outbound)
- Mapper: [mapper/outbound](mdc:src/main/java/com/haihang/mapper/outbound) 与 XML [resources/mapper/outbound](mdc:src/main/resources/mapper/outbound)
- 模型包: [model/DO/outbound](mdc:src/main/java/com/haihang/model/DO/outbound)

## 3. record 报告记录模块
- 控制层分成四类: [common](mdc:src/main/java/com/haihang/controller/record/common)、[finished](mdc:src/main/java/com/haihang/controller/record/finished)、[semiFinished](mdc:src/main/java/com/haihang/controller/record/semiFinished)、[deprecated](mdc:src/main/java/com/haihang/controller/record/deprecated)
- 服务实现包对应 `service/record/*/impl`
- Mapper 与 DO/DTO/VO 亦按分类放置

## 4. requirementMaintenance 执行标准&附加要求维护
- 控制层: [controller/requirementMaintenance](mdc:src/main/java/com/haihang/controller/requirementMaintenance)
  - 主表维护 `RequirementMaintenanceController`
  - 附加要求 `AdditionalRequirementController`
- 服务/Mapper/Model 路径同名子包

## 5. safetyManagement 安全管理模块
- 控制层: [controller/safetyManagement](mdc:src/main/java/com/haihang/controller/safetyManagement)
  - 安全项目维护、评估等
- 服务: [service/safetyManagement](mdc:src/main/java/com/haihang/service/safetyManagement)

## 6. sales 销售模块
- Delivery Note: [controller/sales/deliveryNote](mdc:src/main/java/com/haihang/controller/sales/deliveryNote) → 服务 [service/sales/deliveryNote](mdc:src/main/java/com/haihang/service/sales/deliveryNote)
- Sales Plan: [controller/sales/salesPlan](mdc:src/main/java/com/haihang/controller/sales/salesPlan) → 服务 [service/sales/salesPlan](mdc:src/main/java/com/haihang/service/sales/salesPlan)

## 7. supply 供应商管理模块
- 控制层: [controller/supply](mdc:src/main/java/com/haihang/controller/supply)
- 服务实现: [service/supply/impl](mdc:src/main/java/com/haihang/service/supply/impl)

## 8. standingBook 台账模块
- 控制层: [controller/standingBook](mdc:src/main/java/com/haihang/controller/standingBook)
- 查询条件对象位于 [model/Query/standingBook](mdc:src/main/java/com/haihang/model/Query/standingBook)

## 9. 通用/交叉模块
- 计算: [controller/calculate](mdc:src/main/java/com/haihang/controller/calculate) 处理报表数据计算
- Excel 导入导出: 控制器位于 [controller/excel](mdc:src/main/java/com/haihang/controller/excel)，解析/写入逻辑在 [handler/excel](mdc:src/main/java/com/haihang/handler/excel) 与 [utils/excel](mdc:src/main/java/com/haihang/utils/excel)
- 系统配置: [controller/config](mdc:src/main/java/com/haihang/controller/config)
- 用户/权限: [controller/user](mdc:src/main/java/com/haihang/controller/user)；用户表对应 Mapper/Service 置于同名包

---

> 通过此规则可从业务功能角度快速定位相关 Controller→Service→Mapper→Model，实现高效浏览与修改。

