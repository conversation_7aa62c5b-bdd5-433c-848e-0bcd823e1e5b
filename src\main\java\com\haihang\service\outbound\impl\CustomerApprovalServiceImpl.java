package com.haihang.service.outbound.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haihang.mapper.application.QualityInspectionApplicationMapper;
import com.haihang.mapper.outbound.CustomerApprovalMapper;
import com.haihang.mapper.record.BasicInformationMapper;
import com.haihang.mapper.record.BasicInformationNotSavedMapper;
import com.haihang.mapper.record.ItemPriorityForSortMapper;
import com.haihang.model.DO.application.QualityInspectionApplication;
import com.haihang.model.DO.outbound.OutboundCustomerApproval;
import com.haihang.model.DO.record.BasicInformation;
import com.haihang.model.DO.record.BasicInformationNotSaved;
import com.haihang.model.DO.record.ItemPriorityForSort;
import com.haihang.model.DTO.outbound.customerApproval.CustomerApprovalDTO;
import com.haihang.model.DTO.outbound.customerApproval.CustomerApprovalDetailDTO;
import com.haihang.model.DTO.outbound.transferRecord.TransferRecordProductionAndQualityDataDTO;
import com.haihang.model.Query.outbound.CustomerApprovalQuery;
import com.haihang.service.outbound.CustomerApprovalService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 客户审批Service实现类
 * @Author: zad
 * @Create: 2024/12/20
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CustomerApprovalServiceImpl extends ServiceImpl<CustomerApprovalMapper, OutboundCustomerApproval> 
        implements CustomerApprovalService {
    
    private final QualityInspectionApplicationMapper applicationMapper;
    private final BasicInformationMapper basicInformationMapper;
    private final BasicInformationNotSavedMapper basicInformationNotSavedMapper;
    private final ItemPriorityForSortMapper itemPriorityForSortMapper;
    
    @Override
    public IPage<CustomerApprovalDTO> getCustomerApprovalPage(Integer current, Integer size, CustomerApprovalQuery query) {
        // 使用纯MyBatis-Plus方式
        Page<OutboundCustomerApproval> page = new Page<>(current, size);
        LambdaQueryWrapper<OutboundCustomerApproval> wrapper = new LambdaQueryWrapper<>();
        
        // 构建查询条件
        if (query.getLinkId() != null && !query.getLinkId().isEmpty()) {
            wrapper.in(OutboundCustomerApproval::getLinkId, query.getLinkId());
        }
        if (StrUtil.isNotBlank(query.getCustomerName())) {
            wrapper.like(OutboundCustomerApproval::getCustomerName, query.getCustomerName());
        }
        if (StrUtil.isNotBlank(query.getProductName())) {
            wrapper.like(OutboundCustomerApproval::getProductName, query.getProductName());
        }
        if (StrUtil.isNotBlank(query.getProductionBatch())) {
            wrapper.like(OutboundCustomerApproval::getProductionBatch, query.getProductionBatch());
        }
        if (StrUtil.isNotBlank(query.getQualityInspectionBatch())) {
            wrapper.like(OutboundCustomerApproval::getQualityInspectionBatch, query.getQualityInspectionBatch());
        }
        if (query.getStatus() != null) {
            wrapper.eq(OutboundCustomerApproval::getStatus, query.getStatus());
        }
        if (StrUtil.isNotBlank(query.getOurContractNumber())) {
            wrapper.like(OutboundCustomerApproval::getOurContractNumber, query.getOurContractNumber());
        }
        if (StrUtil.isNotBlank(query.getStartDate())) {
            wrapper.ge(OutboundCustomerApproval::getAddTime, query.getStartDate() + " 00:00:00");
        }
        if (StrUtil.isNotBlank(query.getEndDate())) {
            wrapper.le(OutboundCustomerApproval::getAddTime, query.getEndDate() + " 23:59:59");
        }
        
        // 排序
        wrapper.orderByDesc(OutboundCustomerApproval::getAddTime, OutboundCustomerApproval::getId);
        
        IPage<OutboundCustomerApproval> resultPage = this.page(page, wrapper);
        
        // 转换为DTO
        Page<CustomerApprovalDTO> dtoPage = new Page<>(current, size);
        dtoPage.setTotal(resultPage.getTotal());
        dtoPage.setPages(resultPage.getPages());
        dtoPage.setCurrent(resultPage.getCurrent());
        dtoPage.setSize(resultPage.getSize());
        
        List<CustomerApprovalDTO> dtoList = resultPage.getRecords().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        dtoPage.setRecords(dtoList);
        
        return dtoPage;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean approveCustomerApproval(Integer id, Integer status, String approver) {
        try {
            OutboundCustomerApproval approval = this.getById(id);
            if (ObjUtil.isNull(approval)) {
                log.warn("客户审批记录不存在，id: {}", id);
                return false;
            }
            
            if (!approval.getStatus().equals(0)) {
                log.warn("客户审批记录已处理，无法重复审批，id: {}", id);
                return false;
            }
            
            approval.setStatus(status);
            approval.setApprover(approver);
            approval.setApprovalTime(LocalDateTime.now());
            
            return this.updateById(approval);
        } catch (Exception e) {
            log.error("审批客户申请失败", e);
            return false;
        }
    }
    
    @Override
    public CustomerApprovalDTO getCustomerApprovalDetail(Integer id) {
        try {
            OutboundCustomerApproval approval = this.getById(id);
            if (ObjUtil.isNull(approval)) {
                return null;
            }
            
            return convertToDTO(approval);
        } catch (Exception e) {
            log.error("获取客户审批详情失败", e);
            return null;
        }
    }
    
    @Override
    public CustomerApprovalDetailDTO getCustomerApprovalDetailWithInspectionData(Integer id) {
        try {
            OutboundCustomerApproval approval = this.getById(id);
            if (ObjUtil.isNull(approval)) {
                return null;
            }
            
            CustomerApprovalDetailDTO detailDTO = new CustomerApprovalDetailDTO();
            BeanUtils.copyProperties(approval, detailDTO);
            
            // 设置公司名称和状态描述
            setCompanyNameAndStatus(detailDTO, approval);
            
            // 获取质检申请信息
            QualityInspectionApplication application = applicationMapper.selectById(approval.getQualityInspectionId());
            if (application == null) {
                log.warn("未找到对应的质检申请，id: {}", approval.getQualityInspectionId());
                return detailDTO;
            }
            
            // 获取质检记录
            List<BasicInformation> basicInformationList = basicInformationMapper.selectList(
                new LambdaQueryWrapper<BasicInformation>()
                    .eq(BasicInformation::getApplicationId, application.getId())
                    .eq(BasicInformation::getIsDeleted, false)
            );
            
            if (CollUtil.isEmpty(basicInformationList)) {
                log.warn("未找到对应的质检记录，applicationId: {}", application.getId());
                return detailDTO;
            }
            
            // 查询未提交记录
            List<Integer> notSubmittedRecordIdList = basicInformationList.stream()
                    .map(BasicInformation::getNotSubmittedRecordId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
                    
            List<BasicInformationNotSaved> basicInformationNotSavedList = basicInformationNotSavedMapper.selectList(
                    new LambdaQueryWrapper<BasicInformationNotSaved>()
                            .in(BasicInformationNotSaved::getId, notSubmittedRecordIdList)
                            .eq(BasicInformationNotSaved::getCommitStatus, true)
                            .eq(BasicInformationNotSaved::getIsDeleted, false)
            );
            
            // 构建指标map（使用qualityRequirement而不是transferRecordDTO.getRequirement()）
            Map<String, Map<String, String>> recordRequirementMap = getRequirementMap(approval.getQualityRequirement());
            
            // 移除不需要的字段
            recordRequirementMap.remove("分子式");
            recordRequirementMap.remove("分子量");
            recordRequirementMap.remove("规格依据");
            recordRequirementMap.remove("客户产品代码");
            
            // 创建质检数据（简化版，不需要按生产批号分组）
            List<Map<String, Object>> qualityInspectionData = new ArrayList<>();
            for (BasicInformation basicInfo : basicInformationList) {
                Map<String, Object> map = BeanUtil.beanToMap(basicInfo, false, true);
                
                // 查找对应的 BasicInformationNotSaved 对象
                basicInformationNotSavedList.stream()
                        .filter(item -> item.getId().equals(basicInfo.getNotSubmittedRecordId()))
                        .findFirst()
                        .ifPresent(basicInformationNotSaved -> map.putAll(
                                BeanUtil.beanToMap(basicInformationNotSaved, false, true)));
                
                qualityInspectionData.add(map);
            }
            
            // 创建模拟的productionAndQualityDataList结构用于Handler填充数据
            List<TransferRecordProductionAndQualityDataDTO> mockProductionAndQualityDataList = new ArrayList<>();
            TransferRecordProductionAndQualityDataDTO mockData = new TransferRecordProductionAndQualityDataDTO();
            mockData.setQualityInspectionData(qualityInspectionData);
            mockProductionAndQualityDataList.add(mockData);
            
            // 创建 requirementColumns
            List<Map<String, Object>> requirementColumns = recordRequirementMap.entrySet().stream()
                    .map(entry -> {
                        Map<String, Object> newMap = new HashMap<>();
                        newMap.put("label", entry.getKey());
                        newMap.put("matchingName", entry.getValue().get("matchingName"));
                        newMap.put("itemName", entry.getValue().get("itemName"));
                        return newMap;
                    })
                    .collect(Collectors.toList());
                    
            // 创建 requirementData
            List<Map<String, Object>> requirementData = new ArrayList<>();
            Map<String, Object> requirementMap = recordRequirementMap.entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().get("requirement")));
            requirementData.add(requirementMap);
            
            // 获取排序信息并排序
            List<ItemPriorityForSort> itemPriorityForSorts = getItemPriorityForSorts(approval.getProductCode());
            sortRequirementColumns(requirementColumns, itemPriorityForSorts);
            
            // 填充检测数据
            recordRequirementMap.forEach((key, value) -> {
                try {
                    // 使用反射获取QualityInspectionHandlerFactory
                    Class<?> factoryClass = Class.forName("com.haihang.factory.QualityInspectionHandlerFactory");
                    Method getInvokeStrategyMethod = factoryClass.getMethod("getInvokeStrategy", String.class);
                    Object qualityInspectionHandler = getInvokeStrategyMethod.invoke(null, key.split(",")[0]);
                    
                    if (qualityInspectionHandler != null) {
                        // 调用outboundItemDataFillIn方法
                        Method fillInMethod = qualityInspectionHandler.getClass().getMethod("outboundItemDataFillIn", List.class, List.class);
                        fillInMethod.invoke(qualityInspectionHandler, notSubmittedRecordIdList, mockProductionAndQualityDataList);
                    }
                } catch (Exception e) {
                    log.warn("填充检测数据失败，key: {}", key, e);
                }
            });
            
            // 设置返回数据
            detailDTO.setRequirementColumns(requirementColumns);
            detailDTO.setRequirementData(requirementData);
            detailDTO.setQualityInspectionData(qualityInspectionData);
            
            return detailDTO;
        } catch (Exception e) {
            log.error("获取客户审批详情失败", e);
            return null;
        }
    }
    
    /**
     * 设置公司名称和状态描述
     */
    private void setCompanyNameAndStatus(CustomerApprovalDetailDTO detailDTO, OutboundCustomerApproval approval) {
        // 设置公司名称
        switch (approval.getLinkId()) {
            case 3:
                detailDTO.setCompanyName("尚舜");
                break;
            case 4:
                detailDTO.setCompanyName("恒舜");
                break;
            case 7:
                detailDTO.setCompanyName("潍坊");
                break;
            case 8:
                detailDTO.setCompanyName("永舜");
                break;
            default:
                detailDTO.setCompanyName("未知");
                break;
        }
        
        // 设置状态描述
        switch (approval.getStatus()) {
            case 0:
                detailDTO.setStatusDesc("待审批");
                break;
            case 1:
                detailDTO.setStatusDesc("已通过");
                break;
            case 2:
                detailDTO.setStatusDesc("已否决");
                break;
            default:
                detailDTO.setStatusDesc("未知状态");
                break;
        }
    }
    
    /**
     * 获取指标数据
     */
    private Map<String, Map<String, String>> getRequirementMap(String requirementStr) {
        Map<String, Map<String, String>> requirementMap = new HashMap<>();
        
        // 解析质量要求
        if (StrUtil.isNotBlank(requirementStr)) {
            requirementMap.putAll(parseRequirementString(requirementStr));
        }
        
        return requirementMap;
    }
    
    /**
     * 解析指标字符串（参考TransferRecordServiceImpl.getRequirementMap方法）
     */
    private Map<String, Map<String, String>> parseRequirementString(String requirementStr) {
        // 创建指标map
        Map<String, Map<String, String>> requirementMap = new HashMap<>();
        // 无对应指标
        if (requirementStr == null) {
            return requirementMap;
        }
        // 处理指标字符串
        // 分割检测项目
        String[] pairs = requirementStr.split(";");
        // 遍历检测项目
        for (String pair : pairs) {
            // 分割检测项目与指标
            String[] keyValue = pair.split(":");
            // 校验指标字符串格式
            if (keyValue.length == 2) {
                // 检测项目
                String item = keyValue[0];
                // 检测指标
                String itemRequirement = keyValue[1];
                // 指标map
                Map<String, String> itemMap = new HashMap<>();
                // 判断指标字符串格式
                if (item.contains("|")) {
                    // 新版指标格式
                    // 分割项目信息
                    String[] split = item.split("\\|");
                    // 指标显示状态
                    String itemDisplayStatus = split[2];
                    if (StrUtil.equals(itemDisplayStatus, "1")) {
                        itemMap.put("matchingName", split[0]);
                        itemMap.put("itemName", split[1]);
                        itemMap.put("requirement", itemRequirement);
                        if (split.length > 2) {
                            itemMap.put("showStatus", split[2]);
                        }
                        requirementMap.put(split[0], itemMap);
                    }
                } else {
                    // 旧版指标格式
                    itemMap.put("matchingName", item);
                    itemMap.put("itemName", item);
                    itemMap.put("requirement", itemRequirement);
                    itemMap.put("showStatus", "1");
                    requirementMap.put(item, itemMap);
                }
            }
        }
        return requirementMap;
    }
    
    /**
     * 获取项目优先级
     */
    private int getItemPriority(List<ItemPriorityForSort> itemPriorityList, String itemName) {
        return itemPriorityList.stream()
            .filter(item -> StrUtil.startWith(itemName, item.getItem()))
            .mapToInt(ItemPriorityForSort::getPriority)
            .findFirst()
            .orElse(Integer.MAX_VALUE);
    }
    
    /**
     * 获取检测项目优先级排序列表
     */
    private List<ItemPriorityForSort> getItemPriorityForSorts(String productCode) {
        // 简化实现，直接返回所有排序规则
        return itemPriorityForSortMapper.selectList(null);
    }
    
    /**
     * 对指标列进行排序
     */
    private void sortRequirementColumns(List<Map<String, Object>> requirementColumns, List<ItemPriorityForSort> itemPriorityForSorts) {
        // 创建比较器（参考TransferRecordServiceImpl的实现）
        Comparator<Map<String, Object>> comparator = (m1, m2) -> {
            String item1 = (String) m1.get("label");
            String item2 = (String) m2.get("label");
            
            // 查找优先级
            int priority1 = getItemPriority(itemPriorityForSorts, item1);
            int priority2 = getItemPriority(itemPriorityForSorts, item2);
            
            // 如果都有优先级，按优先级排序（优先级数值越大越靠前）
            if (priority1 != Integer.MAX_VALUE && priority2 != Integer.MAX_VALUE) {
                return Integer.compare(priority2, priority1);
            }
            // 如果都没有优先级，按拼音排序
            else if (priority1 == Integer.MAX_VALUE && priority2 == Integer.MAX_VALUE) {
                try {
                    Class<?> pinyinUtilClass = Class.forName("cn.hutool.extra.pinyin.PinyinUtil");
                    Method getPinyinMethod = pinyinUtilClass.getMethod("getPinyin", String.class);
                    String pinyin1 = (String) getPinyinMethod.invoke(null, item1);
                    String pinyin2 = (String) getPinyinMethod.invoke(null, item2);
                    return StrUtil.compare(pinyin1, pinyin2, true);
                } catch (Exception e) {
                    return item1.compareTo(item2);
                }
            }
            // 有优先级的排在前面，无优先级的排在后面
            else if (priority1 != Integer.MAX_VALUE) {
                return -1; // item1有优先级，排在前面
            } else {
                return 1;  // item2有优先级，item1无优先级，item1排在后面
            }
        };
        
        // 排序
        requirementColumns.sort(comparator);
        
        // 特殊处理：将"有效期"和"保质期"相关的项移到最后
        List<Map<String, Object>> expiryItems = new ArrayList<>();
        Iterator<Map<String, Object>> iterator = requirementColumns.iterator();
        while (iterator.hasNext()) {
            Map<String, Object> item = iterator.next();
            Object labelObj = item.get("label");
            if (labelObj != null && (labelObj.toString().contains("有效期") || labelObj.toString().contains("保质期"))) {
                expiryItems.add(item);
                iterator.remove();
            }
        }
        // 将有效期和保质期相关项添加到列表末尾
        requirementColumns.addAll(expiryItems);
    }
    
    /**
     * 转换实体为DTO
     */
    private CustomerApprovalDTO convertToDTO(OutboundCustomerApproval approval) {
        CustomerApprovalDTO dto = new CustomerApprovalDTO();
        BeanUtils.copyProperties(approval, dto);
        
        // 设置公司名称
        switch (approval.getLinkId()) {
            case 3:
                dto.setCompanyName("尚舜");
                break;
            case 4:
                dto.setCompanyName("恒舜");
                break;
            case 7:
                dto.setCompanyName("潍坊");
                break;
            case 8:
                dto.setCompanyName("永舜");
                break;
            default:
                dto.setCompanyName("未知");
                break;
        }
        
        // 设置状态描述
        switch (approval.getStatus()) {
            case 0:
                dto.setStatusDesc("待审批");
                break;
            case 1:
                dto.setStatusDesc("已通过");
                break;
            case 2:
                dto.setStatusDesc("已否决");
                break;
            default:
                dto.setStatusDesc("未知状态");
                break;
        }
        
        return dto;
    }
} 