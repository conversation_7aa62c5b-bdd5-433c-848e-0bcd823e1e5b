package com.haihang.handler.record.item;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.haihang.factory.QualityInspectionHandlerFactory;
import com.haihang.factory.TargetJudgmentHandlerFactory;
import com.haihang.handler.judgment.TargetJudgmentHandler;
import com.haihang.handler.record.QualityInspectionHandler;
import com.haihang.model.DO.application.QualityInspectionApplication;
import com.haihang.model.DO.item.InsolubleSulfurContent;
import com.haihang.model.DO.record.AnalysisRecordTemplate;
import com.haihang.model.DO.record.BasicInformation;
import com.haihang.model.DO.record.BasicInformationNotSaved;
import com.haihang.model.DO.record.MergedUnSubmittedRecord;
import com.haihang.model.DO.requirementMaintenance.GeneralQualityStandard;
import com.haihang.model.DO.requirementMaintenance.GroupQualityStandard;
import com.haihang.model.DTO.outbound.deprecated.OutboundReportItemDTO;
import com.haihang.model.DTO.outbound.transferRecord.TransferRecordProductionAndQualityDataDTO;
import com.haihang.model.DTO.record.common.RecordDataDTO;
import com.haihang.model.DTO.record.common.StandingBookDataDTO;
import com.haihang.model.DTO.record.finished.SamplingDataDTO;
import com.haihang.model.DTO.record.finished.SamplingItemDTO;
import com.haihang.model.DTO.record.finished.SamplingResultDTO;
import com.haihang.model.DTO.record.semiFinished.SemiFinishedRecordDataDTO;
import com.haihang.model.DTO.report.ReportItemDTO;
import com.haihang.model.VO.data.SamplingDataVO;
import com.haihang.model.VO.record.AnalysisResult;
import com.haihang.model.VO.record.FormData;
import com.haihang.model.VO.record.QualityIndicatorsVO;
import com.haihang.model.VO.report.RecordResultVO;
import com.haihang.model.VO.report.ReportVO;
import com.haihang.service.item.InsolubleSulfurContentService;
import com.haihang.utils.record.ItemCalculator;
import com.haihang.utils.record.RecordCreatUtil;
import lombok.extern.slf4j.Slf4j;
import org.jfree.data.category.DefaultCategoryDataset;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class InsolubleSulfurContentHandler extends QualityInspectionHandler {
    private final InsolubleSulfurContentService itemService;

    public InsolubleSulfurContentHandler(InsolubleSulfurContentService itemService) {
        this.itemService = itemService;
    }

    @Override
    public void qualityStandard(String qualityRequirement, AnalysisRecordTemplate analysisRecordTemplate) {
        TargetJudgmentHandler targetJudgmentHandler = TargetJudgmentHandlerFactory
                .getInvokeStrategy(qualityRequirement);
        analysisRecordTemplate.setInsolubleSulfurContent(true);
        if (qualityRequirement.contains("≥") && !qualityRequirement.contains("≤")) {
            analysisRecordTemplate.setInsolubleSulfurContentDetermineDetermine(false);
            analysisRecordTemplate.setInsolubleSulfurContentDetermine1(true);
            assert targetJudgmentHandler != null;
            analysisRecordTemplate
                    .setInsolubleSulfurContentTarget1(targetJudgmentHandler.singleTargetJudgment(qualityRequirement));
        } else if (qualityRequirement.contains("≤") && !qualityRequirement.contains("≥")) {
            analysisRecordTemplate.setInsolubleSulfurContentDetermineDetermine(false);
            analysisRecordTemplate.setInsolubleSulfurContentDetermine1(false);
            assert targetJudgmentHandler != null;
            analysisRecordTemplate
                    .setInsolubleSulfurContentTarget1(targetJudgmentHandler.singleTargetJudgment(qualityRequirement));
        } else {
            analysisRecordTemplate.setInsolubleSulfurContentDetermineDetermine(true);
            analysisRecordTemplate.setInsolubleSulfurContentDetermine1(true);
            analysisRecordTemplate.setInsolubleSulfurContentDetermine2(false);
            assert targetJudgmentHandler != null;
            Float[] floats = targetJudgmentHandler.doubleTargetJudgment(qualityRequirement);
            analysisRecordTemplate.setInsolubleSulfurContentTarget1(floats[0]);
            analysisRecordTemplate.setInsolubleSulfurContentTarget2(floats[1]);
        }
    }

    @Override
    public void itemRecordCreat(boolean samplingInspection, int samplingInspectionIndex,
            MergedUnSubmittedRecord mergedUnSubmittedRecord, BasicInformationNotSaved basicInformationNotSaved,
            String itemMatchingName, Map<String, Map<String, String>> generalMap,
            Map<String, Map<String, String>> groupMap, Map<String, Map<String, String>> internalControlMap) {
        InsolubleSulfurContent item = new InsolubleSulfurContent();

        item.setMergedUnSubmittedRecordId(mergedUnSubmittedRecord.getId());
        item.setUnSubmittedRecordId(basicInformationNotSaved.getId());
        item.setSamplingInspection(samplingInspection);
        item.setSamplingInspectionIndex(samplingInspectionIndex);

        Map<String, String> itemCreatMap = RecordCreatUtil.itemRecordCreat(itemMatchingName, generalMap, groupMap,
                internalControlMap);
        item.setMatchingName(itemCreatMap.get("itemMatchingName"));
        item.setName(itemCreatMap.get("name"));
        item.setGeneralRequirement(itemCreatMap.get("generalRequirement"));
        if (!StrUtil.contains(item.getGeneralRequirement(), "暂无")) {
            item.setOfTotal(StrUtil.contains(item.getGeneralRequirement(), "占总硫"));
        }
        item.setGroupRequirement(itemCreatMap.get("groupRequirement"));
        if (!StrUtil.contains(item.getGroupRequirement(), "暂无")) {
            item.setOfTotal(StrUtil.contains(item.getGroupRequirement(), "占总硫"));
        }
        item.setInternalControlRequirement(itemCreatMap.get("internalControlRequirement"));
        if (!StrUtil.contains(item.getInternalControlRequirement(), "暂无")) {
            item.setOfTotal(StrUtil.contains(item.getInternalControlRequirement(), "占总硫"));
        }
        if (StrUtil.contains(item.getMatchingName(), "可溶")) {
            item.setInsoluble(false);
        }
        if (StrUtil.contains(item.getMatchingName(), "不溶")) {
            item.setInsoluble(true);
        }
        itemService.save(item);
    }

    @Override
    public void itemSamplingRecordRemove(MergedUnSubmittedRecord mergedUnSubmittedRecord,
            BasicInformationNotSaved basicInformationNotSaved, String matchingName) {
        LambdaQueryWrapper<InsolubleSulfurContent> itemWrapper = new LambdaQueryWrapper<InsolubleSulfurContent>()
                .eq(InsolubleSulfurContent::getMergedUnSubmittedRecordId, mergedUnSubmittedRecord.getId())
                .eq(InsolubleSulfurContent::getUnSubmittedRecordId, basicInformationNotSaved.getId())
                .eq(InsolubleSulfurContent::getSamplingInspection, true)
                .eq(InsolubleSulfurContent::getMatchingName, matchingName);
        itemService.remove(itemWrapper);
    }

    @Override
    public void itemRecordCopy(int mergedRecordId, int recordId, int reInspectionMergedRecordId,
            int reInspectionRecordId, String itemMatchingName) {
        LambdaQueryWrapper<InsolubleSulfurContent> itemWrapper = new LambdaQueryWrapper<InsolubleSulfurContent>()
                .eq(InsolubleSulfurContent::getMergedUnSubmittedRecordId, mergedRecordId)
                .eq(InsolubleSulfurContent::getUnSubmittedRecordId, recordId)
                .eq(InsolubleSulfurContent::getMatchingName, itemMatchingName);
        List<InsolubleSulfurContent> list = itemService.list(itemWrapper);
        List<InsolubleSulfurContent> reInspectionList = new ArrayList<>();
        list.forEach(item -> {
            InsolubleSulfurContent reInspectionItem = BeanUtil.copyProperties(item, InsolubleSulfurContent.class, "id");
            reInspectionItem.setMergedUnSubmittedRecordId(reInspectionMergedRecordId);
            reInspectionItem.setUnSubmittedRecordId(reInspectionRecordId);
            reInspectionList.add(reInspectionItem);
        });
        itemService.saveBatch(reInspectionList);
    }

    @Override
    public void customerAdjustmentItemRecordCopy(QualityInspectionApplication qualityInspectionApplication,
            int mergedRecordId, int recordId, int reInspectionMergedRecordId, int reInspectionRecordId,
            String itemMatchingName, Map<String, Map<String, String>> generalMap,
            Map<String, Map<String, String>> groupMap, Map<String, Map<String, String>> internalControlMap) {
        LambdaQueryWrapper<InsolubleSulfurContent> itemWrapper = new LambdaQueryWrapper<InsolubleSulfurContent>()
                .eq(InsolubleSulfurContent::getMergedUnSubmittedRecordId, mergedRecordId)
                .eq(InsolubleSulfurContent::getUnSubmittedRecordId, recordId)
                .eq(InsolubleSulfurContent::getMatchingName, itemMatchingName);
        List<InsolubleSulfurContent> list = itemService.list(itemWrapper);
        List<InsolubleSulfurContent> reInspectionList = new ArrayList<>();
        list.forEach(item -> {
            InsolubleSulfurContent reInspectionItem = BeanUtil.copyProperties(item, InsolubleSulfurContent.class, "id");
            reInspectionItem.setMergedUnSubmittedRecordId(reInspectionMergedRecordId);
            reInspectionItem.setUnSubmittedRecordId(reInspectionRecordId);

            Map<String, String> itemCreatMap = RecordCreatUtil.itemRecordCreat(itemMatchingName, generalMap, groupMap,
                    internalControlMap);
            reInspectionItem.setMatchingName(itemCreatMap.get("itemMatchingName"));
            reInspectionItem.setName(itemCreatMap.get("name"));
            reInspectionItem.setGeneralRequirement(itemCreatMap.get("generalRequirement"));
            reInspectionItem.setGroupRequirement(itemCreatMap.get("groupRequirement"));
            reInspectionItem.setInternalControlRequirement(itemCreatMap.get("internalControlRequirement"));
            if (!StrUtil.contains(reInspectionItem.getGeneralRequirement(), "暂无")) {
                reInspectionItem.setOfTotal(StrUtil.contains(reInspectionItem.getGeneralRequirement(), "占总硫"));
            }
            if (!StrUtil.contains(reInspectionItem.getGroupRequirement(), "暂无")) {
                reInspectionItem.setOfTotal(StrUtil.contains(reInspectionItem.getGroupRequirement(), "占总硫"));
            }
            if (!StrUtil.contains(reInspectionItem.getInternalControlRequirement(), "暂无")) {
                reInspectionItem.setOfTotal(StrUtil.contains(reInspectionItem.getInternalControlRequirement(), "占总硫"));
            }

            if (ObjectUtil.isNotNull(reInspectionItem.getAverage())) {
                // 原记录占总硫，新纪录不占总硫
                if (item.getOfTotal() && !reInspectionItem.getOfTotal()) {
                    // 不溶性硫磺含量1
                    BigDecimal sulfurContent1 = NumberUtil.toBigDecimal(reInspectionItem.getSulfurContent1());
                    // 不溶性硫磺含量2
                    BigDecimal sulfurContent2 = NumberUtil.toBigDecimal(reInspectionItem.getSulfurContent2());
                    // 总磺含量
                    BigDecimal sulfurContent = NumberUtil.toBigDecimal(reInspectionItem.getSulfurContent());
                    // 重新计算不溶性硫磺含量
                    BigDecimal recalculatedValue1 = NumberUtil.mul(sulfurContent1, sulfurContent,
                            NumberUtil.toBigDecimal(0.01));
                    BigDecimal recalculatedValue2 = NumberUtil.mul(sulfurContent2, sulfurContent,
                            NumberUtil.toBigDecimal(0.01));
                    // 保留四位小数
                    reInspectionItem.setSulfurContent1(
                            NumberUtil.round(recalculatedValue1, 4, RoundingMode.HALF_EVEN).floatValue());
                    reInspectionItem.setSulfurContent2(
                            NumberUtil.round(recalculatedValue2, 4, RoundingMode.HALF_EVEN).floatValue());
                    // 重新计算修约前数值
                    BigDecimal beforeRounding = recalculatedValue1.add(recalculatedValue2)
                            .divide(NumberUtil.toBigDecimal(2), 4, RoundingMode.HALF_EVEN);
                    reInspectionItem.setBeforeRounding(beforeRounding.floatValue());
                    // 修约平均值
                    reInspectionItem.setAverage(beforeRounding.setScale(2, RoundingMode.HALF_EVEN).floatValue());
                }
                // 原记录不占总硫，新纪录占总硫
                if (!item.getOfTotal() && reInspectionItem.getOfTotal()) {
                    // 不溶性硫磺含量1
                    BigDecimal sulfurContent1 = NumberUtil.toBigDecimal(reInspectionItem.getSulfurContent1());
                    // 不溶性硫磺含量2
                    BigDecimal sulfurContent2 = NumberUtil.toBigDecimal(reInspectionItem.getSulfurContent2());
                    // 总磺含量
                    BigDecimal sulfurContent = NumberUtil.toBigDecimal(reInspectionItem.getSulfurContent());
                    // 重新计算不溶性硫磺含量并保留四位小数
                    BigDecimal recalculatedValue1 = sulfurContent1.divide(sulfurContent, 4, RoundingMode.HALF_EVEN)
                            .multiply(new BigDecimal("100"));
                    BigDecimal recalculatedValue2 = sulfurContent2.divide(sulfurContent, 4, RoundingMode.HALF_EVEN)
                            .multiply(new BigDecimal("100"));
                    reInspectionItem.setSulfurContent1(recalculatedValue1.floatValue());
                    reInspectionItem.setSulfurContent2(recalculatedValue2.floatValue());
                    // 重新计算修约前数值并保留四位小数
                    BigDecimal beforeRounding = recalculatedValue1.add(recalculatedValue2)
                            .divide(NumberUtil.toBigDecimal(2), 4, RoundingMode.HALF_EVEN);
                    reInspectionItem.setBeforeRounding(beforeRounding.floatValue());
                    // 修约平均值
                    reInspectionItem.setAverage(beforeRounding.setScale(2, RoundingMode.HALF_EVEN).floatValue());
                }
                Integer general = ItemCalculator.isQualified(reInspectionItem.getGeneralRequirement(),
                        reInspectionItem.getAverage());
                Integer group;
                if (reInspectionItem.getInternalControlRequirement().contains("暂无")) {
                    group = ItemCalculator.isQualified(reInspectionItem.getGroupRequirement(),
                            reInspectionItem.getAverage());
                } else {
                    group = ItemCalculator.isQualified(reInspectionItem.getInternalControlRequirement(),
                            reInspectionItem.getAverage());
                }

                reInspectionItem.setResult(general);
                reInspectionItem.setGroupResult(group);
                reInspectionItem.setFinalResult(ItemCalculator.generateFinalResult(general, group,
                        qualityInspectionApplication.getGroupRequirement() != null
                                || qualityInspectionApplication.getInternalControlRequirement() != null));
            }

            reInspectionList.add(reInspectionItem);
        });

        // 来源质检记录未查询到该检测项目
        if (CollUtil.isEmpty(list)) {
            // 查询相同处理id的非抽检记录
            itemWrapper = new LambdaQueryWrapper<InsolubleSulfurContent>()
                    .eq(InsolubleSulfurContent::getMergedUnSubmittedRecordId, mergedRecordId)
                    .eq(InsolubleSulfurContent::getMatchingName, itemMatchingName)
                    .eq(InsolubleSulfurContent::getSamplingInspection, false);
            list = itemService.list(itemWrapper);

            if (CollUtil.isNotEmpty(list)) {
                // 复制最后一个检测项目至目标质检记录
                InsolubleSulfurContent item = list.get(list.size() - 1);
                InsolubleSulfurContent reInspectionItem = BeanUtil.copyProperties(item, InsolubleSulfurContent.class,
                        "id");
                reInspectionItem.setMergedUnSubmittedRecordId(reInspectionMergedRecordId);
                reInspectionItem.setUnSubmittedRecordId(reInspectionRecordId);

                Map<String, String> itemCreatMap = RecordCreatUtil.itemRecordCreat(itemMatchingName, generalMap,
                        groupMap, internalControlMap);
                reInspectionItem.setMatchingName(itemCreatMap.get("itemMatchingName"));
                reInspectionItem.setName(itemCreatMap.get("name"));
                reInspectionItem.setGeneralRequirement(itemCreatMap.get("generalRequirement"));
                reInspectionItem.setGroupRequirement(itemCreatMap.get("groupRequirement"));
                reInspectionItem.setInternalControlRequirement(itemCreatMap.get("internalControlRequirement"));
                if (!StrUtil.contains(reInspectionItem.getGeneralRequirement(), "暂无")) {
                    reInspectionItem.setOfTotal(StrUtil.contains(reInspectionItem.getGeneralRequirement(), "占总硫"));
                }
                if (!StrUtil.contains(reInspectionItem.getGroupRequirement(), "暂无")) {
                    reInspectionItem.setOfTotal(StrUtil.contains(reInspectionItem.getGroupRequirement(), "占总硫"));
                }
                if (!StrUtil.contains(reInspectionItem.getInternalControlRequirement(), "暂无")) {
                    reInspectionItem
                            .setOfTotal(StrUtil.contains(reInspectionItem.getInternalControlRequirement(), "占总硫"));
                }

                if (ObjectUtil.isNotNull(reInspectionItem.getAverage())) {
                    // 原记录占总硫，新纪录不占总硫
                    if (item.getOfTotal() && !reInspectionItem.getOfTotal()) {
                        // 不溶性硫磺含量1
                        BigDecimal sulfurContent1 = NumberUtil.toBigDecimal(reInspectionItem.getSulfurContent1());
                        // 不溶性硫磺含量2
                        BigDecimal sulfurContent2 = NumberUtil.toBigDecimal(reInspectionItem.getSulfurContent2());
                        // 总磺含量
                        BigDecimal sulfurContent = NumberUtil.toBigDecimal(reInspectionItem.getSulfurContent());
                        // 重新计算不溶性硫磺含量
                        BigDecimal recalculatedValue1 = NumberUtil.mul(sulfurContent1, sulfurContent,
                                NumberUtil.toBigDecimal(0.01));
                        BigDecimal recalculatedValue2 = NumberUtil.mul(sulfurContent2, sulfurContent,
                                NumberUtil.toBigDecimal(0.01));
                        // 保留四位小数
                        reInspectionItem.setSulfurContent1(
                                NumberUtil.round(recalculatedValue1, 4, RoundingMode.HALF_EVEN).floatValue());
                        reInspectionItem.setSulfurContent2(
                                NumberUtil.round(recalculatedValue2, 4, RoundingMode.HALF_EVEN).floatValue());
                        // 重新计算修约前数值
                        BigDecimal beforeRounding = recalculatedValue1.add(recalculatedValue2)
                                .divide(NumberUtil.toBigDecimal(2), 4, RoundingMode.HALF_EVEN);
                        reInspectionItem.setBeforeRounding(beforeRounding.floatValue());
                        // 修约平均值
                        reInspectionItem.setAverage(beforeRounding.setScale(2, RoundingMode.HALF_EVEN).floatValue());
                    }
                    // 原记录不占总硫，新纪录占总硫
                    if (!item.getOfTotal() && reInspectionItem.getOfTotal()) {
                        // 不溶性硫磺含量1
                        BigDecimal sulfurContent1 = NumberUtil.toBigDecimal(reInspectionItem.getSulfurContent1());
                        // 不溶性硫磺含量2
                        BigDecimal sulfurContent2 = NumberUtil.toBigDecimal(reInspectionItem.getSulfurContent2());
                        // 总磺含量
                        BigDecimal sulfurContent = NumberUtil.toBigDecimal(reInspectionItem.getSulfurContent());
                        // 重新计算不溶性硫磺含量并保留四位小数
                        BigDecimal recalculatedValue1 = sulfurContent1.divide(sulfurContent, 4, RoundingMode.HALF_EVEN)
                                .multiply(new BigDecimal("100"));
                        BigDecimal recalculatedValue2 = sulfurContent2.divide(sulfurContent, 4, RoundingMode.HALF_EVEN)
                                .multiply(new BigDecimal("100"));
                        reInspectionItem.setSulfurContent1(recalculatedValue1.floatValue());
                        reInspectionItem.setSulfurContent2(recalculatedValue2.floatValue());
                        // 重新计算修约前数值并保留四位小数
                        BigDecimal beforeRounding = recalculatedValue1.add(recalculatedValue2)
                                .divide(NumberUtil.toBigDecimal(2), 4, RoundingMode.HALF_EVEN);
                        reInspectionItem.setBeforeRounding(beforeRounding.floatValue());
                        // 修约平均值
                        reInspectionItem.setAverage(beforeRounding.setScale(2, RoundingMode.HALF_EVEN).floatValue());
                    }
                    Integer general = ItemCalculator.isQualified(reInspectionItem.getGeneralRequirement(),
                            reInspectionItem.getAverage());
                    Integer group;
                    if (reInspectionItem.getInternalControlRequirement().contains("暂无")) {
                        group = ItemCalculator.isQualified(reInspectionItem.getGroupRequirement(),
                                reInspectionItem.getAverage());
                    } else {
                        group = ItemCalculator.isQualified(reInspectionItem.getInternalControlRequirement(),
                                reInspectionItem.getAverage());
                    }

                    reInspectionItem.setResult(general);
                    reInspectionItem.setGroupResult(group);
                    reInspectionItem.setFinalResult(ItemCalculator.generateFinalResult(general, group,
                            qualityInspectionApplication.getGroupRequirement() != null
                                    || qualityInspectionApplication.getInternalControlRequirement() != null));
                }

                reInspectionList.add(reInspectionItem);
            } else {
                // 新增检测项目至目标质检记录
                InsolubleSulfurContent reInspectionItem = new InsolubleSulfurContent();
                reInspectionItem.setMergedUnSubmittedRecordId(reInspectionMergedRecordId);
                reInspectionItem.setUnSubmittedRecordId(reInspectionRecordId);

                Map<String, String> itemCreatMap = RecordCreatUtil.itemRecordCreat(itemMatchingName, generalMap,
                        groupMap, internalControlMap);
                reInspectionItem.setMatchingName(itemCreatMap.get("itemMatchingName"));
                reInspectionItem.setName(itemCreatMap.get("name"));
                reInspectionItem.setGeneralRequirement(itemCreatMap.get("generalRequirement"));
                reInspectionItem.setGroupRequirement(itemCreatMap.get("groupRequirement"));
                reInspectionItem.setInternalControlRequirement(itemCreatMap.get("internalControlRequirement"));

                reInspectionList.add(reInspectionItem);
            }
        }

        itemService.saveBatch(reInspectionList);
    }

    @Override
    public void samplingItemRecordCreat(SamplingItemDTO samplingItemDTO, Map<String, String> generalMap,
            Map<String, String> groupMap, Map<String, String> internalControlMap, StringBuilder stringBuilder) {
        stringBuilder.append(";").append(samplingItemDTO.getItem()).append(":");
        for (int i = 0; i < samplingItemDTO.getNumber(); i++) {
            InsolubleSulfurContent insolubleSulfurContent = new InsolubleSulfurContent();
            String[] split = samplingItemDTO.getItem().split(",");
            if (split.length > 1) {
                insolubleSulfurContent.setName(split[0] + "(" + split[1] + ")");
            } else {
                insolubleSulfurContent.setName(samplingItemDTO.getItem());
            }
            String generalRequirement = generalMap.get(samplingItemDTO.getItem());
            if (generalRequirement != null) {
                insolubleSulfurContent.setGeneralRequirement(generalRequirement);
            } else {
                insolubleSulfurContent.setGeneralRequirement("暂无通用指标");
            }
            insolubleSulfurContent.setGroupRequirement("暂无客户指标");
            if (groupMap != null) {
                String groupRequirement = groupMap.get(samplingItemDTO.getItem());
                if (groupRequirement != null) {
                    if (groupRequirement.contains("占总硫")) {
                        insolubleSulfurContent.setGroupRequirement(groupRequirement.split(",")[0]);
                        insolubleSulfurContent.setOfTotal(true);
                    } else {
                        insolubleSulfurContent.setGroupRequirement(groupRequirement);
                    }
                } else {
                    insolubleSulfurContent.setGroupRequirement("暂无客户指标");
                }
            }
            insolubleSulfurContent.setInternalControlRequirement("暂无内控指标");
            if (internalControlMap != null) {
                String internalControlRequirement = internalControlMap.get(samplingItemDTO.getItem());
                if (internalControlRequirement != null) {
                    if (internalControlRequirement.contains("占总硫")) {
                        insolubleSulfurContent.setInternalControlRequirement(internalControlRequirement.split(",")[0]);
                        insolubleSulfurContent.setOfTotal(true);
                    } else {
                        insolubleSulfurContent.setInternalControlRequirement(internalControlRequirement);
                    }
                } else {
                    insolubleSulfurContent.setInternalControlRequirement("暂无内控指标");
                }
            }
            itemService.save(insolubleSulfurContent);
            stringBuilder.append(insolubleSulfurContent.getId()).append(",");
        }
        stringBuilder.deleteCharAt(stringBuilder.length() - 1);
    }

    @Override
    public void recordDataFillIn(int mergedUnSubmittedRecordId, RecordDataDTO recordData) {
        List<InsolubleSulfurContent> itemList = recordData.getInsolubleSulfurContent();
        if (CollUtil.isEmpty(itemList)) {
            LambdaQueryWrapper<InsolubleSulfurContent> lambdaQueryWrapper = new LambdaQueryWrapper<InsolubleSulfurContent>()
                    .eq(InsolubleSulfurContent::getMergedUnSubmittedRecordId, mergedUnSubmittedRecordId)
                    .eq(InsolubleSulfurContent::getSamplingInspection, false);
            itemList = itemService.list(lambdaQueryWrapper);
        }
        List<InsolubleSulfurContent> distinctList = itemList.stream()
                .distinct()
                .collect(Collectors.toList());
        distinctList.forEach(item -> {
            if (item.getInternalControlRequirement().contains("暂无")) {
                item.setGroupRequirementView(item.getGroupRequirement());
            } else {
                item.setGroupRequirementView(item.getInternalControlRequirement());
            }
        });
        distinctList.sort(Comparator.comparing(InsolubleSulfurContent::getMatchingName));
        recordData.setInsolubleSulfurContent(distinctList);
    }

    @Override
    public void reportItemFillIn(int recordId, String itemMatchingName, ReportVO reportVO) {
        LambdaQueryWrapper<InsolubleSulfurContent> lambdaQueryWrapper = new LambdaQueryWrapper<InsolubleSulfurContent>()
                .eq(InsolubleSulfurContent::getUnSubmittedRecordId, recordId)
                .eq(InsolubleSulfurContent::getMatchingName, itemMatchingName);
        List<InsolubleSulfurContent> itemList = itemService.list(lambdaQueryWrapper);
        itemList.sort(Comparator.comparing(InsolubleSulfurContent::getSamplingInspectionIndex));

        List<RecordResultVO> recordResultList = reportVO.getRecordResultList();
        List<RecordResultVO> samplingResultList = reportVO.getSamplingResultList();

        itemList.forEach(item -> {
            RecordResultVO recordResultVO = new RecordResultVO();
            recordResultVO.setId(item.getId());
            recordResultVO.setItem(item.getName());
            recordResultVO.setMatchingName(item.getMatchingName());
            recordResultVO.setGeneralRequirement(item.getGeneralRequirement());
            if ("暂无内控指标".equals(item.getInternalControlRequirement())) {
                recordResultVO.setGroupRequirement(item.getGroupRequirement());
            } else {
                recordResultVO.setGroupRequirement(item.getInternalControlRequirement());
            }
            if (item.getAverage() == null) {
                recordResultVO.setData("暂无数据");
            } else {
                recordResultVO.setData(String.format("%.2f", item.getAverage()));
            }
            recordResultVO.setResult(item.getResult());
            recordResultVO.setGroupResult(item.getGroupResult());
            recordResultVO.setFinalResult(item.getFinalResult());
            recordResultVO.setInspector(item.getInspector());
            // 计算系数相关
            recordResultVO.setShouldProcessCoefficient(item.getOfTotal());
            recordResultVO.setCalculateCoefficient(StrUtil.toString(item.getSulfurContent()));

            if (item.getSamplingInspection()) {
                samplingResultList.add(recordResultVO);
            } else {
                recordResultList.add(recordResultVO);
            }
        });

        reportVO.setRecordResultList(recordResultList);
        reportVO.setSamplingResultList(samplingResultList);
        /*
         * List<InsolubleSulfurContent> insolubleSulfurContentList =
         * recordDataVO.getInsolubleSulfurContent();
         * insolubleSulfurContentList.forEach(insolubleSulfurContent -> {
         * if (name.equals(insolubleSulfurContent.getName())) {
         * RecordResultDTO recordResultDTO = new RecordResultDTO();
         * recordResultDTO.setId(insolubleSulfurContent.getId());
         * recordResultDTO.setItem(insolubleSulfurContent.getName());
         * recordResultDTO.setGeneralRequirement(insolubleSulfurContent.
         * getGeneralRequirement());
         * if ("暂无内控指标".equals(insolubleSulfurContent.getInternalControlRequirement()))
         * {
         * recordResultDTO.setGroupRequirement(insolubleSulfurContent.
         * getGroupRequirement());
         * } else {
         * recordResultDTO.setGroupRequirement(insolubleSulfurContent.
         * getInternalControlRequirement());
         * }
         * if (insolubleSulfurContent.getAverage() == null) {
         * recordResultDTO.setData("暂无数据");
         * } else {
         * recordResultDTO.setData(String.format("%.2f",
         * insolubleSulfurContent.getAverage()));
         * }
         * recordResultDTO.setResult(insolubleSulfurContent.getResult());
         * recordResultDTO.setGroupResult(insolubleSulfurContent.getGroupResult());
         * recordResultDTO.setFinalResult(insolubleSulfurContent.getFinalResult());
         * recordResultDTO.setInspector(insolubleSulfurContent.getInspector());
         * recordResultDTOList.add(recordResultDTO);
         * }
         * 
         * });
         */
    }

    @Override
    public void semiFinishedReportItemFillIn(List<ReportItemDTO> reportItemDTOList,
            SemiFinishedRecordDataDTO semiFinishedRecordDataDTO, String name) {
        List<InsolubleSulfurContent> insolubleSulfurContentList = semiFinishedRecordDataDTO.getRecordData()
                .getInsolubleSulfurContent();
        insolubleSulfurContentList.forEach(insolubleSulfurContent -> {
            if (name.equals(insolubleSulfurContent.getName())) {
                ReportItemDTO reportItemDTO = new ReportItemDTO();
                reportItemDTO.setId(insolubleSulfurContent.getId());
                reportItemDTO.setUnSubmittedRecordId(semiFinishedRecordDataDTO.getBasicInformationNotSaved().getId());
                reportItemDTO.setQualityInspectionBatch(
                        semiFinishedRecordDataDTO.getBasicInformationNotSaved().getQualityInspectionBatch());
                reportItemDTO.setTankBatch(semiFinishedRecordDataDTO.getTankBatch());
                reportItemDTO.setItem(insolubleSulfurContent.getName());
                reportItemDTO.setGeneralRequirement(insolubleSulfurContent.getGeneralRequirement());
                if ("暂无内控指标".equals(insolubleSulfurContent.getInternalControlRequirement())) {
                    reportItemDTO.setGroupRequirement(insolubleSulfurContent.getGroupRequirement());
                } else {
                    reportItemDTO.setGroupRequirement(insolubleSulfurContent.getInternalControlRequirement());
                }
                if (insolubleSulfurContent.getAverage() == null) {
                    reportItemDTO.setData("暂无数据");
                } else {
                    reportItemDTO.setData(String.format("%.2f", insolubleSulfurContent.getAverage()));
                }
                reportItemDTO.setResult(insolubleSulfurContent.getResult());
                reportItemDTO.setGroupResult(insolubleSulfurContent.getGroupResult());
                reportItemDTO.setFinalResult(insolubleSulfurContent.getFinalResult());
                reportItemDTO.setInspector(insolubleSulfurContent.getInspector());
                reportItemDTOList.add(reportItemDTO);
            }
        });
    }

    @Override
    public void submittedAnalysisRecord(int recordId, AnalysisResult analysisResult,
            List<AnalysisResult> analysisResults, FormData formData, AnalysisRecordTemplate analysisRecordTemplate) {
        InsolubleSulfurContent insolubleSulfurContent = itemService.getById(recordId);
        formData.setInsolubleSulfurContent(insolubleSulfurContent);
        analysisResult.setItem("不溶性硫含量(%)");
        String insolubleSulfurContentTarget1 = String.format("%.2f",
                analysisRecordTemplate.getInsolubleSulfurContentTarget1());
        String insolubleSulfurContentTarget2 = String.format("%.2f",
                analysisRecordTemplate.getInsolubleSulfurContentTarget2());
        analysisResult.setTarget(Objects.equals(insolubleSulfurContentTarget1, "nu") ? "/"
                : (analysisRecordTemplate.getInsolubleSulfurContentDetermineDetermine()
                        ? (analysisRecordTemplate.getInsolubleSulfurContentDetermine1()
                                ? (insolubleSulfurContentTarget1 + "-" + insolubleSulfurContentTarget2)
                                : (insolubleSulfurContentTarget2 + "-" + insolubleSulfurContentTarget1))
                        : ((analysisRecordTemplate.getInsolubleSulfurContentDetermine1() ? "≥" : "≤")
                                + insolubleSulfurContentTarget1)));
        analysisResult.setResult(String.format("%.2f", insolubleSulfurContent.getAverage()));
        analysisResult.setDetermine(analysisRecordTemplate.getInsolubleSulfurContentDetermine1() == null ? null
                : insolubleSulfurContent.getResult());
        analysisResult.setInspector(insolubleSulfurContent.getInspector());
        analysisResults.add(analysisResult);
    }

    @Override
    public void getRecordResult(int recordId, AtomicInteger generalResult, AtomicInteger groupResult,
            AtomicInteger finalResult) {
        LambdaQueryWrapper<InsolubleSulfurContent> lambdaQueryWrapper = new LambdaQueryWrapper<InsolubleSulfurContent>()
                .eq(InsolubleSulfurContent::getUnSubmittedRecordId, recordId);
        List<InsolubleSulfurContent> insolubleSulfurContentList = itemService.list(lambdaQueryWrapper);

        for (InsolubleSulfurContent insolubleSulfurContent : insolubleSulfurContentList) {
            itemService.updateById(insolubleSulfurContent);
            if (insolubleSulfurContent.getResult() != null) {
                if (insolubleSulfurContent.getResult() == 0) {
                    generalResult.set(0);
                }
            }
            if (insolubleSulfurContent.getGroupResult() != null) {
                if (insolubleSulfurContent.getGroupResult() == 0) {
                    groupResult.set(0);
                }
            }
            if (insolubleSulfurContent.getFinalResult() != null) {
                if (finalResult.get() == 1) {
                    finalResult.set(insolubleSulfurContent.getFinalResult());
                }
                if (finalResult.get() == 2 && insolubleSulfurContent.getFinalResult() == 0) {
                    finalResult.set(0);
                }
            }
        }
    }

    @Override
    public void getSubmittedResult(FormData formData, StringBuilder stringBuilder, Boolean result) {
        if (result) {
            InsolubleSulfurContent insolubleSulfurContent = formData.getInsolubleSulfurContent();
            itemService.save(insolubleSulfurContent);
            stringBuilder.append(",insoluble_sulfur_content:").append(insolubleSulfurContent.getId());
        }
    }

    @Override
    public void analysisRecordFillIn(StringBuilder stringBuilder, String[] records, BasicInformation basicInformation) {
        for (String record : records) {
            if ("insoluble_sulfur_content".equals(record.split(":")[0])) {
                Float average = itemService.getById(record.split(":")[1]).getAverage();
                String format = String.format("%.2f", average);
                stringBuilder.append(format);
            }
        }
    }

    @Override
    public void targetFillIn(BasicInformation basicInformation, StringBuilder stringBuilder,
            AnalysisRecordTemplate analysisRecordTemplate, int count) {
        String insolubleSulfurContentTarget1 = String.format("%.2f",
                analysisRecordTemplate.getInsolubleSulfurContentTarget1());
        String insolubleSulfurContentTarget2 = String.format("%.2f",
                analysisRecordTemplate.getInsolubleSulfurContentTarget2());
        stringBuilder.append(Objects.equals(insolubleSulfurContentTarget1, "nu") ? "/"
                : (analysisRecordTemplate.getInsolubleSulfurContentDetermineDetermine()
                        ? (analysisRecordTemplate.getInsolubleSulfurContentDetermine1()
                                ? (insolubleSulfurContentTarget1 + "-" + insolubleSulfurContentTarget2)
                                : (insolubleSulfurContentTarget2 + "-" + insolubleSulfurContentTarget1))
                        : ((analysisRecordTemplate.getInsolubleSulfurContentDetermine1() ? "≥" : "≤")
                                + insolubleSulfurContentTarget1)));
        stringBuilder.append(Objects.equals(insolubleSulfurContentTarget1, "nu") ? "" : "%");
    }

    @Override
    public void nameConversion(int id, Map<String, String> map) {
        InsolubleSulfurContent byId = itemService.getById(id);
        Float average = byId.getAverage();
        String format = String.format("%.2f", average);
        map.put("不溶性硫磺含量", format);
    }

    @Override
    public String createChart(List<BasicInformation> basicInformationList,
            List<GeneralQualityStandard> generalQualityStandardList, DefaultCategoryDataset dataSet) {
        final int[] count = { 1 };
        final double[] acidityCount = { 0 };
        basicInformationList.forEach(basicInformation -> {
            String[] split = basicInformation.getRecords().split(",");
            Map<String, String> map = new HashMap<>();
            Arrays.asList(split).forEach(record -> {
                String[] recordArray = record.split(":");
                map.put(recordArray[0], recordArray[1]);
            });
            if (map.get("insoluble_sulfur_content") != null) {
                String acidityId = map.get("insoluble_sulfur_content");
                InsolubleSulfurContent byId = itemService.getById(acidityId);
                double value = new BigDecimal(String.valueOf(byId.getAverage())).doubleValue();
                dataSet.setValue(value, "不溶性硫磺含量", String.valueOf(count[0]));
                count[0]++;
                acidityCount[0] += value;
            }
        });
        count[0] = 1;
        basicInformationList.forEach(basicInformation -> {
            dataSet.setValue(acidityCount[0] / basicInformationList.size(), "CL", String.valueOf(count[0]));
            count[0]++;
        });
        count[0] = 1;
        basicInformationList.forEach(basicInformation -> {
            Map<String, String> standardMap = new HashMap<>();
            generalQualityStandardList.forEach(standard -> standardMap.put(
                    standard.getQualityInspectionItem().replaceAll("[^a-zA-Z\\d\\u4E00-\\u9FA5]", ""),
                    standard.getQualityRequirement()));
            String requirement = standardMap.get("不溶性硫磺含量");
            TargetJudgmentHandler targetJudgmentHandler = TargetJudgmentHandlerFactory.getInvokeStrategy(requirement);
            if (targetJudgmentHandler != null) {
                if (requirement.contains("≥") && !requirement.contains("≤")) {
                    Float requirementNumber = targetJudgmentHandler.singleTargetJudgment(requirement);
                    double value = new BigDecimal(String.valueOf(requirementNumber)).doubleValue();
                    dataSet.setValue(value, "LCL", String.valueOf(count[0]));
                } else if (requirement.contains("≤") && !requirement.contains("≥")) {
                    Float requirementNumber = targetJudgmentHandler.singleTargetJudgment(requirement);
                    double value = new BigDecimal(String.valueOf(requirementNumber)).doubleValue();
                    dataSet.setValue(value, "UCL", String.valueOf(count[0]));
                } else {
                    Float[] requirementNumber = targetJudgmentHandler.doubleTargetJudgment(requirement);
                    double value1 = new BigDecimal(String.valueOf(requirementNumber[0])).doubleValue();
                    double value2 = new BigDecimal(String.valueOf(requirementNumber[1])).doubleValue();
                    dataSet.setValue(value1, "LCL", String.valueOf(count[0]));
                    dataSet.setValue(value2, "UCL", String.valueOf(count[0]));
                }
                count[0]++;
            }
        });
        return "不溶性硫磺含量";
    }

    @Override
    public void generalIndicatorVOFillIn(QualityIndicatorsVO qualityIndicatorsVO,
            GeneralQualityStandard generalQualityStandard) {
        qualityIndicatorsVO.setGeneralInsolubleSulfurContentIndicator(generalQualityStandard.getQualityRequirement());
    }

    @Override
    public void groupIndicatorVOFillIn(QualityIndicatorsVO qualityIndicatorsVO,
            GroupQualityStandard groupQualityStandard) {
        qualityIndicatorsVO.setGroupInsolubleSulfurContentIndicator(groupQualityStandard.getQualityRequirement());
    }

    @Override
    public void samplingItemFillIn(int mergedUnSubmittedRecordId, String itemName, String requirement,
            String itemMatchingName, List<SamplingItemDTO> samplingItemList) {
        LambdaQueryWrapper<InsolubleSulfurContent> lambdaQueryWrapper = new LambdaQueryWrapper<InsolubleSulfurContent>()
                .eq(InsolubleSulfurContent::getMergedUnSubmittedRecordId, mergedUnSubmittedRecordId)
                // .eq(InsolubleSulfurContent::getName, itemName)
                .eq(InsolubleSulfurContent::getMatchingName, itemMatchingName)
                .eq(InsolubleSulfurContent::getSamplingInspection, true);
        List<InsolubleSulfurContent> list = CollUtil.distinct(itemService.list(lambdaQueryWrapper),
                InsolubleSulfurContent::getSamplingInspectionIndex, false);
        if (CollUtil.isNotEmpty(list)) {
            /*
             * Map<Integer, List<InsolubleSulfurContent>> idMap = new HashMap<>();
             * list.forEach(item -> {
             * List<InsolubleSulfurContent> itemList =
             * idMap.get(item.getUnSubmittedRecordId());
             * itemList.add(item);
             * idMap.put(item.getUnSubmittedRecordId(), itemList);
             * });
             */

            SamplingItemDTO samplingItemDTO = new SamplingItemDTO();
            Map<String, String> itemMap = new HashMap<>();
            itemMap.put("itemName", itemName);
            itemMap.put("requirement", requirement);
            itemMap.put("matchingName", itemMatchingName);
            itemMap.remove("itemName");
            samplingItemDTO.setItem(itemMap.toString());
            samplingItemDTO.setNumber(list.size());
            samplingItemList.add(samplingItemDTO);
        }
    }

    @Override
    public void samplingDataFillIn(int mergedUnSubmittedRecordId, SamplingDataDTO samplingData) {
        List<InsolubleSulfurContent> resultList = new ArrayList<>();
        List<InsolubleSulfurContent> itemList = samplingData.getInsolubleSulfurContent();
        if (CollUtil.isEmpty(itemList)) {
            LambdaQueryWrapper<InsolubleSulfurContent> lambdaQueryWrapper = new LambdaQueryWrapper<InsolubleSulfurContent>()
                    .eq(InsolubleSulfurContent::getMergedUnSubmittedRecordId, mergedUnSubmittedRecordId)
                    .eq(InsolubleSulfurContent::getSamplingInspection, true);
            itemList = itemService.list(lambdaQueryWrapper);
        }
        if (CollUtil.isNotEmpty(itemList)) {
            List<InsolubleSulfurContent> nameDistinctList = itemList.stream()
                    .distinct()
                    .collect(Collectors.toList());
            nameDistinctList.forEach(item -> {
                LambdaQueryWrapper<InsolubleSulfurContent> lambdaQueryWrapper = new LambdaQueryWrapper<InsolubleSulfurContent>()
                        .eq(InsolubleSulfurContent::getMergedUnSubmittedRecordId, mergedUnSubmittedRecordId)
                        .eq(InsolubleSulfurContent::getSamplingInspection, true)
                        .eq(InsolubleSulfurContent::getMatchingName, item.getMatchingName());
                List<InsolubleSulfurContent> indexList = itemService.list(lambdaQueryWrapper);
                resultList.addAll(
                        CollUtil.distinct(indexList, InsolubleSulfurContent::getSamplingInspectionIndex, false));
            });
            resultList.forEach(item -> {
                if (item.getInternalControlRequirement().contains("暂无")) {
                    item.setGroupRequirementView(item.getGroupRequirement());
                } else {
                    item.setGroupRequirementView(item.getInternalControlRequirement());
                }
            });
            resultList.sort(Comparator.comparing(InsolubleSulfurContent::getMatchingName));
            samplingData.setInsolubleSulfurContent(resultList);
        }
    }

    @Override
    public void getSamplingResult(SamplingDataDTO samplingDataDTO, AtomicInteger generalResult,
            AtomicInteger groupResult) {
        List<InsolubleSulfurContent> insolubleSulfurContentList = samplingDataDTO.getInsolubleSulfurContent();
        insolubleSulfurContentList.forEach(insolubleSulfurContent -> {
            itemService.updateById(insolubleSulfurContent);
            if (insolubleSulfurContent.getResult() != null) {
                if (insolubleSulfurContent.getResult() == 0) {
                    generalResult.set(0);
                }
            }
            if (insolubleSulfurContent.getGroupResult() != null) {
                if (insolubleSulfurContent.getGroupResult() == 0) {
                    groupResult.set(0);
                }
            }
        });
    }

    @Override
    public void reportSamplingItemFillIn(List<SamplingResultDTO> samplingResultDTOList, SamplingDataVO samplingDataVO,
            String name) {
        List<InsolubleSulfurContent> insolubleSulfurContentList = samplingDataVO.getInsolubleSulfurContent();
        insolubleSulfurContentList.forEach(insolubleSulfurContent -> {
            if (name.equals(insolubleSulfurContent.getName())) {
                SamplingResultDTO samplingResultDTO = new SamplingResultDTO();
                samplingResultDTO.setId(insolubleSulfurContent.getId());
                samplingResultDTO.setItem(insolubleSulfurContent.getName());
                samplingResultDTO.setGeneralRequirement(insolubleSulfurContent.getGeneralRequirement());
                if ("暂无内控指标".equals(insolubleSulfurContent.getInternalControlRequirement())) {
                    samplingResultDTO.setGroupRequirement(insolubleSulfurContent.getGroupRequirement());
                } else {
                    samplingResultDTO.setGroupRequirement(insolubleSulfurContent.getInternalControlRequirement());
                }
                if (insolubleSulfurContent.getAverage() == null) {
                    samplingResultDTO.setData("暂无数据");
                } else {
                    samplingResultDTO.setData(String.format("%.2f", insolubleSulfurContent.getAverage()));
                }
                samplingResultDTO.setResult(insolubleSulfurContent.getResult());
                samplingResultDTO.setGroupResult(insolubleSulfurContent.getGroupResult());
                samplingResultDTO.setFinalResult(insolubleSulfurContent.getFinalResult());
                samplingResultDTO.setInspector(insolubleSulfurContent.getInspector());
                samplingResultDTOList.add(samplingResultDTO);
            }
        });
    }

    @Override
    public void standingBookDataFillIn(StandingBookDataDTO standingBookDataDTO, Map<String, Object> dataMap,
            StringBuilder samplingInspectionBuilder, Set<String> itemSet, String itemMatchingName) {
        log.info("台账数据填充-不溶性硫磺含量");
        List<InsolubleSulfurContent> itemList = standingBookDataDTO.getInsolubleSulfurContent();
        itemList.forEach(item -> {
            String matchingName = item.getMatchingName();
            String name;
            if (matchingName.split(",").length > 1) {
                name = matchingName.split(",")[0] + "(" + matchingName.split(",")[1] + ")";
            } else {
                name = matchingName;
            }
            itemSet.add(name);
            if (!item.getSamplingInspection()) {
                if (ObjectUtil.isNotNull(item.getAverage())) {
                    dataMap.put(name, String.format("%.2f", item.getAverage()));
                } else {
                    dataMap.put(name, "暂无数据");
                }
            } else if (StrUtil.equals(itemMatchingName, matchingName)) {
                samplingInspectionBuilder
                        .append(";")
                        .append(name)
                        .append(":")
                        .append(String.format("%.2f", item.getAverage()));
            }
        });
    }

    @Override
    public void standingBookColumnFillIn(String item, List<Map<String, Object>> columnMapList) {
        Map<String, Object> columnMap = new HashMap<>();
        if (item.split(",").length > 1) {
            columnMap.put("label", item.split(",")[0] + "(" + item.split(",")[1] + ")");
        } else {
            columnMap.put("label", item);
        }
        columnMapList.add(columnMap);
    }

    @Override
    public void itemDataFillIn(int id, List<Map<String, Object>> list) {
        InsolubleSulfurContent byId = itemService.getById(id);
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("label", "坩埚质量(g)");
        dataMap.put("value", byId.getCrucibleQuality1());
        list.add(dataMap);
        dataMap = new HashMap<>();
        dataMap.put("label", "坩埚质量(g)");
        dataMap.put("value", byId.getCrucibleQuality2());
        list.add(dataMap);
        dataMap = new HashMap<>();
        dataMap.put("label", "坩埚及样品质量(g)");
        dataMap.put("value", byId.getSpecimenAndCrucibleQuality1());
        list.add(dataMap);
        dataMap = new HashMap<>();
        dataMap.put("label", "坩埚及样品质量(g)");
        dataMap.put("value", byId.getSpecimenAndCrucibleQuality2());
        list.add(dataMap);
        dataMap = new HashMap<>();
        dataMap.put("label", "样品量(g)");
        dataMap.put("value", byId.getSpecimenQuality1());
        list.add(dataMap);
        dataMap = new HashMap<>();
        dataMap.put("label", "样品量(g)");
        dataMap.put("value", byId.getSpecimenQuality2());
        list.add(dataMap);
        dataMap = new HashMap<>();
        dataMap.put("label", "坩埚及剩余质量(g)");
        dataMap.put("value", byId.getQualityAfterBurning1());
        list.add(dataMap);
        dataMap = new HashMap<>();
        dataMap.put("label", "坩埚及剩余质量(g)");
        dataMap.put("value", byId.getQualityAfterBurning2());
        list.add(dataMap);
        dataMap = new HashMap<>();
        dataMap.put("label", "剩余质量(g)");
        dataMap.put("value", byId.getResidueQuality1());
        list.add(dataMap);
        dataMap = new HashMap<>();
        dataMap.put("label", "剩余质量(g)");
        dataMap.put("value", byId.getResidueQuality2());
        list.add(dataMap);
        dataMap = new HashMap<>();
        dataMap.put("label", "硫含量(%)");
        dataMap.put("value", byId.getSulfurContent1());
        list.add(dataMap);
        dataMap = new HashMap<>();
        dataMap.put("label", "硫含量(%)");
        dataMap.put("value", byId.getSulfurContent2());
        list.add(dataMap);
        dataMap = new HashMap<>();
        dataMap.put("label", "平均(%)");
        dataMap.put("value", byId.getAverage());
        list.add(dataMap);
        dataMap = new HashMap<>();
        dataMap.put("label", "检测人");
        dataMap.put("value", byId.getInspector());
        list.add(dataMap);
    }

    @Override
    public void recordCopy(String recordId, String itemName, StringBuilder reInspectionRecord) {
        reInspectionRecord.append(";").append(itemName).append(":");
        InsolubleSulfurContent byId = itemService.getById(recordId);
        InsolubleSulfurContent insolubleSulfurContent = BeanUtil.copyProperties(byId, InsolubleSulfurContent.class);
        insolubleSulfurContent.setId(null);
        itemService.save(insolubleSulfurContent);
        reInspectionRecord.append(insolubleSulfurContent.getId());
    }

    @Override
    public void samplingCopy(String[] samplingIds, String itemName, StringBuilder reInspectionRecord) {
        reInspectionRecord.append(";").append(itemName).append(":");
        for (String samplingId : samplingIds) {
            InsolubleSulfurContent byId = itemService.getById(samplingId);
            InsolubleSulfurContent insolubleSulfurContent = BeanUtil.copyProperties(byId, InsolubleSulfurContent.class);
            insolubleSulfurContent.setId(null);
            itemService.save(insolubleSulfurContent);
            reInspectionRecord.append(insolubleSulfurContent.getId()).append(",");
        }
        reInspectionRecord.deleteCharAt(reInspectionRecord.length() - 1);
    }

    @Override
    public void getOutboundReportItem(OutboundReportItemDTO outboundReportItemDTO, String itemRequirement,
            boolean isGroup, boolean isPreDispersedCustomer, int itemRecordId) {
        InsolubleSulfurContent byId = itemService.getById(itemRecordId);
        outboundReportItemDTO.setItemName(byId.getName());
        outboundReportItemDTO.setRequirement(itemRequirement);
        outboundReportItemDTO.setData(String.format("%.2f", byId.getAverage()));
    }

    @Override
    public void selectCollection(MPJLambdaWrapper<BasicInformation> wrapper) {
        log.info("selectCollection-不溶性硫磺含量");
        wrapper.selectCollection(InsolubleSulfurContent.class, StandingBookDataDTO::getInsolubleSulfurContent);
    }

    @Override
    public void leftJoin(MPJLambdaWrapper<BasicInformation> wrapper) {
        log.info("leftJoin-不溶性硫磺含量");
        wrapper.leftJoin(InsolubleSulfurContent.class, InsolubleSulfurContent::getUnSubmittedRecordId,
                BasicInformation::getNotSubmittedRecordId);
    }

    @Override
    public void outboundItemDataFillIn(List<Integer> notSubmittedRecordIdList,
            List<TransferRecordProductionAndQualityDataDTO> productionAndQualityDataList) {
        // 查询所有相关的不溶性硫磺含量数据
        List<InsolubleSulfurContent> list = itemService.list(new LambdaQueryWrapper<InsolubleSulfurContent>()
                .in(InsolubleSulfurContent::getUnSubmittedRecordId, notSubmittedRecordIdList));

        // 遍历每条生产和质量数据记录
        productionAndQualityDataList.forEach(outboundInformationVO -> {
            outboundInformationVO.getQualityInspectionData().forEach(inspectionDataMap -> {
                Integer notSubmittedRecordId = (Integer) inspectionDataMap.get("notSubmittedRecordId");

                // 过滤出当前记录相关的不溶性硫磺含量数据
                List<InsolubleSulfurContent> filteredItems = list.stream()
                        .filter(a -> a.getUnSubmittedRecordId().equals(notSubmittedRecordId))
                        .collect(Collectors.toList());

                // 处理非抽检数据
                filteredItems.stream()
                        .filter(item -> !item.getSamplingInspection())
                        .forEach(item -> inspectionDataMap.put(item.getMatchingName(),
                                ObjUtil.isNull(item.getAverage()) ? "暂无数据"
                                        : formatValue(item.getAverage())));

                // 处理抽样数据
                @SuppressWarnings("unchecked")
                final List<Map<String, String>> samplingData = (List<Map<String, String>>) inspectionDataMap
                        .computeIfAbsent("samplingData", k -> new ArrayList<>());

                // 添加抽检数据
                filteredItems.stream()
                        .filter(InsolubleSulfurContent::getSamplingInspection)
                        .forEach(item -> {
                            Map<String, String> samplingEntry = new HashMap<>();
                            samplingEntry.put("matchingName", item.getMatchingName());
                            samplingEntry.put("value", ObjUtil.isNull(item.getAverage()) ? "暂无数据"
                                    : formatValue(item.getAverage()));
                            samplingData.add(samplingEntry);
                        });
            });
        });
    }

    // 格式化值的辅助方法
    private String formatValue(Float value) {
        return NumberUtil.round(NumberUtil.toBigDecimal(value), 1, RoundingMode.HALF_EVEN).toPlainString();
    }

    @Override
    public void recordDownloadInspectionDataFillIn(Map<Integer, Object> inspectionDataMap,
            List<Integer> unSubmiitedRecordIdList, String itemMatchingName) {
        List<InsolubleSulfurContent> list = itemService.list(new LambdaQueryWrapper<InsolubleSulfurContent>()
                .in(InsolubleSulfurContent::getUnSubmittedRecordId, unSubmiitedRecordIdList)
                .eq(InsolubleSulfurContent::getMatchingName, itemMatchingName)
                .orderByAsc(InsolubleSulfurContent::getSamplingInspection,
                        InsolubleSulfurContent::getSamplingInspectionIndex));

        list.forEach(item -> {
            // 从检验数据映射中获取当前记录的检验数据
            @SuppressWarnings("unchecked")
            Map<String, Object> recordInspectionData = (Map<String, Object>) inspectionDataMap
                    .get(item.getUnSubmittedRecordId());

            // 如果记录检验数据为空,则初始化一个新的HashMap
            if (ObjUtil.isNull(recordInspectionData)) {
                recordInspectionData = new HashMap<>();
                inspectionDataMap.put(item.getUnSubmittedRecordId(), recordInspectionData);
            }

            // 获取检验数据列表(用于存储处理后的检验数据)
            @SuppressWarnings("unchecked")
            List<List<String>> inspectionDataList = (List<List<String>>) recordInspectionData.get("inspectionDataList");

            // 如果检验数据列表为空,则初始化一个新的ArrayList
            if (ObjUtil.isNull(inspectionDataList)) {
                inspectionDataList = new ArrayList<>();
                recordInspectionData.put("inspectionDataList", inspectionDataList);
            }

            // 获取抽样数据列表(用于存储处理后的抽样检验数据)
            @SuppressWarnings("unchecked")
            List<List<String>> samplingDataList = (List<List<String>>) recordInspectionData
                    .get("samplingDataList");

            // 如果抽样数据列表为空,则初始化一个新的ArrayList
            if (ObjUtil.isNull(samplingDataList)) {
                samplingDataList = new ArrayList<>();
                recordInspectionData.put("samplingDataList", samplingDataList);
            }

            // 获取原始检验数据列表(用于存储未经处理的检验原始数据)
            @SuppressWarnings("unchecked")
            List<List<List<String>>> rawInspectionDataList = (List<List<List<String>>>) recordInspectionData
                    .get("rawInspectionDataList");

            // 如果原始检验数据列表为空,则初始化一个新的ArrayList
            if (ObjUtil.isNull(rawInspectionDataList)) {
                rawInspectionDataList = new ArrayList<>();
                recordInspectionData.put("rawInspectionDataList", rawInspectionDataList);
            }

            // 获取原始抽样数据列表(用于存储未经处理的抽样原始数据)
            @SuppressWarnings("unchecked")
            List<List<List<String>>> rawSamplingDataList = (List<List<List<String>>>) recordInspectionData
                    .get("rawSamplingDataList");

            // 如果原始抽样数据列表为空,则初始化一个新的ArrayList
            if (ObjUtil.isNull(rawSamplingDataList)) {
                rawSamplingDataList = new ArrayList<>();
                recordInspectionData.put("rawSamplingDataList", rawSamplingDataList);
            }

            Map<String, Object> itemDataMap = new HashMap<>();

            List<String> inspectionData = new ArrayList<>();
            inspectionData.add(item.getMatchingName());
            inspectionData.add(item.getGeneralRequirement());
            if (StrUtil.contains(item.getInternalControlRequirement(), "暂无")) {
                inspectionData.add(item.getGroupRequirement());
            } else {
                inspectionData.add(item.getInternalControlRequirement());
            }
            inspectionData.add(ObjUtil.isNull(item.getAverage()) ? "暂无数据"
                    : NumberUtil.round(NumberUtil.toBigDecimal(item.getAverage()), 2, RoundingMode.HALF_EVEN)
                            .toPlainString());
            if (ObjUtil.isNotNull(item.getFinalResult())) {
                switch (item.getFinalResult()) {
                    case 0:
                        inspectionData.add("不合格");
                        break;
                    case 2:
                        inspectionData.add("不符合");
                        break;
                    default:
                        inspectionData.add("合格");
                        break;
                }
            } else {
                inspectionData.add("/");
            }
            inspectionData.add(StrUtil.isBlank(item.getInspector()) ? "/" : item.getInspector());
            itemDataMap.put("inspectionData", inspectionData);

            List<List<String>> rawInspectionData = new ArrayList<>();
            List<String> row1 = new ArrayList<>();
            row1.add(item.getMatchingName());
            row1.add(null);
            row1.add("1#");
            row1.add(null);
            row1.add("2#");
            row1.add(item.getInspector());
            rawInspectionData.add(row1);

            List<String> row2 = new ArrayList<>();

            row2.add(null);
            row2.add("坩埚质量(g)");
            row2.add(ObjUtil.isNull(item.getCrucibleQuality1()) ? "/"
                    : NumberUtil.round(NumberUtil.toBigDecimal(item.getCrucibleQuality1()), 4, RoundingMode.HALF_EVEN)
                            .toPlainString());
            row2.add(null);
            row2.add(ObjUtil.isNull(item.getCrucibleQuality2()) ? "/"
                    : NumberUtil.round(NumberUtil.toBigDecimal(item.getCrucibleQuality2()), 4, RoundingMode.HALF_EVEN)
                            .toPlainString());
            row2.add(null);
            rawInspectionData.add(row2);

            List<String> row3 = new ArrayList<>();

            row3.add(null);
            row3.add("坩埚及样品质量(g)");
            row3.add(ObjUtil.isNull(item.getSpecimenAndCrucibleQuality1()) ? "/"
                    : NumberUtil
                            .round(NumberUtil.toBigDecimal(item.getSpecimenAndCrucibleQuality1()), 4,
                                    RoundingMode.HALF_EVEN)
                            .toPlainString());
            row3.add(null);
            row3.add(ObjUtil.isNull(item.getSpecimenAndCrucibleQuality2()) ? "/"
                    : NumberUtil
                            .round(NumberUtil.toBigDecimal(item.getSpecimenAndCrucibleQuality2()), 4,
                                    RoundingMode.HALF_EVEN)
                            .toPlainString());
            row3.add(null);
            rawInspectionData.add(row3);

            List<String> row4 = new ArrayList<>();

            row4.add(null);
            row4.add("样品量(g)");
            row4.add(ObjUtil.isNull(item.getSpecimenQuality1()) ? "/"
                    : NumberUtil.round(NumberUtil.toBigDecimal(item.getSpecimenQuality1()), 4, RoundingMode.HALF_EVEN)
                            .toPlainString());
            row4.add(null);
            row4.add(ObjUtil.isNull(item.getSpecimenQuality2()) ? "/"
                    : NumberUtil.round(NumberUtil.toBigDecimal(item.getSpecimenQuality2()), 4, RoundingMode.HALF_EVEN)
                            .toPlainString());
            row4.add(null);
            rawInspectionData.add(row4);

            List<String> row5 = new ArrayList<>();

            row5.add(null);
            row5.add("坩埚及剩余质量(g)");
            row5.add(ObjUtil.isNull(item.getQualityAfterBurning1()) ? "/"
                    : NumberUtil
                            .round(NumberUtil.toBigDecimal(item.getQualityAfterBurning1()), 4, RoundingMode.HALF_EVEN)
                            .toPlainString());
            row5.add(null);
            row5.add(ObjUtil.isNull(item.getQualityAfterBurning2()) ? "/"
                    : NumberUtil
                            .round(NumberUtil.toBigDecimal(item.getQualityAfterBurning2()), 4, RoundingMode.HALF_EVEN)
                            .toPlainString());
            row5.add(null);
            rawInspectionData.add(row5);

            List<String> row6 = new ArrayList<>();

            row6.add(null);
            row6.add("剩余质量(g)");
            row6.add(ObjUtil.isNull(item.getResidueQuality1()) ? "/"
                    : NumberUtil
                            .round(NumberUtil.toBigDecimal(item.getResidueQuality1()), 4, RoundingMode.HALF_EVEN)
                            .toPlainString());
            row6.add(null);
            row6.add(ObjUtil.isNull(item.getResidueQuality2()) ? "/"
                    : NumberUtil
                            .round(NumberUtil.toBigDecimal(item.getResidueQuality2()), 4, RoundingMode.HALF_EVEN)
                            .toPlainString());
            row6.add(null);
            rawInspectionData.add(row6);

            List<String> row7 = new ArrayList<>();

            row7.add(null);
            row7.add(item.getName() + "(%)");
            row7.add(ObjUtil.isNull(item.getSulfurContent1()) ? "/"
                    : NumberUtil.round(NumberUtil.toBigDecimal(item.getSulfurContent1()), 4, RoundingMode.HALF_EVEN)
                            .toPlainString());
            row7.add(null);
            row7.add(ObjUtil.isNull(item.getSulfurContent2()) ? "/"
                    : NumberUtil.round(NumberUtil.toBigDecimal(item.getSulfurContent2()), 4, RoundingMode.HALF_EVEN)
                            .toPlainString());
            row7.add(null);
            rawInspectionData.add(row7);

            List<String> row8 = new ArrayList<>();
            row8.add(null);
            row8.add("平均(%)");
            row8.add(ObjUtil.isNull(item.getAverage()) ? "/"
                    : NumberUtil.round(NumberUtil.toBigDecimal(item.getAverage()), 2, RoundingMode.HALF_EVEN)
                            .toPlainString());
            row8.add(null);
            row8.add(null);
            row8.add(null);
            rawInspectionData.add(row8);

            if (item.getSamplingInspection()) {
                samplingDataList.add(inspectionData);
                rawSamplingDataList.add(rawInspectionData);
            } else {
                inspectionDataList.add(inspectionData);
                rawInspectionDataList.add(rawInspectionData);
            }
        });
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        QualityInspectionHandlerFactory.register("可溶性硫磺含量", this);
        QualityInspectionHandlerFactory.register("不溶性硫磺含量", this);
        QualityInspectionHandlerFactory.register("不溶性硫磺含量(占总硫)", this);
        QualityInspectionHandlerFactory.register("CS2不溶份", this);
        QualityInspectionHandlerFactory.register("CS2不容份", this);
        QualityInspectionHandlerFactory.register("不溶性硫磺含量指标", this);
        QualityInspectionHandlerFactory.register("insoluble_sulfur_content", this);
        QualityInspectionHandlerFactory.register("insolubleSulfurContent", this);
    }
}
