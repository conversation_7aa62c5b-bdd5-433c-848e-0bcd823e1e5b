package com.haihang.model.DO.outbound;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description: 仓库使用非自身订单审批表
 * @Author: zad
 * @Create: 2024/12/20
 */
@Data
@TableName("cmf_ck_cd_pch_sp")
public class OutboundCustomerApproval {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    
    /**
     * 公司ID
     */
    @TableField("link_id")
    private Integer linkId;
    
    /**
     * 客户编号
     */
    @TableField("kh_bh")
    private String customerCode;
    
    /**
     * 客户名称
     */
    @TableField("kh_mc")
    private String customerName;
    
    /**
     * 产品编号
     */
    @TableField("cp_bh")
    private String productCode;
    
    /**
     * 产品类别编号
     */
    @TableField("cp_lb_bh")
    private String productCategoryCode;
    
    /**
     * 产品名称
     */
    @TableField("cp_mc")
    private String productName;
    
    /**
     * 生产批号
     */
    @TableField("pch")
    private String productionBatch;
    
    /**
     * 质检批号
     */
    @TableField("zj_pch")
    private String qualityInspectionBatch;
    
    /**
     * 数量
     */
    @TableField("sl")
    private BigDecimal quantity;
    
    /**
     * 包装数量
     */
    @TableField("bz_sl")
    private BigDecimal packagingQuantity;
    
    /**
     * 仓库传递人UID
     */
    @TableField("add_uid")
    private Integer addUserId;
    
    /**
     * 传递时间
     */
    @TableField("add_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime addTime;
    
    /**
     * 我方合同号
     */
    @TableField("wf_ht")
    private String ourContractNumber;
    
    /**
     * 生产入库单ID
     */
    @TableField("rkdid")
    private String inboundId;
    
    /**
     * 实际出库生产批次号
     */
    @TableField("sj_sc_pch")
    private String actualOutboundProductionBatch;
    
    /**
     * 质检单ID
     */
    @TableField("zid")
    private Integer qualityInspectionId;
    
    /**
     * 状态：0-待审，1-通过，2-否决
     */
    @TableField("status")
    private Integer status;
    
    /**
     * 质量要求
     */
    @TableField("zl_yq")
    private String qualityRequirement;
    
    /**
     * 集团质量指标类别
     */
    @TableField("jt_zl_zb_lb")
    private String groupQualityIndicatorCategory;
    
    /**
     * 审批人
     */
    @TableField("spr")
    private String approver;
    
    /**
     * 审批时间
     */
    @TableField("sp_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime approvalTime;
} 