package com.haihang.service.outbound;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.haihang.model.DO.common.Staff;
import com.haihang.model.DO.outbound.TransportReportItemTestMethod;
import com.haihang.model.DO.outbound.TransportReportOnlineAvailability;
import com.haihang.model.DO.user.User;
import com.haihang.model.DTO.outbound.transferRecord.TransferRecordDTO;
import com.haihang.model.DTO.outbound.transferRecord.TransferRecordDetailsDTO;
import com.haihang.model.DTO.outbound.transportReport.*;
import com.haihang.model.Query.outbound.TransferRecordQuery;

import java.util.List;
import java.util.Map;

/**
 * @Description: 出库传递单service @Author: zad @Create: 2024/9/21 08:19
 */
public interface TransferRecordService {
    /**
     * 分页查询出库传递单
     *
     * @param current             当前页面
     * @param size                页面大小
     * @param transferRecordQuery 出库传递单查询
     * @return 出库传递单页面
     */
    IPage<TransferRecordDTO> getTransferRecordPage(
            int current, int size, TransferRecordQuery transferRecordQuery);

    /**
     * 获取出库传递单详情
     *
     * @param transferRecordDTO 出库传递单DTO
     * @return 出库传递单详情
     */
    TransferRecordDetailsDTO getTransferRecordDetails(TransferRecordDTO transferRecordDTO);

    /**
     * 创建随车质检单
     *
     * @param transportReportCreatDTO 随车质检单创建DTO
     * @return 随车质检单DTO
     */
    TransportReportDTO creatTransportReport(TransportReportCreatDTO transportReportCreatDTO);

    /**
     * 保存随车质检单
     *
     * @param transportReportDTO 随车质检单DTO
     * @return 保存结果
     */
    Boolean saveTransportReport(TransportReportDTO transportReportDTO);

    /**
     * 创建随车质检单图片
     *
     * @param linkId         公司id
     * @param outboundNumber 出库编号
     * @param productName    产品名称
     * @return 随车质检单图片url
     */
    List<String> creatTransportReportPicture(Integer linkId, String outboundNumber, String productName);

    /**
     * 获取随车质检单预览
     *
     * @param transferRecordDTO 出库传递单DTO
     * @return 随车质检单图片url
     */
    List<String> getTransportReportPreview(TransferRecordDTO transferRecordDTO);

    /**
     * 重置随车质检单
     *
     * @param transferRecordDTO 出库传递单DTO
     * @return 重置结果
     */
    Boolean resetTransportReport(TransferRecordDTO transferRecordDTO);

    /**
     * 随车质检单审核
     *
     * @param transportReportReviewDTO 随车质检单审核DTO
     * @return 审核结果
     */
    Boolean reportReview(TransportReportReviewDTO transportReportReviewDTO);

    /**
     * 下载随车质检单
     *
     * @param linkId         公司id
     * @param outboundNumber 出库单编号
     * @param productName    产品名称
     * @return 下载信息map
     */
    Map<String, String> downloadTransportReport(Integer linkId, String outboundNumber, String productName,
            String language);

    /**
     * 保存下载记录
     *
     * @param transportReportDownloadRecord 下载记录
     * @return 保存结果
     */
    Boolean saveTransportReportDownloadRecord(Integer linkId, String outboundNumber, String productName,
            String language, Integer userId);

    /**
     * 获取工作人员
     *
     * @param user 用户信息
     * @return 当前用户设定的工作人员
     */
    Staff getStaff(User user);

    /**
     * 导出随车质检单数据
     *
     * @param groupNumber 集团编号（可选）
     * @param customerNumbers 客户编号列表（必选）
     * @param productCategoryNumbers 产品类别编号列表（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @param separateByCustomer 是否根据客户区分Excel工作表
     * @return 导出文件路径
     */
    String exportTransportReportData(String groupNumber, List<String> customerNumbers,
            List<String> productCategoryNumbers, String startDate, String endDate,
            Boolean separateByCustomer);

    /**
     * 设置工作人员
     *
     * @param staff 工作人员信息
     * @return 设置结果
     */
    Boolean setStaff(Staff staff);

    /**
     * 随车质检单检测项目数据结果判断
     *
     * @param resultJudgmentDTO 结果判断DTO
     * @return 判断结果
     */
    Boolean resultJudgment(ResultJudgmentDTO resultJudgmentDTO);

    /**
     * 获取当前客户当前生产批号历史出库记录
     *
     * @param transportReportCreatDTO 随车质检单创建DTO
     * @return 出库记录map
     */
    Map<String, Object> getProductionBatchOutboundRecord(
            TransportReportCreatDTO transportReportCreatDTO);

    /**
     * 判断当前客户当前质检批号对应其他生产批号的检测数据与当前检测数据是否相同
     *
     * @param transportReportDTO 随车质检单DTO
     * @return 判断结果
     */
    Boolean getProductionBatchInspectionData(TransportReportDTO transportReportDTO);

    /**
     * 改变线上开单状态
     *
     * @param transferRecordDTO 出库传递单DTO
     * @return 改变结果
     */
    TransportReportOnlineAvailability changeOnlineAvailable(TransferRecordDTO transferRecordDTO);
}
