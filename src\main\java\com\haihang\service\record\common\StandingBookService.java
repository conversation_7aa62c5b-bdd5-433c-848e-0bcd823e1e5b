package com.haihang.service.record.common;

import com.haihang.model.Query.record.ItemDataQuery;
import com.haihang.model.Query.standingBook.StandingBookQuery;
import com.haihang.model.VO.record.ProductTypeLabelVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface StandingBookService {

    /**
     * 获取产品列表
     *
     * @param linkId         linkId
     * @param isSemiFinished 半成品
     * @param userId         用户id
     * @return 产品列表
     */
    List<ProductTypeLabelVO> getProductTypeList(String linkId, String isSemiFinished, String userId);

    /**
     * 获取台账信息
     *
     * @param standingBookQuery 台账query
     * @return 台账map（表头&数据）
     */
    Map<String, Object> getStandingBook(StandingBookQuery standingBookQuery);

    /**
     * 获取质检项目数据
     *
     * @param itemDataQuery 质检项目数据query
     * @return 质检项目数据map集合
     */
    List<Map<String, Object>> getItemData(ItemDataQuery itemDataQuery);

    /**
     * 获取抽检项目数据
     *
     * @param itemDataQuery 质检项目数据query
     * @return 抽检项目数据map集合
     */
    List<Map<String, Object>> getSamplingItemData(ItemDataQuery itemDataQuery);

    /**
     * 生成台账Excel文件
     * 
     * @param standingBookQuery 台账查询参数对象，包含以下主要参数：
     *                          - userId: 用户ID
     *                          - linkId: 关联ID
     *                          - startDate: 开始日期
     *                          - endDate: 结束日期
     *                          - dateRange: 是否使用日期范围
     *                          - productCategoryNumber: 产品类别编号
     * @return 生成的Excel文件路径
     * 
     *         主要功能：
     *         1. 查询用户信息
     *         2. 获取目标公司全部产品类型
     *         3. 按产品类型优先级排序
     *         4. 创建Excel文件目录
     *         5. 为每个产品类型生成Excel工作表
     *         6. 设置Excel表头和数据
     *         7. 应用Excel样式
     * 
     *         处理逻辑：
     *         - 对于每个产品类型，查询相关质检数据
     *         - 获取通用指标和集团指标
     *         - 按优先级对检测项目进行排序
     *         - 将数据写入Excel工作表
     *         - 处理特殊产品类型（如预分散体）
     */
    String standingBookExcelCreate(StandingBookQuery standingBookQuery);

    /**
     * 生成样品存放台账Excel文件
     * 
     * @param standingBookQuery 台账查询参数对象，包含以下主要参数：
     *                          - userId: 用户ID
     *                          - linkId: 关联ID
     *                          - startDate: 开始日期
     *                          - endDate: 结束日期
     *                          - dateRange: 是否使用日期范围
     * @return 生成的Excel文件路径
     * 
     *         主要功能：
     *         1. 查询用户信息
     *         2. 获取所有产品类型的质检记录
     *         3. 剔除带"[复]"字样的记录
     *         4. 去除质检批号中的"[放行]"字样
     *         5. 生成样品存放台账Excel文件
     * 
     *         Excel结构：
     *         - 第一行：样品存放台账（横向合并所有列的单元格）
     *         - 第二行：质检批号、罐次、包装规格、数量、客户名称、质检人员、样品存放区域
     *         - 样品存放区域：产品名称+样品橱柜X月份区域
     */
    String sampleStorageStandingBookExcelCreate(StandingBookQuery standingBookQuery);
}
