package com.haihang.model.DO.outbound;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@TableName(value = "cmf_transport_report_item_i18n")
public class TransportReportItemI18n {
    @TableId(value = "id")
    private Integer id;

    private Integer groupId;
    private String groupNumber;
    private String companyNumber;

    @TableField(value = "zh_cn")
    private String zhCn;
    @TableField(value = "zh_cn_optimized")
    private String zhCnOptimized;
    
    @TableField(value = "en_us")
    private String enUs;
    @TableField(value = "en_us_optimized")
    private String enUsOptimized;
}
