<!DOCTYPE html>
<html lang="en">
<head>
    <!-- 页面meta -->
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>分析记录台账-尚舜化工</title>
    <meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" name="viewport">
    <!-- 引入样式 -->
    <link rel="stylesheet" href="../../plugins/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="../../css/style.css">
    <link rel="icon" type="image/png" href="../../picture/logo.png">
    <style>
        [v-cloak] {
            display: none;
        }

        /* 字体设置 */
        @font-face {
            font-family: "zad";
            src: url("../../font/MiSans-Medium.woff2");
            /*font-weight: bold;*/
        }

        /* 字体应用组件 */
        html, body, button, input, select, textarea, form {
            font-family: "zad", sans-serif;
            font-size: 14px !important;
        }

        .el-divider__text {
            font-size: 18px !important;
        }

        .el-message__content {
            font-size: 14px !important;
        }

        .el-tag {
            font-size: 14px !important;
        }

        .el-table .cell {
            font-size: 16px !important;
        }

        .el-form-item .el-form-item__label {
            font-size: 16px !important; /* 更改此值以设置所需的字体大小，并使用 !important 标志 */
        }

        .el-dialog__title {
            font-size: 22px !important; /* 修改为自己想要的字体大小 */
        }

        .el-tooltip__popper {
            font-size: 14px !important; /* 更改此值以设置所需的字体大小 */
        }

        .el-descriptions-item__label,
        .el-descriptions-item__content {
            font-size: 16px !important; /* 修改为你需要的字体大小 */
        }

        .custom-prepend-text {
            font-size: 14px !important; /* 更改此值以设置所需的字体大小 */
        }

        /* 修改标题文字大小 */
        .el-message-box__header .el-message-box__title {
            font-size: 18px !important; /* 更改此值以设置标题文字的字体大小 */
        }

        /* 修改内容文字大小 */
        .el-message-box__message {
            font-size: 16px !important; /* 更改此值以设置内容文字的字体大小 */
        }


        /*.el-table__body tr:hover > td {
            background-color: #FFDAB9 !important;
        }*/

        .el-table__body .el-table__row.hover-row td {
            background-color: #FFDAB9 !important;
        }


        /* 选中某行颜色 */
        .el-table--striped .el-table__body tr.el-table__row--striped.current-row td,
        .el-table__body tr.current-row > td {
            background-color: #FFDAB9 !important;
        }

        .recordEditInput .el-input__inner {
            text-align: center !important;
            /*font-weight: bolder;*/
            font-size: 14px !important;
            background-color: #c9eccd;
        }

        .el-select .el-select__input,
        .el-select-dropdown .el-select-dropdown__item {
            text-align: center !important;
            font-size: 14px !important;
            /*font-weight: bolder !important;*/
        }

        .el-select .el-input__inner {
            text-align: center !important;
            font-size: 14px !important;
            /*font-weight: bolder !important;*/
        }

        .el-select__tags-text {
            text-align: center !important;
            font-size: 14px !important;
            /*font-weight: bolder !important;*/
        }

        .el-cascader .el-input__inner::placeholder {
            text-align: center;
        }

        .el-dialog .el-dialog__body {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .tag-title {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            text-align: center;
            /*font-weight: bolder !important;*/
            font-size: 14px !important;
        }

        .tag-result {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            text-align: center;
            /*font-weight: bolder !important;*/
            font-size: 14px !important;
        }

        .el-table__body-wrapper {
            z-index: 2;
        }

        .el-table__fixed {
            height: 100% !important;
        }

        .el-table__fixed-right {
            height: 100% !important;
        }
    </style>
</head>
<body class="hold-transition">
<div id="app" v-loading.fullscreen.lock="fullscreenLoading" v-cloak>
    <!--标题-->
    <div class="content-header">
        <h1>分析记录台账</h1>
    </div>
    <!--内容-->
    <div class="app-container">
        <div class="box">
            <br>
            <!--条件查询-->
            <div>
                <el-row :gutter="20">
                    <el-col :span="4" v-if="user.id === 79
                                            || user.id === 610
                                            || user.id === 265
                                            || user.id === 616
                                            || user.id === 617
                                            || user.id === 997
                                            || user.id === 421
                                            || user.id === 787
                                            || user.id === 545
                                            || user.id === 785
                                            || user.id === 784
                                            || user.id === 780
                                            || user.id === 783
                                            || user.id === 825
                                            || user.id === 703
                                            || user.id === 782
                                            || user.id === 343
                                            || user.id === 706
                                            || user.id === 700">
                        <el-select v-model="pagination.linkId" filterable placeholder="请选择公司"
                                   :style="{width: '100%'}"
                                   @change="getAll()">
                            <el-option
                                    v-for="item in linkId"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                            </el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="4">
                        <el-select v-model="pagination.productType" filterable placeholder="请选择产品类型"
                                   :style="{width: '100%'}"
                                   @change="getAll()"
                        >
                            <el-option
                                    v-for="item in productTypes"
                                    :key="item.label"
                                    :label="item.label"
                                    :value="item.value">
                            </el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="6">
                        <el-date-picker
                                placement="bottom-start"
                                v-model="pagination.samplingDate"
                                type="monthrange"
                                range-separator="至"
                                start-placeholder="开始月份"
                                end-placeholder="结束月份"
                                value-format="yyyy-MM"
                                @change="getAll()"
                                clearable
                                :style="{width: '100%'}">
                        </el-date-picker>
                    </el-col>
                    <el-col :span="4">
                        <el-cascader
                                placeholder="请选择检测结果"
                                :options="finalResult"
                                :props="{ multiple: true }"
                                :style="{width: '100%'}"
                                v-model="pagination.finalResult"
                                collapse-tags
                                clearable
                        ></el-cascader>
                    </el-col>
                    <el-col :span="6">
                        <el-tooltip content="记录查询" placement="top">
                            <el-button @click="getAll()" type="primary" size="small" icon="el-icon-search" circle>
                            </el-button>
                        </el-tooltip>
                        <el-tooltip content="页面刷新" placement="top">
                            <el-button @click="resetSearch()" type="primary" size="small" icon="el-icon-refresh" circle>
                            </el-button>
                        </el-tooltip>
                        <el-tooltip v-if="!moreSearch" content="更多筛选" placement="top">
                            <el-button @click="openSearch()" type="warning" size="small" icon="el-icon-arrow-down"
                                       circle>
                            </el-button>
                        </el-tooltip>
                        <el-tooltip v-else content="更多筛选" placement="top">
                            <el-button @click="closeSearch()" type="warning" size="small" icon="el-icon-arrow-up"
                                       circle>
                            </el-button>
                        </el-tooltip>
                        <el-tooltip content="台账下载" placement="top">
                            <el-button @click="openStandingBookDownload()" type="success" size="small"
                                       icon="el-icon-download"
                                       circle>
                            </el-button>
                        </el-tooltip>
                    </el-col>
                </el-row>
                <el-divider v-if="moreSearch"></el-divider>
                <el-row v-if="moreSearch" :gutter="20">
                    <el-col :span="4">
                        <el-input placeholder="质检批号" v-model="pagination.qualityInspectionBatch"
                                  :style="{width: '100%'}"
                                  @change="getAll()"></el-input>
                    </el-col>
                    <el-col :span="4">
                        <el-input placeholder="生产批号" v-model="pagination.productionBatch" :style="{width: '100%'}"
                                  @change="getAll()"></el-input>
                    </el-col>
                    <el-col :span="4">
                        <el-input placeholder="客户名称" v-model="pagination.customerName" :style="{width: '100%'}"
                                  @change="getAll()"></el-input>
                    </el-col>
                    <el-col :span="6">
                        <el-tooltip content="记录查询" placement="top">
                            <el-button @click="getAll()" type="primary" size="small" icon="el-icon-search" circle>
                            </el-button>
                        </el-tooltip>
                        <el-tooltip content="查询刷新" placement="top">
                            <el-button @click="resetSearch2()" type="primary" size="small" icon="el-icon-refresh"
                                       circle>
                            </el-button>
                        </el-tooltip>
                    </el-col>
                </el-row>
            </div>
            <br>
            <!--分页列表-->
            <el-table ref="standingBookTable" size="small"
                      v-loading="standingBookLoading"
                      current-row-key="id"
                      :data="dataList"
                      stripe
                      :header-cell-style="{background: '#f2f4f7', color: '#606266'}"
                      highlight-current-row
                      border
                      max-height="650"
                      show-summary
                      :summary-method="getTotal">
                <el-table-column type="index" align="center" label="序号" fixed width="60"
                                 sortable></el-table-column>
                <el-table-column label="质检批号" prop="qualityInspectionBatch" align="center"
                                 fixed width="150"
                                 sortable :show-overflow-tooltip=true>
                </el-table-column>
                <el-table-column label="生产批号" prop="productionBatch" align="center" fixed
                                 width="150" sortable
                                 :show-overflow-tooltip=true>
                </el-table-column>
                <el-table-column v-if="pagination.productType === '0105'" label="产品类型"
                                 prop="productType" align="center" fixed width="150" sortable
                                 :show-overflow-tooltip=true>
                </el-table-column>
                <el-table-column  label="罐次" prop="tankBatch" align="center" fixed width="100"
                                 sortable resizable
                                 :show-overflow-tooltip=true>
                </el-table-column>
                <el-table-column v-if="columnList !== null" v-for="(item, index) in columnList" :key="`${item.label}-${index}`"
                                 :label="item.label" align="center"
                                 :property="item.label"
                                 min-width=110
                                 sortable resizable :show-overflow-tooltip=true>
                </el-table-column>
                <el-table-column v-if="columnList === null" v-for="(item, index) in groupColumnList" :key="`${item.label}-${index}`"
                                 :label="item.label" align="center"
                                 :property="item.label"
                                 min-width=110
                                 sortable resizable :show-overflow-tooltip=true>
                </el-table-column>
                <el-table-column label="规格" prop="packagingSpecification" align="center"
                                 width="100"
                                 sortable resizable :show-overflow-tooltip=true>
                </el-table-column>
                <el-table-column label="数量" prop="quantity" align="center" width="100"
                                 sortable resizable :show-overflow-tooltip=true>
                </el-table-column>
                <el-table-column label="吨数" prop="stairs" align="center" property="stairs"
                                 width="100"
                                 sortable resizable :show-overflow-tooltip=true>
                </el-table-column>
                <el-table-column label="结论" prop="result" align="center" width="200"
                                 sortable resizable :show-overflow-tooltip=true>
                </el-table-column>
                <el-table-column label="质检班长" prop="inspectorLeader" align="center" width="110"
                                 sortable resizable :show-overflow-tooltip=true>
                </el-table-column>
                <el-table-column label="客户名称" prop="customerName" align="center" width="150"
                                 sortable
                                 resizable :show-overflow-tooltip=true>
                </el-table-column>
                <el-table-column v-if="columnList !== null" v-for="(item, index) in groupColumnList" :key="`${item.label}-${index}`"
                                 :label="item.label" align="center"
                                 :property="item.label"
                                 min-width=110
                                 sortable resizable :show-overflow-tooltip=true>
                </el-table-column>
                <el-table-column label="抽检项目" prop="samplingItem" align="center" width="160"
                                 sortable resizable :show-overflow-tooltip=true>
                </el-table-column>
                <el-table-column label="抽检数量" prop="samplingNumber" align="center" width="110"
                                 sortable resizable :show-overflow-tooltip=true>
                </el-table-column>
                <el-table-column label="抽检数据" prop="samplingData" align="center" width="160"
                                 sortable resizable :show-overflow-tooltip=true>
                </el-table-column>

                <el-table-column label="质检人员" prop="inspector" align="center" width="150"
                                 sortable resizable :show-overflow-tooltip=true>
                </el-table-column>
                <el-table-column v-if="!(
                                            user.id === 787
                                            || user.id === 545
                                            || user.id === 785
                                            || user.id === 784
                                            || user.id === 780
                                            || user.id === 783
                                            || user.id === 825
                                            || user.id === 703
                                            || user.id === 782
                                            || user.id === 827
                                            || user.id === 343
                                            || user.id === 706
                                            || user.id === 700
                                            || user.id === 52
                                            || user.id === 833
                                            || user.id === 777
                                            || user.id === 51
                                            || user.id === 421
                        ) && !(user.linkId === 4 && user.id !== 79)" label="操作" align="center" fixed="right">
                    <template slot-scope="scope">
                        <el-button type="danger" size="mini" @click="revoke(scope.row)"
                                   icon="el-icon-refresh-left" circle>
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!--质检数据查看弹层-->
            <div>
                <el-dialog :visible.sync="itemDataVisible"
                           :title="dialogTitle"
                           :append-to-body="true"
                           :lock-scroll="false"
                           width="45%"
                           center
                           :before-close="backToList">
                    <div style="width: 100%; display: flex; flex-direction: column; margin: 0 20px;">
                        <el-descriptions
                                border
                                :label-style="LS"
                                :content-style="CS"
                                :column="2">
                            <el-descriptions-item v-for="(itemData, index) in itemDataList" :key="index"
                                                  :label="itemData.label">{{ itemData.value }}
                            </el-descriptions-item>
                        </el-descriptions>
                    </div>
                </el-dialog>
            </div>
            <!--台账下载-->
            <div>
                <el-dialog :visible.sync="standingBookDownloadVisible"
                           title="台账下载"
                           :append-to-body="true"
                           :lock-scroll="false"
                           :before-close="backToForm"
                           width="50%"
                           center>
                    <div style="width: 100%; display: flex; flex-direction: column; margin: 0 20px;">
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-select v-model="standingBookQuery.linkId" filterable placeholder="请选择公司"
                                           :style="{width: '100%'}"
                                           :disabled="!(user.id === 79
                                           || user.id === 610
                                           || user.id === 265
                                           || user.id === 616
                                           || user.id === 617
                                           || user.id === 997)"
                                >
                                    <el-option
                                            v-for="item in linkId"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-col>
                            <el-col :span="14">
                                <el-date-picker
                                        placement="bottom-start"
                                        v-if="!dateRange"
                                        v-model="standingBookQuery.samplingDate"
                                        type="monthrange"
                                        range-separator="至"
                                        start-placeholder="开始月份"
                                        end-placeholder="结束月份"
                                        value-format="yyyy-MM"
                                        unlink-panels
                                        clearable
                                        :style="{width: '100%'}">
                                </el-date-picker>
                                <el-date-picker
                                        placement="bottom-start"
                                        v-if="dateRange"
                                        v-model="standingBookQuery.samplingDate"
                                        type="daterange"
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        value-format="yyyy-MM-dd"
                                        clearable
                                        :style="{width: '100%'}">
                                </el-date-picker>
                            </el-col>
                            <el-col :span="2">
                                <el-button type="primary" icon="el-icon-refresh" @click="pickerChange()" size="small"
                                           circle></el-button>
                            </el-col>
                        </el-row>
                    </div>
                    <span slot="footer" class="dialog-footer">
                        <el-tooltip content="台账下载（合并生产批号）" placement="top">
                            <el-button @click="standingBookDownload(true)"
                                       type="success"
                                       size="small"
                                       icon="el-icon-download"
                                       circle
                            ></el-button>
                        </el-tooltip>
                        <el-tooltip content="台账下载（独立生产批号）" placement="top">
                            <el-button @click="standingBookDownload(false)"
                                       type="warning"
                                       size="small"
                                       icon="el-icon-download"
                                       circle
                            ></el-button>
                        </el-tooltip>
                        <el-tooltip content="样品存放台账下载" placement="top">
                            <el-button @click="sampleStorageStandingBookDownload()"
                                       type="info"
                                       size="small"
                                       icon="el-icon-box"
                                       circle
                            ></el-button>
                        </el-tooltip>
                        <el-tooltip content="取消下载" placement="top">
                            <el-button type="danger" icon="el-icon-close" @click="backToForm()" size="small"
                                       circle></el-button>
                        </el-tooltip>
                    </span>
                </el-dialog>
            </div>
        </div>
    </div>
</div>
</body>
<!-- 引入组件库 -->
<script src="../../js/vue.js"></script>
<script src="../../element-ui/lib/index.js"></script>
<link rel="stylesheet" href="../../element-ui/lib/theme-chalk/index.css">
<script type="text/javascript" src="../../js/jquery.min.js"></script>
<script src="../../js/axios.min.js"></script>
<script>
    let vue = new Vue({
        el: '#app',
        data: {
            fullscreenLoading: true,
            standingBookLoading: false,
            tableBodyWrapper: null,
            user: "",
            // 当前页要展示的列表数据
            dataList: [],
            // 分页相关模型数据
            pagination: {
                // 当前页码
                currentPage: 1,
                // 每页显示的记录数
                pageSize: 10000,
                // 总记录数
                total: 0,
                linkId: "",
                // 产品类型
                productType: "",
                samplingDate: ["", ""],
                qualityInspectionBatch: "",
                productionBatch: "",
                customerName: "",
                finalResult: []
            },
            columnList: {},
            groupColumnList: {},
            itemDataVisible: false,
            standingBookDownloadVisible: false,
            itemDataList: [],
            dialogTitle: "",
            LS: {
                'font-weight': '600',
                'min-width': '80px',
                'height': '10px',
                'word-break': 'keep-all'
            },
            CS: {
                'min-width': '80px',
                'word-break': 'keep-all'
            },
            productTypes: [],
            moreSearch: false,
            linkId: [
                {
                    value: 3,
                    label: "尚舜"
                },
                {
                    value: 8,
                    label: "永舜"
                },
                {
                    value: 4,
                    label: "恒舜"
                },
                {
                    value: 7,
                    label: "潍坊"
                }
            ],
            finalResult: [
                {
                    value: 1,
                    label: "合格"
                },
                {
                    value: 2,
                    label: "不符合",
                    children: [{
                        value: 3,
                        label: 'Y'
                    }, {
                        value: 4,
                        label: 'N'
                    }]
                },
                {
                    value: 0,
                    label: "不合格"
                }
            ],
            standingBookQuery: {
                linkId: "",
                samplingDate: ["", ""],
            },
            dateRange: false
        },

        // 钩子函数，VUE对象初始化完成后自动执行
        created() {
            let url = decodeURI(location.search).slice(1);
            // 创建空对象存储参数
            let obj = {};
            // 再通过 & 将每一个参数单独分割出来
            let paramsArr = url.split('&')
            for (let i = 0, len = paramsArr.length; i < len; i++) {
                // 再通过 = 将每一个参数分割为 key:value 的形式
                let arr = paramsArr[i].split('=')
                obj[arr[0]] = arr[1];
            }
            this.param = {
                id: obj.uid,
                password: obj.pwd
            };
            axios.post("/user/verification", this.param).then((res) => {
                if (res.data.flag) {
                    // this.getAll();
                    this.user = res.data.data;
                    this.pagination.linkId = this.user.linkId;
                    axios.get("/standingBook/getProductTypes/" + this.pagination.linkId + "/0/" + this.param.id).then((res) => {
                        if (res.data.flag) {
                            this.productTypes = res.data.data;
                        } else {
                            this.$message.error("产品信息获取异常");
                        }
                    });
                    this.fullscreenLoading = false;
                }
            });
        },

        mounted() {
            this.$nextTick(()=> {
                this.tableBodyWrapper = this.$refs.standingBookTable.bodyWrapper;
                this.tableBodyWrapper.addEventListener('scroll',this.setScrollTop);
            });
        },

        beforeDestroy() {
            this.tableBodyWrapper.removeEventListener('scroll',this.setScrollTop);
        },


        methods: {
            // 分页查询
            getAll() {
                axios.get("/standingBook/getProductTypes/" + this.pagination.linkId + "/0/" + this.param.id).then((res) => {
                    if (res.data.flag) {

                        this.productTypes = res.data.data;
                        if (this.pagination.productType) {

                            let param = "?productCategoryNumber=" + this.pagination.productType;
                            if (this.pagination.samplingDate == null) {
                                this.pagination.samplingDate = ["", ""];
                            }
                            param += "&startDate=" + this.pagination.samplingDate[0];
                            param += "&endDate=" + this.pagination.samplingDate[1];
                            param += "&userId=" + this.param.id;
                            param += "&linkId=" + this.pagination.linkId;
                            param += "&productionBatch=" + this.pagination.productionBatch;
                            param += "&qualityInspectionBatch=" + this.pagination.qualityInspectionBatch;
                            param += "&customerName=" + this.pagination.customerName;
                            param += "&finalResult=" + JSON.stringify(this.pagination.finalResult);
                            param += "&dateRange=" + false;
                            this.standingBookLoading = true;
                            axios.get("/standingBook/getAll" + param).then((res) => {
                                if (res.data.flag) {
                                    this.columnList = res.data.data.columnList;
                                    this.groupColumnList = res.data.data.groupColumnList;
                                    // this.dataList = res.data.data.dataList;
                                    this.$nextTick(() => {
                                        this.dataList = res.data.data.dataList;
                                        this.$refs.standingBookTable.doLayout();
                                    });
                                    this.standingBookLoading = false;
                                } else {
                                    this.$message.error("集团信息异常,请检查后重试");
                                }
                            })
                        }
                    } else {
                        this.$message.error("产品信息获取异常");
                    }
                });
            },

            // 切换页码
            handleCurrentChange(currentPage) {
                this.pagination.currentPage = currentPage;
                this.getAll();
            },

            // 重置搜索
            resetSearch() {
                this.pagination.qualityInspectionBatch = "";
                this.pagination.productionBatch = "";
                this.pagination.customerName = "";
                // this.pagination.productType = "";
                this.pagination.samplingDate = ["", ""];
                this.pagination.finalResult = [];
                if (!this.pagination.productType) {
                    this.getAll();
                }
            },

            // 打开额外搜索界面
            openSearch() {
                this.moreSearch = true;
            },

            // 关闭额外搜索界面
            closeSearch() {
                this.moreSearch = false;
            },

            // 重置额外搜索
            resetSearch2() {
                this.pagination.qualityInspectionBatch = "";
                this.pagination.productionBatch = "";
                this.pagination.customerName = "";
                if (!this.pagination.productType) {
                    this.getAll();
                }
            },

            // 查看检测项目原始数据
            itemDataView(id, label) {
                let itemDataQuery = {};
                itemDataQuery['id'] = id;
                itemDataQuery['item'] = label;
                axios.post("/standingBook/getItemData", itemDataQuery).then((res) => {
                    if (res.data.flag) {
                        this.dialogTitle = label + "原始数据";
                        this.itemDataList = res.data.data;
                        this.itemDataVisible = true;
                    }
                });

            },

            // 返回台账列表
            backToList() {
                this.itemDataVisible = false;
                this.$message.info("返回质检记录列表")
            },

            // 撤回质检记录至未提交状态
            revoke(row) {
                this.$confirm("此操作将重置本条分析记录为未提交状态,是否继续?", "提示", {type: "warning"}).then(() => {
                    let recordRevokeDTO = {};
                    recordRevokeDTO.user = this.user;
                    recordRevokeDTO.recordId = row.id;
                    axios.post("/analysisRecordData/revoke", recordRevokeDTO).then((res) => {
                        if (res.data.flag) {
                            this.$message.success("撤回成功");
                            this.getAll();
                        } else {
                            this.$message.error("撤回失败");
                        }
                    });
                }).catch(() => {
                    this.$message.info("操作取消");
                });
            },

            // 返回台账列表
            backToForm() {
                this.standingBookDownloadVisible = false;
                this.$message.info("返回台账数据列表")
            },

            // 打开台账下载界面
            openStandingBookDownload() {
                this.standingBookQuery.samplingDate = this.pagination.samplingDate;
                this.standingBookQuery.linkId = this.pagination.linkId;
                this.standingBookDownloadVisible = true;
            },

            /**
             * 下载质检台账
             *
             * needToMerge: 生产批号是否合并显示
             */
            standingBookDownload(needToMerge) {
                this.fullscreenLoading = true;
                if (this.standingBookQuery.samplingDate == null) {
                    this.standingBookQuery.samplingDate = ["", ""];
                }
                let param = "?startDate=" + this.standingBookQuery.samplingDate[0];
                param += "&endDate=" + this.standingBookQuery.samplingDate[1];
                param += "&userId=" + this.param.id;
                param += "&linkId=" + this.standingBookQuery.linkId;
                param += "&dateRange=" + this.dateRange;
                param += "&needToMerge=" + needToMerge;
                axios.get("/standingBook/standingBookExcelCreate" + param).then((res) => {
                    if (res.data.flag) {
                        this.fullscreenLoading = false;
                        window.location.href = "/standingBook/standingBookExcelCreateDownload/" + this.user.id + "/" + res.data.msg;
                    } else {
                        this.$message.error(res.data.msg);
                    }
                })
            },

            /**
             * 下载样品存放台账
             */
            sampleStorageStandingBookDownload() {
                this.fullscreenLoading = true;
                if (this.standingBookQuery.samplingDate == null) {
                    this.standingBookQuery.samplingDate = ["", ""];
                }
                let param = "?startDate=" + this.standingBookQuery.samplingDate[0];
                param += "&endDate=" + this.standingBookQuery.samplingDate[1];
                param += "&userId=" + this.param.id;
                param += "&linkId=" + this.standingBookQuery.linkId;
                param += "&dateRange=" + this.dateRange;
                axios.get("/standingBook/sampleStorageStandingBookExcelCreate" + param).then((res) => {
                    if (res.data.flag) {
                        this.fullscreenLoading = false;
                        window.location.href = "/standingBook/standingBookExcelCreateDownload/" + this.user.id + "/" + res.data.msg;
                    } else {
                        this.$message.error(res.data.msg);
                        this.fullscreenLoading = false;
                    }
                })
            },

            // 数据统计（总计）
            getTotal(param) {
                const {columns, data} = param;
                const sums = [];

                columns.forEach((column, index) => {
                    // console.log(column.property);

                    if (index === 0) {
                        sums[index] = '统计';
                        return;
                    }

                    if (column.property === "qualityInspectionBatch" || column.property === "productionBatch" || column.property === "customerName" || column.property === "tankBatch" || column.property === "packagingSpecification" || column.property === "quantity") {
                        return;
                    }

                    const values = data.map(item => {
                        if (item && item[column.property]) {
                            if (typeof item[column.property] === 'object') {
                                if (item[column.property].data !== null && item[column.property].data !== undefined) {
                                    let value = Number(item[column.property].data);
                                    if ((column.property.includes("颗粒强度") || column.property.includes("最大平均值")) && value > 10) {
                                        value = value / 100; // 大于10的数值除以100
                                    }
                                    return value;
                                }
                            } else {
                                let value = Number(item[column.property]);
                                if ((column.property.includes("颗粒强度") || column.property.includes("最大平均值")) && value > 10) {
                                    value = value / 100; // 大于10的数值除以100
                                }
                                return value;
                            }
                        }
                    });

                    const validValues = values.filter(value => !isNaN(value)); // 过滤掉非数字的值
                    if (validValues.length > 0) {
                        const sum = validValues.reduce((prev, curr) => prev + curr, 0);
                        const average = sum / validValues.length; // 计算平均值
                        sums[index] = (Math.round(average * 100) / 100).toFixed(2); // 保留两位小数
                        if (this.groupColumnList === null) {
                            if (!(
                                this.user.id === 787
                                || this.user.id === 545
                                || this.user.id === 785
                                || this.user.id === 784
                                || this.user.id === 780
                                || this.user.id === 783
                                || this.user.id === 825
                                || this.user.id === 703
                                || this.user.id === 782
                                || this.user.id === 827
                                || this.user.id === 343
                                || this.user.id === 706
                                || this.user.id === 700
                                || this.user.id === 52
                                || this.user.id === 833
                                || this.user.id === 777
                                || this.user.id === 51
                            ) && this.user.id !== 421) {
                                if (index === columns.length - 9) {
                                    sums[index] = (Math.round(sum * 1000) / 1000).toFixed(3).replace(/\.?0+$/, '');
                                }
                            } else {
                                if (index === columns.length - 8) {
                                    sums[index] = (Math.round(sum * 1000) / 1000).toFixed(3).replace(/\.?0+$/, '');
                                }
                            }

                        } else {
                            if (!(
                                this.user.id === 787
                                || this.user.id === 545
                                || this.user.id === 785
                                || this.user.id === 784
                                || this.user.id === 780
                                || this.user.id === 783
                                || this.user.id === 825
                                || this.user.id === 703
                                || this.user.id === 782
                                || this.user.id === 827
                                || this.user.id === 343
                                || this.user.id === 706
                                || this.user.id === 700
                                || this.user.id === 52
                                || this.user.id === 833
                                || this.user.id === 777
                                || this.user.id === 51
                            ) && this.user.id !== 421) {
                                if (index === columns.length - (9 + this.groupColumnList.length)) {
                                    sums[index] = (Math.round(sum * 1000) / 1000).toFixed(3).replace(/\.?0+$/, '');
                                }
                            } else {
                                if (index === columns.length - (8 + this.groupColumnList.length)) {
                                    sums[index] = (Math.round(sum * 1000) / 1000).toFixed(3).replace(/\.?0+$/, '');
                                }
                            }
                        }
                        /*if (this.groupColumnList === null) {
                            if (index === columns.length - 6) {
                                sums[index] = (Math.round(sum * 1000) / 1000).toFixed(3).replace(/\.?0+$/, '');
                            }
                        } else {
                            if (this.columnList !== null) {
                                if (index === columns.length - (6 + this.groupColumnList.length)) {
                                    sums[index] = (Math.round(sum * 1000) / 1000).toFixed(3).replace(/\.?0+$/, '');
                                }
                            } else {
                                if (index === columns.length - 6) {
                                    sums[index] = (Math.round(sum * 1000) / 1000).toFixed(3).replace(/\.?0+$/, '');
                                }
                            }
                        }*/
                        /*if (index === columns.length - 4) {
                            sums[index] = (Math.round(sum * 1000) / 1000).toFixed(3).replace(/\.?0+$/, '');
                        }*/
                    } else {
                        sums[index] = '';
                    }
                });
                return sums;
            },

            // 切换日期选择器模式（月份选择、日期选择）
            pickerChange() {
                this.dateRange = !this.dateRange;
                this.standingBookQuery.samplingDate = ["", ""];
            },

            setScrollTop() {
                this.$refs.standingBookTable.$refs.fixedBodyWrapper.scrollTop = this.tableBodyWrapper.scrollTop;
            },
        }
    })
</script>
</html>