package com.haihang.model.DO.outbound;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 运输报告模板实体类
 * <p>
 * 该类用于管理运输质检报告的模板配置，包括模板的基本信息如模板类型、路径、优先级等，以及报告中各项内容的位置坐标信息。
 * 对应数据库表 cmf_transport_report_template。
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@Accessors(chain = true)
@TableName(value = "cmf_transport_report_template")
public class TransportReportTemplate {
    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Integer id;

    /**
     * 集团编号
     * <p>
     * 用于指定模板所属的集团编号，可用于集团级别的模板定制
     * </p>
     */
    private String groupNumber;

    /**
     * 集团ID
     * <p>
     * 用于指定模板所属的集团ID，可用于集团级别的模板定制
     * </p>
     */
    private Integer groupId;

    /**
     * 客户编号
     * <p>
     * 用于指定模板所属的客户编号，可用于客户级别的模板定制
     * </p>
     */
    private String customerNumber;

    /**
     * 模板字段
     * <p>
     * 用于存储模板中所有字段的位置信息
     * </p>
     */
    private String templateField;

    /**
     * 模板优先级
     * <p>
     * 用于排序模板的优先级顺序
     * </p>
     */
    private Integer priority;

    /**
     * 模板文件路径
     */
    private String path;

    /**
     * 模板项目数量
     * <p>
     * 模板中可包含的检测项目数量
     * </p>
     */
    private Integer itemNumber;

    /**
     * 模板类型
     * <p>
     * 区分不同用途的模板类型
     * </p>
     */
    private Integer templateType;

    /**
     * 语言类型
     * <p>
     * 模板支持的语言类型，例如：1-中文，2-英文等
     * </p>
     */
    private Integer languageType;

    /**
     * 是否添加检测方法
     */
    private Boolean isAddTestMethod;

    /**
     * 模板缩放比例
     */
    private Integer zoom;

    /**
     * 记录编号行位置
     */
    private Integer recordCodeRow;

    /**
     * 记录编号列位置
     */
    private Integer recordCodeColumn;

    /**
     * 客户名称行位置
     */
    private Integer customerNameRow;

    /**
     * 客户名称列位置
     */
    private Integer customerNameColumn;

    /**
     * 重量行位置
     */
    private Integer weightRow;

    /**
     * 重量列位置
     */
    private Integer weightColumn;

    /**
     * 产品类型行位置
     */
    private Integer productTypeRow;

    /**
     * 产品类型列位置
     */
    private Integer productTypeColumn;

    /**
     * 包装规格行位置
     */
    private Integer packingSpecificationRow;

    /**
     * 包装规格列位置
     */
    private Integer packingSpecificationColumn;

    /**
     * 执行标准行位置
     */
    private Integer executionStandardRow;

    /**
     * 执行标准列位置
     */
    private Integer executionStandardColumn;

    /**
     * 生产批号行位置
     */
    private Integer productionBatchRow;

    /**
     * 生产批号列位置
     */
    private Integer productionBatchColumn;

    /**
     * 生产日期行位置
     */
    private Integer productionDateRow;

    /**
     * 生产日期列位置
     */
    private Integer productionDateColumn;

    /**
     * 要求行位置
     */
    private Integer requirementRow;

    /**
     * 要求列位置
     */
    private Integer requirementColumn;

    /**
     * 数据行起始位置
     */
    private Integer dataRow;

    /**
     * 数据列起始位置
     */
    private Integer dataColumn;

    /**
     * 包装规格2行位置
     * <p>
     * 用于特殊模板中第二个包装规格的位置
     * </p>
     */
    private Integer packingSpecificationRow2;

    /**
     * 包装规格2列位置
     */
    private Integer packingSpecificationColumn2;

    /**
     * 数量行位置
     */
    private Integer quantityRow;

    /**
     * 数量列位置
     */
    private Integer quantityColumn;

    /**
     * 合并单元格起始位置
     * <p>
     * 用于Excel模板中合并单元格的起始位置
     * </p>
     */
    private String mergeStart;

    /**
     * 合并单元格结束位置
     * <p>
     * 用于Excel模板中合并单元格的结束位置
     * </p>
     */
    private String mergeEnd;

    /**
     * 检验结论行位置
     */
    private Integer inspectionConclusionRow;

    /**
     * 检验结论列位置
     */
    private Integer inspectionConclusionColumn;

    /**
     * 检验员行位置
     */
    private Integer inspectorRow;

    /**
     * 检验员列位置
     */
    private Integer inspectorColumn;

    /**
     * 审核员行位置
     */
    private Integer auditorRow;

    /**
     * 审核员列位置
     */
    private Integer auditorColumn;

    /**
     * 批准员行位置
     */
    private Integer verifierRow;

    /**
     * 批准员列位置
     */
    private Integer verifierColumn;

    /**
     * 报告日期行位置
     */
    private Integer reportDateRow;

    /**
     * 报告日期列位置
     */
    private Integer reportDateColumn;

    /**
     * 客户自定义字段1的行位置
     * <p>
     * 用于在模板中定位客户自定义内容1的行位置
     * </p>
     */
    private Integer customerRow1;

    /**
     * 客户自定义字段1的列位置
     * <p>
     * 用于在模板中定位客户自定义内容1的列位置
     * </p>
     */
    private Integer customerColumn1;

    /**
     * 客户自定义字段1的内容
     * <p>
     * 用于存储客户自定义内容1的具体值
     * </p>
     */
    private String customerItem1;

    /**
     * 客户自定义字段2的行位置
     * <p>
     * 用于在模板中定位客户自定义内容2的行位置
     * </p>
     */
    private Integer customerRow2;

    /**
     * 客户自定义字段2的列位置
     * <p>
     * 用于在模板中定位客户自定义内容2的列位置
     * </p>
     */
    private Integer customerColumn2;

    /**
     * 客户自定义字段2的内容
     * <p>
     * 用于存储客户自定义内容2的具体值
     * </p>
     */
    private String customerItem2;

    /**
     * 客户自定义字段3的行位置
     * <p>
     * 用于在模板中定位客户自定义内容3的行位置
     * </p>
     */
    private Integer customerRow3;

    /**
     * 客户自定义字段3的列位置
     * <p>
     * 用于在模板中定位客户自定义内容3的列位置
     * </p>
     */
    private Integer customerColumn3;

    /**
     * 客户自定义字段3的内容
     * <p>
     * 用于存储客户自定义内容3的具体值
     * </p>
     */
    private String customerItem3;

    /**
     * 客户自定义字段4的行位置
     * <p>
     * 用于在模板中定位客户自定义内容4的行位置
     * </p>
     */
    private Integer customerRow4;

    /**
     * 客户自定义字段4的列位置
     * <p>
     * 用于在模板中定位客户自定义内容4的列位置
     * </p>
     */
    private Integer customerColumn4;

    /**
     * 客户自定义字段4的内容
     * <p>
     * 用于存储客户自定义内容4的具体值
     * </p>
     */
    private String customerItem4;

    /**
     * 客户自定义字段5的行位置
     * <p>
     * 用于在模板中定位客户自定义内容5的行位置
     * </p>
     */
    private Integer customerRow5;

    /**
     * 客户自定义字段5的列位置
     * <p>
     * 用于在模板中定位客户自定义内容5的列位置
     * </p>
     */
    private Integer customerColumn5;

    /**
     * 客户自定义字段5的内容
     * <p>
     * 用于存储客户自定义内容5的具体值
     * </p>
     */
    private String customerItem5;

    /**
     * 客户自定义字段6的行位置
     * <p>
     * 用于在模板中定位客户自定义内容6的行位置
     * </p>
     */
    private Integer customerRow6;

    /**
     * 客户自定义字段6的列位置
     * <p>
     * 用于在模板中定位客户自定义内容6的列位置
     * </p>
     */
    private Integer customerColumn6;

    /**
     * 客户自定义字段6的内容
     * <p>
     * 用于存储客户自定义内容6的具体值
     * </p>
     */
    private String customerItem6;

    /**
     * 客户自定义字段7的行位置
     * <p>
     * 用于在模板中定位客户自定义内容7的行位置
     * </p>
     */
    private Integer customerRow7;

    /**
     * 客户自定义字段7的列位置
     * <p>
     * 用于在模板中定位客户自定义内容7的列位置
     * </p>
     */
    private Integer customerColumn7;

    /**
     * 客户自定义字段7的内容
     * <p>
     * 用于存储客户自定义内容7的具体值
     * </p>
     */
    private String customerItem7;

    /**
     * 客户自定义字段8的行位置
     * <p>
     * 用于在模板中定位客户自定义内容8的行位置
     * </p>
     */
    private Integer customerRow8;

    /**
     * 客户自定义字段8的列位置
     * <p>
     * 用于在模板中定位客户自定义内容8的列位置
     * </p>
     */
    private Integer customerColumn8;

    /**
     * 客户自定义字段8的内容
     * <p>
     * 用于存储客户自定义内容8的具体值
     * </p>
     */
    private String customerItem8;

    /**
     * 检测项目排列方向
     * <p>
     * 1-横向排列，2-竖向排列
     * </p>
     */
    private Integer itemDirection;

    /**
     * 检测方法行位置
     */
    private Integer testMethodRow;

    /**
     * 检测方法列位置
     */
    private Integer testMethodColumn;
}
