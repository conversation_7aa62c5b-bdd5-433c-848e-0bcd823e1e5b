package com.haihang.service.outbound;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.haihang.model.DO.outbound.OutboundCustomerApproval;
import com.haihang.model.DTO.outbound.customerApproval.CustomerApprovalDTO;
import com.haihang.model.DTO.outbound.customerApproval.CustomerApprovalDetailDTO;
import com.haihang.model.Query.outbound.CustomerApprovalQuery;

/**
 * @Description: 客户审批Service接口
 * @Author: zad
 * @Create: 2024/12/20
 */
public interface CustomerApprovalService extends IService<OutboundCustomerApproval> {
    
    /**
     * 分页查询客户审批列表
     *
     * @param current 当前页
     * @param size 每页大小
     * @param query 查询条件
     * @return 分页结果
     */
    IPage<CustomerApprovalDTO> getCustomerApprovalPage(Integer current, Integer size, CustomerApprovalQuery query);
    
    /**
     * 审批客户申请
     *
     * @param id 审批记录ID
     * @param status 审批状态：1-通过，2-否决
     * @param approver 审批人
     * @return 是否成功
     */
    boolean approveCustomerApproval(Integer id, Integer status, String approver);
    
    /**
     * 获取客户审批详情
     *
     * @param id 审批记录ID
     * @return 审批详情
     */
    CustomerApprovalDTO getCustomerApprovalDetail(Integer id);
    
    /**
     * 获取客户审批详情，包含检测数据
     *
     * @param id 审批记录ID
     * @return 审批详情
     */
    CustomerApprovalDetailDTO getCustomerApprovalDetailWithInspectionData(Integer id);
} 