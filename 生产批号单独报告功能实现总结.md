# 生产批号单独报告功能实现总结

## 功能需求描述

在原有系统中，报告生成是根据模板类型（横向/纵向）来判断是否为每个生产批号生成单独的报告。现在需要添加一个新的配置字段来控制这个行为，并且设置优先级规则。

## 实现方案

### 1. 新增字段设计

在以下4个实体类中添加新字段 `isSeparateReportPerBatch`：

#### 1.1 Company.java
- 字段名：`isSeparateReportPerBatch`
- 类型：`Boolean`
- 功能：控制公司级别是否每个生产批号生成单独报告

#### 1.2 Group.java
- 字段名：`isSeparateReportPerBatch`
- 类型：`Boolean`
- 功能：控制集团级别是否每个生产批号生成单独报告

#### 1.3 TransportReportCompany.java
- 字段名：`isSeparateReportPerBatch`
- 类型：`Boolean`
- 功能：控制运输报告公司级别是否每个生产批号生成单独报告

#### 1.4 TransportReportGroup.java
- 字段名：`isSeparateReportPerBatch`
- 类型：`Boolean`
- 功能：控制运输报告集团级别是否每个生产批号生成单独报告

### 2. 优先级规则

设计的优先级规则如下（从高到低）：
1. **公司设置优先**：如果公司表中有设置，则使用公司设置
2. **集团设置次优**：如果公司表中没有设置，则使用集团设置
3. **默认行为最低**：如果集团也没有设置，则多个生产批号在同一report（默认为false）

### 3. 核心代码修改

#### 3.1 配置获取逻辑优化

在 `getCustomerReportConfig` 方法中：

```java
// 1. 添加新的默认配置
resultMap.put("isSeparateReportPerBatch", false); // 默认多个生产批号在同一report

// 2. 从集团获取配置后，再检查公司级别设置
if (group != null) {
    // 获取集团的配置
    resultMap.put("isSeparateReportPerBatch", Boolean.TRUE.equals(group.getIsSeparateReportPerBatch()));
}

// 检查公司级别是否有单独设置，公司设置优先级高于集团设置
if (company.getIsSeparateReportPerBatch() != null) {
    resultMap.put("isSeparateReportPerBatch", Boolean.TRUE.equals(company.getIsSeparateReportPerBatch()));
}
```

#### 3.2 报告生成逻辑修改

在 `creatTransportReportPicture` 方法中：

**原逻辑：**
```java
boolean isVerticalTemplate = transportReportTemplate.getItemDirection() != null && transportReportTemplate.getItemDirection() == 2;
boolean generateMultipleFiles = isVerticalTemplate && transportReportDataList.size() > 1;
```

**新逻辑：**
```java
// 判断是否为竖版模板
boolean isVerticalTemplate = transportReportTemplate.getItemDirection() != null && transportReportTemplate.getItemDirection() == 2;
// 竖版模板强制生成多个文件，或者根据配置字段判断是否生成多个文件
boolean generateMultipleFiles = (isVerticalTemplate || Boolean.TRUE.equals(configMap.get("isSeparateReportPerBatch"))) 
        && transportReportDataList.size() > 1;
```

#### 3.3 重量计算逻辑修复

在生成多个文件时，每个批次的重量计算逻辑：

**修复前：**
```java
// 错误：直接从rawInspectionData中获取
Object batchWeightObj = currentBatchData.getRawInspectionData().get("production_weight");
String batchWeight = (batchWeightObj != null) ? batchWeightObj.toString() : transportReport.getWeightInTons();
```

**修复后：**
```java
// 正确：根据袋数和规格计算，使用BigDecimal精确计算
String numberOfPackagesStr = currentBatchData.getNumberOfPackages();
String packagingSpec = transportReport.getPackagingSpecifications();

// 解析袋数
int numberOfPackages = Integer.parseInt(numberOfPackagesStr);
// 从规格字符串中提取数字（例如："25KG" -> 25）
String specNumStr = packagingSpec.replaceAll("[^0-9.]", "");
BigDecimal specWeight = new BigDecimal(specNumStr);
// 计算总重量（袋数 × 单袋重量，转换为吨）
BigDecimal totalWeight = BigDecimal.valueOf(numberOfPackages)
        .multiply(specWeight)
        .divide(BigDecimal.valueOf(1000), 3, RoundingMode.HALF_UP);
String batchWeight = totalWeight.stripTrailingZeros().toPlainString();
```

#### 3.4 精确计算改进

**改进点：**
1. **使用BigDecimal**：避免浮点数精度问题
2. **四舍五入策略**：使用`RoundingMode.HALF_UP`进行精确舍入
3. **去除末尾零**：使用`stripTrailingZeros()`自动去除无效的末尾零
4. **保留精度**：最多保留3位小数，但会自动去除无效零

**示例对比：**
- 原来：`2.500` → 显示为 `2.500`  
- 现在：`2.500` → 显示为 `2.5`

### 4. 生成逻辑规则

#### 4.1 多文件生成条件
系统会在以下情况下为每个生产批号生成单独的报告：
1. **竖版模板强制规则**：当模板为竖版时（`itemDirection == 2`），强制生成多个文件
2. **配置字段控制**：当 `isSeparateReportPerBatch = true` 时，生成多个文件
3. **数据条件**：必须有多个生产批号（`transportReportDataList.size() > 1`）

#### 4.2 逻辑优先级
```
生成多个文件 = (竖版模板 OR 配置启用) AND 数据量 > 1
```

### 5. 功能特点

#### 5.1 灵活性
- 支持公司级别和集团级别的独立配置
- 支持优先级覆盖机制
- 竖版模板保持强制多文件生成的业务逻辑

#### 5.2 向后兼容
- 竖版模板的原有行为完全保持不变
- 横版模板增加了可配置的控制选项
- 不影响现有的模板逻辑

#### 5.3 配置优先级
- 公司配置 > 集团配置 > 默认配置
- 模板类型约束优先于配置字段
- 确保了灵活的业务需求适配

### 6. 使用场景

#### 6.1 强制单独报告场景
当使用竖版模板时：
- 无论配置如何，都会为每个生产批号生成独立报告
- 保持竖版模板的原有业务逻辑

#### 6.2 可配置单独报告场景  
当 `isSeparateReportPerBatch = true` 且使用横版模板时：
- 每个生产批号生成独立的报告文件
- 文件命名会包含生产批号信息
- 便于按批次管理和追溯

#### 6.3 合并报告场景
当 `isSeparateReportPerBatch = false` 且使用横版模板时：
- 多个生产批号合并在一个报告中
- 减少文件数量，便于整体查看

### 7. 数据库变更需求

需要在以下表中添加 `is_separate_report_per_batch` 字段：
- `cmf_jt_xx` (Group表)
- `cmf_jt_xs_gs` (Company表)  
- `cmf_transport_report_company` (TransportReportCompany表)
- `cmf_transport_report_group` (TransportReportGroup表)

字段类型：`TINYINT(1)` 默认值：`NULL`

## 总结

本次功能实现成功地将报告生成逻辑从"基于模板类型判断"改为"基于配置字段判断"，提供了更加灵活的控制方式。通过优先级机制，确保了配置的层次化管理，满足了不同级别的业务需求。同时保持了向后兼容性，不会影响现有系统的正常运行。 