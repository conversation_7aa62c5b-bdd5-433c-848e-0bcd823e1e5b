---
description: 
globs: 
alwaysApply: false
---
# 项目结构指南

- 本仓库为 Maven 管理的 Java8 项目，根配置文件位于 [pom.xml](mdc:pom.xml)。
- **源代码** 位于 [src/main/java/com/haihang](mdc:src/main/java/com/haihang)。
  - 控制层在 [controller](mdc:src/main/java/com/haihang/controller) 下按业务模块分包组织，例如 *application*、*outbound* 等。
  - 业务服务层在 [service](mdc:src/main/java/com/haihang/service)，对应每个控制层模块有 `impl` 实现子包。
  - MyBatis 数据访问接口位于 [mapper](mdc:src/main/java/com/haihang/mapper)。对应的 XML 映射文件位于 [src/main/resources/mapper](mdc:src/main/resources/mapper)。
  - 持久化实体类在 [model/DO](mdc:src/main/java/com/haihang/model/DO)，数据传输对象在 [model/DTO](mdc:src/main/java/com/haihang/model/DTO)。
  - 通用工具类位于 [utils](mdc:src/main/java/com/haihang/utils)。
  - 全局异常及处理器位于 [exception](mdc:src/main/java/com/haihang/exception) 与 [handler](mdc:src/main/java/com/haihang/handler)。
- **静态资源** 存放于 [src/main/resources/static](mdc:src/main/resources/static)。
- **测试代码** 在 [src/test/java/com/haihang](mdc:src/test/java/com/haihang)。

> 使用此规则可帮助快速在大型包结构中定位代码位置，提高导航效率。

