package com.haihang.mapper.outbound;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haihang.model.DO.outbound.OutboundCustomerApproval;
import org.apache.ibatis.annotations.Mapper;

/**
 * @Description: 客户审批Mapper
 * @Author: zad
 * @Create: 2024/12/20
 */
@Mapper
public interface CustomerApprovalMapper extends BaseMapper<OutboundCustomerApproval> {
    
    // 使用MyBatis-Plus的条件构造器，不需要自定义SQL
    // 所有查询通过Service层的LambdaQueryWrapper来实现
} 