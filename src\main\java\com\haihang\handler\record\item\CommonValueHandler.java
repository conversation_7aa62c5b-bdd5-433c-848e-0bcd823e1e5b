package com.haihang.handler.record.item;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.haihang.factory.QualityInspectionHandlerFactory;
import com.haihang.factory.TargetJudgmentHandlerFactory;
import com.haihang.handler.judgment.TargetJudgmentHandler;
import com.haihang.handler.record.QualityInspectionHandler;
import com.haihang.model.DO.application.QualityInspectionApplication;
import com.haihang.model.DO.item.CommonValue;
import com.haihang.model.DO.record.BasicInformation;
import com.haihang.model.DO.record.BasicInformationNotSaved;
import com.haihang.model.DO.record.MergedUnSubmittedRecord;
import com.haihang.model.DO.requirementMaintenance.GeneralQualityStandard;
import com.haihang.model.DTO.outbound.deprecated.OutboundReportItemDTO;
import com.haihang.model.DTO.outbound.transferRecord.TransferRecordProductionAndQualityDataDTO;
import com.haihang.model.DTO.record.common.RecordDataDTO;
import com.haihang.model.DTO.record.common.StandingBookDataDTO;
import com.haihang.model.DTO.record.finished.SamplingDataDTO;
import com.haihang.model.DTO.record.finished.SamplingItemDTO;
import com.haihang.model.DTO.record.finished.SamplingResultDTO;
import com.haihang.model.DTO.record.semiFinished.SemiFinishedRecordDataDTO;
import com.haihang.model.DTO.report.ReportItemDTO;
import com.haihang.model.VO.data.SamplingDataVO;
import com.haihang.model.VO.report.RecordResultVO;
import com.haihang.model.VO.report.ReportVO;
import com.haihang.service.item.CommonValueService;
import com.haihang.utils.record.ItemCalculator;
import com.haihang.utils.record.RecordCreatUtil;
import lombok.RequiredArgsConstructor;
import org.jfree.data.category.DefaultCategoryDataset;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class CommonValueHandler extends QualityInspectionHandler {
    private final CommonValueService itemService;

    @Override
    public void itemRecordCreat(boolean samplingInspection, int samplingInspectionIndex,
            MergedUnSubmittedRecord mergedUnSubmittedRecord, BasicInformationNotSaved basicInformationNotSaved,
            String itemMatchingName, Map<String, Map<String, String>> generalMap,
            Map<String, Map<String, String>> groupMap, Map<String, Map<String, String>> internalControlMap) {
        CommonValue item = new CommonValue();

        item.setMergedUnSubmittedRecordId(mergedUnSubmittedRecord.getId());
        item.setUnSubmittedRecordId(basicInformationNotSaved.getId());
        item.setSamplingInspection(samplingInspection);
        item.setSamplingInspectionIndex(samplingInspectionIndex);

        Map<String, String> itemCreatMap = RecordCreatUtil.itemRecordCreat(itemMatchingName, generalMap, groupMap,
                internalControlMap);
        item.setMatchingName(itemCreatMap.get("itemMatchingName"));
        item.setName(itemCreatMap.get("name"));
        item.setGeneralRequirement(itemCreatMap.get("generalRequirement"));
        item.setGroupRequirement(itemCreatMap.get("groupRequirement"));
        item.setInternalControlRequirement(itemCreatMap.get("internalControlRequirement"));

        itemService.save(item);
    }

    @Override
    public void itemRecordCopy(int mergedRecordId, int recordId, int reInspectionMergedRecordId,
            int reInspectionRecordId, String itemMatchingName) {
        LambdaQueryWrapper<CommonValue> itemWrapper = new LambdaQueryWrapper<CommonValue>()
                .eq(CommonValue::getMergedUnSubmittedRecordId, mergedRecordId)
                .eq(CommonValue::getUnSubmittedRecordId, recordId)
                .eq(CommonValue::getMatchingName, itemMatchingName);
        List<CommonValue> list = itemService.list(itemWrapper);
        List<CommonValue> reInspectionList = new ArrayList<>();
        list.forEach(item -> {
            CommonValue reInspectionItem = BeanUtil.copyProperties(item, CommonValue.class, "id");
            reInspectionItem.setMergedUnSubmittedRecordId(reInspectionMergedRecordId);
            reInspectionItem.setUnSubmittedRecordId(reInspectionRecordId);
            reInspectionList.add(reInspectionItem);
        });
        itemService.saveBatch(reInspectionList);
    }

    @Override
    public void customerAdjustmentItemRecordCopy(QualityInspectionApplication qualityInspectionApplication,
            int mergedRecordId, int recordId, int reInspectionMergedRecordId, int reInspectionRecordId,
            String itemMatchingName, Map<String, Map<String, String>> generalMap,
            Map<String, Map<String, String>> groupMap, Map<String, Map<String, String>> internalControlMap) {
        LambdaQueryWrapper<CommonValue> itemWrapper = new LambdaQueryWrapper<CommonValue>()
                .eq(CommonValue::getMergedUnSubmittedRecordId, mergedRecordId)
                .eq(CommonValue::getUnSubmittedRecordId, recordId)
                .eq(CommonValue::getMatchingName, itemMatchingName);
        List<CommonValue> list = itemService.list(itemWrapper);
        List<CommonValue> reInspectionList = new ArrayList<>();
        list.forEach(item -> {
            CommonValue reInspectionItem = BeanUtil.copyProperties(item, CommonValue.class, "id");
            reInspectionItem.setMergedUnSubmittedRecordId(reInspectionMergedRecordId);
            reInspectionItem.setUnSubmittedRecordId(reInspectionRecordId);

            Map<String, String> itemCreatMap = RecordCreatUtil.itemRecordCreat(itemMatchingName, generalMap, groupMap,
                    internalControlMap);
            reInspectionItem.setMatchingName(itemCreatMap.get("itemMatchingName"));
            reInspectionItem.setName(itemCreatMap.get("name"));
            reInspectionItem.setGeneralRequirement(itemCreatMap.get("generalRequirement"));
            reInspectionItem.setGroupRequirement(itemCreatMap.get("groupRequirement"));
            reInspectionItem.setInternalControlRequirement(itemCreatMap.get("internalControlRequirement"));

            if (ObjectUtil.isNotNull(reInspectionItem.getValue())) {
                Integer general = ItemCalculator.isQualified(reInspectionItem.getGeneralRequirement(),
                        reInspectionItem.getValue());
                Integer group;
                if (reInspectionItem.getInternalControlRequirement().contains("暂无")) {
                    group = ItemCalculator.isQualified(reInspectionItem.getGroupRequirement(),
                            reInspectionItem.getValue());
                } else {
                    group = ItemCalculator.isQualified(reInspectionItem.getInternalControlRequirement(),
                            reInspectionItem.getValue());
                }

                reInspectionItem.setResult(general);
                reInspectionItem.setGroupResult(group);
                reInspectionItem.setFinalResult(ItemCalculator.generateFinalResult(general, group,
                        qualityInspectionApplication.getGroupRequirement() != null
                                || qualityInspectionApplication.getInternalControlRequirement() != null));
            }

            reInspectionList.add(reInspectionItem);
        });

        // 来源质检记录未查询到该检测项目
        if (CollUtil.isEmpty(list)) {
            // 查询相同处理id的非抽检记录
            itemWrapper = new LambdaQueryWrapper<CommonValue>()
                    .eq(CommonValue::getMergedUnSubmittedRecordId, mergedRecordId)
                    .eq(CommonValue::getMatchingName, itemMatchingName)
                    .eq(CommonValue::getSamplingInspection, false);
            list = itemService.list(itemWrapper);

            if (CollUtil.isNotEmpty(list)) {
                // 复制最后一个检测项目至目标质检记录
                CommonValue reInspectionItem = BeanUtil.copyProperties(list.get(list.size() - 1), CommonValue.class,
                        "id");
                reInspectionItem.setMergedUnSubmittedRecordId(reInspectionMergedRecordId);
                reInspectionItem.setUnSubmittedRecordId(reInspectionRecordId);

                Map<String, String> itemCreatMap = RecordCreatUtil.itemRecordCreat(itemMatchingName, generalMap,
                        groupMap, internalControlMap);
                reInspectionItem.setMatchingName(itemCreatMap.get("itemMatchingName"));
                reInspectionItem.setName(itemCreatMap.get("name"));
                reInspectionItem.setGeneralRequirement(itemCreatMap.get("generalRequirement"));
                reInspectionItem.setGroupRequirement(itemCreatMap.get("groupRequirement"));
                reInspectionItem.setInternalControlRequirement(itemCreatMap.get("internalControlRequirement"));

                if (ObjectUtil.isNotNull(reInspectionItem.getValue())) {

                    Integer general = ItemCalculator.isQualified(reInspectionItem.getGeneralRequirement(),
                            reInspectionItem.getValue());
                    Integer group;
                    if (reInspectionItem.getInternalControlRequirement().contains("暂无")) {
                        group = ItemCalculator.isQualified(reInspectionItem.getGroupRequirement(),
                                reInspectionItem.getValue());
                    } else {
                        group = ItemCalculator.isQualified(reInspectionItem.getInternalControlRequirement(),
                                reInspectionItem.getValue());
                    }

                    reInspectionItem.setResult(general);
                    reInspectionItem.setGroupResult(group);
                    reInspectionItem.setFinalResult(ItemCalculator.generateFinalResult(general, group,
                            qualityInspectionApplication.getGroupRequirement() != null
                                    || qualityInspectionApplication.getInternalControlRequirement() != null));
                }

                reInspectionList.add(reInspectionItem);
            } else {
                // 新增检测项目至目标质检记录
                CommonValue reInspectionItem = new CommonValue();
                reInspectionItem.setMergedUnSubmittedRecordId(reInspectionMergedRecordId);
                reInspectionItem.setUnSubmittedRecordId(reInspectionRecordId);

                Map<String, String> itemCreatMap = RecordCreatUtil.itemRecordCreat(itemMatchingName, generalMap,
                        groupMap, internalControlMap);
                reInspectionItem.setMatchingName(itemCreatMap.get("itemMatchingName"));
                reInspectionItem.setName(itemCreatMap.get("name"));
                reInspectionItem.setGeneralRequirement(itemCreatMap.get("generalRequirement"));
                reInspectionItem.setGroupRequirement(itemCreatMap.get("groupRequirement"));
                reInspectionItem.setInternalControlRequirement(itemCreatMap.get("internalControlRequirement"));

                reInspectionList.add(reInspectionItem);
            }
        }

        itemService.saveBatch(reInspectionList);
    }

    @Override
    public void itemSamplingRecordRemove(MergedUnSubmittedRecord mergedUnSubmittedRecord,
            BasicInformationNotSaved basicInformationNotSaved, String matchingName) {
        LambdaQueryWrapper<CommonValue> itemWrapper = new LambdaQueryWrapper<CommonValue>()
                .eq(CommonValue::getMergedUnSubmittedRecordId, mergedUnSubmittedRecord.getId())
                .eq(CommonValue::getUnSubmittedRecordId, basicInformationNotSaved.getId())
                .eq(CommonValue::getSamplingInspection, true)
                .eq(CommonValue::getMatchingName, matchingName);
        itemService.remove(itemWrapper);
    }

    @Override
    public void samplingItemRecordCreat(SamplingItemDTO samplingItemDTO, Map<String, String> generalMap,
            Map<String, String> groupMap, Map<String, String> internalControlMap, StringBuilder stringBuilder) {
        stringBuilder.append(";").append(samplingItemDTO.getItem()).append(":");
        for (int i = 0; i < samplingItemDTO.getNumber(); i++) {
            CommonValue commonValue = new CommonValue();
            String[] split = samplingItemDTO.getItem().split(",");
            if (split.length > 1) {
                commonValue.setName(split[0] + "(" + split[1] + ")");
            } else {
                commonValue.setName(samplingItemDTO.getItem());
            }
            String generalRequirement = generalMap.get(samplingItemDTO.getItem());
            if (generalRequirement != null) {
                commonValue.setGeneralRequirement(generalRequirement);
            } else {
                commonValue.setGeneralRequirement("暂无通用指标");
            }
            commonValue.setGroupRequirement("暂无客户指标");
            if (groupMap != null) {
                String groupRequirement = groupMap.get(samplingItemDTO.getItem());
                if (groupRequirement != null) {
                    commonValue.setGroupRequirement(groupRequirement);
                } else {
                    commonValue.setGroupRequirement("暂无客户指标");
                }
            }
            commonValue.setInternalControlRequirement("暂无内控指标");
            if (internalControlMap != null) {
                String internalControlRequirement = internalControlMap.get(samplingItemDTO.getItem());
                if (internalControlRequirement != null) {
                    commonValue.setInternalControlRequirement(internalControlRequirement);
                } else {
                    commonValue.setInternalControlRequirement("暂无内控指标");
                }
            }
            itemService.save(commonValue);
            stringBuilder.append(commonValue.getId()).append(",");
        }
        stringBuilder.deleteCharAt(stringBuilder.length() - 1);
    }

    @Override
    public void recordDataFillIn(int mergedUnSubmittedRecordId, RecordDataDTO recordData) {
        List<CommonValue> itemList = recordData.getCommonValue();
        if (CollUtil.isEmpty(itemList)) {
            LambdaQueryWrapper<CommonValue> lambdaQueryWrapper = new LambdaQueryWrapper<CommonValue>()
                    .eq(CommonValue::getMergedUnSubmittedRecordId, mergedUnSubmittedRecordId)
                    .eq(CommonValue::getSamplingInspection, false);
            itemList = itemService.list(lambdaQueryWrapper);
        }
        List<CommonValue> distinctList = itemList.stream()
                .distinct()
                .collect(Collectors.toList());
        distinctList.forEach(item -> {
            if (item.getInternalControlRequirement().contains("暂无")) {
                item.setGroupRequirementView(item.getGroupRequirement());
            } else {
                item.setGroupRequirementView(item.getInternalControlRequirement());
            }
        });
        distinctList.sort(Comparator.comparing(CommonValue::getMatchingName));
        recordData.setCommonValue(distinctList);
    }

    @Override
    public void reportItemFillIn(int recordId, String itemMatchingName, ReportVO reportVO) {
        LambdaQueryWrapper<CommonValue> lambdaQueryWrapper = new LambdaQueryWrapper<CommonValue>()
                .eq(CommonValue::getUnSubmittedRecordId, recordId)
                .eq(CommonValue::getMatchingName, itemMatchingName);
        List<CommonValue> itemList = itemService.list(lambdaQueryWrapper);
        itemList.sort(Comparator.comparing(CommonValue::getSamplingInspectionIndex));

        List<RecordResultVO> recordResultList = reportVO.getRecordResultList();
        List<RecordResultVO> samplingResultList = reportVO.getSamplingResultList();

        itemList.forEach(item -> {
            RecordResultVO recordResultVO = new RecordResultVO();
            recordResultVO.setId(item.getId());
            recordResultVO.setItem(item.getName());
            recordResultVO.setMatchingName(item.getMatchingName());
            recordResultVO.setGeneralRequirement(item.getGeneralRequirement());
            if ("暂无内控指标".equals(item.getInternalControlRequirement())) {
                recordResultVO.setGroupRequirement(item.getGroupRequirement());
            } else {
                recordResultVO.setGroupRequirement(item.getInternalControlRequirement());
            }
            if (item.getValue() == null) {
                recordResultVO.setData("暂无数据");
            } else {
                recordResultVO.setData(String.format("%.2f", item.getValue()));
            }
            recordResultVO.setResult(item.getResult());
            recordResultVO.setGroupResult(item.getGroupResult());
            recordResultVO.setFinalResult(item.getFinalResult());
            recordResultVO.setInspector(item.getInspector());

            if (item.getSamplingInspection()) {
                samplingResultList.add(recordResultVO);
            } else {
                recordResultList.add(recordResultVO);
            }
        });

        reportVO.setRecordResultList(recordResultList);
        reportVO.setSamplingResultList(samplingResultList);
        /*
         * List<CommonValue> commonValueList = recordDataVO.getCommonValue();
         * commonValueList.forEach(commonValue -> {
         * if (name.equals(commonValue.getName())) {
         * RecordResultDTO recordResultDTO = new RecordResultDTO();
         * recordResultDTO.setId(commonValue.getId());
         * recordResultDTO.setItem(commonValue.getName());
         * recordResultDTO.setGeneralRequirement(commonValue.getGeneralRequirement());
         * if ("暂无内控指标".equals(commonValue.getInternalControlRequirement())) {
         * recordResultDTO.setGroupRequirement(commonValue.getGroupRequirement());
         * } else {
         * recordResultDTO.setGroupRequirement(commonValue.getInternalControlRequirement
         * ());
         * }
         * if (commonValue.getValue() == null) {
         * recordResultDTO.setData("暂无数据");
         * } else {
         * recordResultDTO.setData(String.format("%.2f", commonValue.getValue()));
         * }
         * recordResultDTO.setResult(commonValue.getResult());
         * recordResultDTO.setGroupResult(commonValue.getGroupResult());
         * recordResultDTO.setFinalResult(commonValue.getFinalResult());
         * recordResultDTO.setInspector(commonValue.getInspector());
         * recordResultDTOList.add(recordResultDTO);
         * }
         * });
         */
    }

    @Override
    public void semiFinishedReportItemFillIn(List<ReportItemDTO> reportItemDTOList,
            SemiFinishedRecordDataDTO semiFinishedRecordDataDTO, String name) {
        List<CommonValue> commonValueList = semiFinishedRecordDataDTO.getRecordData().getCommonValue();
        commonValueList.forEach(commonValue -> {
            if (name.equals(commonValue.getName())) {
                ReportItemDTO reportItemDTO = new ReportItemDTO();
                reportItemDTO.setId(commonValue.getId());
                reportItemDTO.setUnSubmittedRecordId(semiFinishedRecordDataDTO.getBasicInformationNotSaved().getId());
                reportItemDTO.setQualityInspectionBatch(
                        semiFinishedRecordDataDTO.getBasicInformationNotSaved().getQualityInspectionBatch());
                reportItemDTO.setTankBatch(semiFinishedRecordDataDTO.getTankBatch());
                reportItemDTO.setItem(commonValue.getName());
                reportItemDTO.setGeneralRequirement(commonValue.getGeneralRequirement());
                if ("暂无内控指标".equals(commonValue.getInternalControlRequirement())) {
                    reportItemDTO.setGroupRequirement(commonValue.getGroupRequirement());
                } else {
                    reportItemDTO.setGroupRequirement(commonValue.getInternalControlRequirement());
                }
                if (commonValue.getValue() == null) {
                    reportItemDTO.setData("暂无数据");
                } else {
                    reportItemDTO.setData(String.format("%.2f", commonValue.getValue()));
                }
                reportItemDTO.setResult(commonValue.getResult());
                reportItemDTO.setGroupResult(commonValue.getGroupResult());
                reportItemDTO.setFinalResult(commonValue.getFinalResult());
                reportItemDTO.setInspector(commonValue.getInspector());
                reportItemDTOList.add(reportItemDTO);
            }
        });
    }

    @Override
    public void getRecordResult(int recordId, AtomicInteger generalResult, AtomicInteger groupResult,
            AtomicInteger finalResult) {
        LambdaQueryWrapper<CommonValue> lambdaQueryWrapper = new LambdaQueryWrapper<CommonValue>()
                .eq(CommonValue::getUnSubmittedRecordId, recordId);
        List<CommonValue> commonValueList = itemService.list(lambdaQueryWrapper);

        for (CommonValue commonValue : commonValueList) {
            itemService.updateById(commonValue);
            if (commonValue.getResult() != null) {
                if (commonValue.getResult() == 0) {
                    generalResult.set(0);
                }
            }
            if (commonValue.getGroupResult() != null) {
                if (commonValue.getGroupResult() == 0) {
                    groupResult.set(0);
                }
            }
            if (commonValue.getFinalResult() != null) {
                if (finalResult.get() == 1) {
                    finalResult.set(commonValue.getFinalResult());
                }
                if (finalResult.get() == 2 && commonValue.getFinalResult() == 0) {
                    finalResult.set(0);
                }
            }
        }
    }

    @Override
    public void nameConversion(int id, Map<String, String> map) {
        CommonValue byId = itemService.getById(id);
        Float average = byId.getValue();
        String format = String.format("%.2f", average);
        map.put("杂质", format);
    }

    @Override
    public String createChart(List<BasicInformation> basicInformationList,
            List<GeneralQualityStandard> generalQualityStandardList, DefaultCategoryDataset dataSet) {
        final int[] count = { 1 };
        final double[] acidityCount = { 0 };
        basicInformationList.forEach(basicInformation -> {
            String[] split = basicInformation.getRecords().split(",");
            Map<String, String> map = new HashMap<>();
            Arrays.asList(split).forEach(record -> {
                String[] recordArray = record.split(":");
                map.put(recordArray[0], recordArray[1]);
            });
            if (map.get("commonValue") != null) {
                String acidityId = map.get("commonValue");
                CommonValue byId = itemService.getById(acidityId);
                double value = new BigDecimal(String.valueOf(byId.getValue())).doubleValue();
                dataSet.setValue(value, "杂质", String.valueOf(count[0]));
                count[0]++;
                acidityCount[0] += value;
            }
        });
        count[0] = 1;
        basicInformationList.forEach(basicInformation -> {
            dataSet.setValue(acidityCount[0] / basicInformationList.size(), "CL", String.valueOf(count[0]));
            count[0]++;
        });
        count[0] = 1;
        basicInformationList.forEach(basicInformation -> {
            Map<String, String> standardMap = new HashMap<>();
            generalQualityStandardList.forEach(standard -> standardMap.put(
                    standard.getQualityInspectionItem().replaceAll("[^a-zA-Z\\d\\u4E00-\\u9FA5]", ""),
                    standard.getQualityRequirement()));
            String requirement = standardMap.get("杂质");
            TargetJudgmentHandler targetJudgmentHandler = TargetJudgmentHandlerFactory.getInvokeStrategy(requirement);
            if (targetJudgmentHandler != null) {
                if (requirement.contains("≥") && !requirement.contains("≤")) {
                    Float requirementNumber = targetJudgmentHandler.singleTargetJudgment(requirement);
                    double value = new BigDecimal(String.valueOf(requirementNumber)).doubleValue();
                    dataSet.setValue(value, "LCL", String.valueOf(count[0]));
                } else if (requirement.contains("≤") && !requirement.contains("≥")) {
                    Float requirementNumber = targetJudgmentHandler.singleTargetJudgment(requirement);
                    double value = new BigDecimal(String.valueOf(requirementNumber)).doubleValue();
                    dataSet.setValue(value, "UCL", String.valueOf(count[0]));
                } else {
                    Float[] requirementNumber = targetJudgmentHandler.doubleTargetJudgment(requirement);
                    double value1 = new BigDecimal(String.valueOf(requirementNumber[0])).doubleValue();
                    double value2 = new BigDecimal(String.valueOf(requirementNumber[1])).doubleValue();
                    dataSet.setValue(value1, "LCL", String.valueOf(count[0]));
                    dataSet.setValue(value2, "UCL", String.valueOf(count[0]));
                }
                count[0]++;
            }
        });
        return "杂质";
    }

    @Override
    public void samplingItemFillIn(int mergedUnSubmittedRecordId, String itemName, String requirement,
            String itemMatchingName, List<SamplingItemDTO> samplingItemList) {
        LambdaQueryWrapper<CommonValue> lambdaQueryWrapper = new LambdaQueryWrapper<CommonValue>()
                .eq(CommonValue::getMergedUnSubmittedRecordId, mergedUnSubmittedRecordId)
                .eq(CommonValue::getName, itemName)
                .eq(CommonValue::getMatchingName, itemMatchingName)
                .eq(CommonValue::getSamplingInspection, true);
        List<CommonValue> list = CollUtil.distinct(itemService.list(lambdaQueryWrapper),
                CommonValue::getSamplingInspectionIndex, false);
        if (CollUtil.isNotEmpty(list)) {
            SamplingItemDTO samplingItemDTO = new SamplingItemDTO();
            Map<String, String> itemMap = new HashMap<>();
            itemMap.put("itemName", itemName);
            itemMap.put("requirement", requirement);
            itemMap.put("matchingName", itemMatchingName);
            itemMap.remove("itemName");
            samplingItemDTO.setItem(itemMap.toString());
            samplingItemDTO.setNumber(list.size());
            samplingItemList.add(samplingItemDTO);
        }
    }

    @Override
    public void samplingDataFillIn(int mergedUnSubmittedRecordId, SamplingDataDTO samplingData) {
        List<CommonValue> resultList = new ArrayList<>();
        List<CommonValue> itemList = samplingData.getCommonValue();
        if (CollUtil.isEmpty(itemList)) {
            LambdaQueryWrapper<CommonValue> lambdaQueryWrapper = new LambdaQueryWrapper<CommonValue>()
                    .eq(CommonValue::getMergedUnSubmittedRecordId, mergedUnSubmittedRecordId)
                    .eq(CommonValue::getSamplingInspection, true);
            itemList = itemService.list(lambdaQueryWrapper);
        }
        if (CollUtil.isNotEmpty(itemList)) {
            List<CommonValue> nameDistinctList = itemList.stream()
                    .distinct()
                    .collect(Collectors.toList());
            nameDistinctList.forEach(item -> {
                LambdaQueryWrapper<CommonValue> lambdaQueryWrapper = new LambdaQueryWrapper<CommonValue>()
                        .eq(CommonValue::getMergedUnSubmittedRecordId, mergedUnSubmittedRecordId)
                        .eq(CommonValue::getSamplingInspection, true)
                        .eq(CommonValue::getMatchingName, item.getMatchingName());
                List<CommonValue> indexList = itemService.list(lambdaQueryWrapper);
                resultList.addAll(CollUtil.distinct(indexList, CommonValue::getSamplingInspectionIndex, false));
            });
            resultList.forEach(item -> {
                if (item.getInternalControlRequirement().contains("暂无")) {
                    item.setGroupRequirementView(item.getGroupRequirement());
                } else {
                    item.setGroupRequirementView(item.getInternalControlRequirement());
                }
            });
            resultList.sort(Comparator.comparing(CommonValue::getMatchingName));
            samplingData.setCommonValue(resultList);
        }
    }

    @Override
    public void getSamplingResult(SamplingDataDTO samplingDataDTO, AtomicInteger generalResult,
            AtomicInteger groupResult) {
        List<CommonValue> commonValueList = samplingDataDTO.getCommonValue();
        commonValueList.forEach(commonValue -> {
            itemService.updateById(commonValue);
            if (commonValue.getResult() != null) {
                if (commonValue.getResult() == 0) {
                    generalResult.set(0);
                }
            }
            if (commonValue.getGroupResult() != null) {
                if (commonValue.getGroupResult() == 0) {
                    groupResult.set(0);
                }

            }
        });
    }

    @Override
    public void reportSamplingItemFillIn(List<SamplingResultDTO> samplingResultDTOList, SamplingDataVO samplingDataVO,
            String name) {
        List<CommonValue> commonValueList = samplingDataVO.getCommonValue();
        commonValueList.forEach(commonValue -> {
            if (name.equals(commonValue.getName())) {
                SamplingResultDTO samplingResultDTO = new SamplingResultDTO();
                samplingResultDTO.setId(commonValue.getId());
                samplingResultDTO.setItem(commonValue.getName());
                samplingResultDTO.setGeneralRequirement(commonValue.getGeneralRequirement());
                if ("暂无内控指标".equals(commonValue.getInternalControlRequirement())) {
                    samplingResultDTO.setGroupRequirement(commonValue.getGroupRequirement());
                } else {
                    samplingResultDTO.setGroupRequirement(commonValue.getInternalControlRequirement());
                }
                if (commonValue.getValue() == null) {
                    samplingResultDTO.setData("暂无数据");
                } else {
                    samplingResultDTO.setData(String.format("%.2f", commonValue.getValue()));
                }
                samplingResultDTO.setResult(commonValue.getResult());
                samplingResultDTO.setGroupResult(commonValue.getGroupResult());
                samplingResultDTO.setFinalResult(commonValue.getFinalResult());
                samplingResultDTO.setInspector(commonValue.getInspector());
                samplingResultDTOList.add(samplingResultDTO);
            }

        });
    }

    @Override
    public void standingBookDataFillIn(StandingBookDataDTO standingBookDataDTO, Map<String, Object> dataMap,
            StringBuilder samplingInspectionBuilder, Set<String> itemSet, String itemMatchingName) {
        List<CommonValue> itemList = standingBookDataDTO.getCommonValue();
        itemList.forEach(item -> {
            String matchingName = item.getMatchingName();
            String name;
            if (matchingName.split(",").length > 1) {
                name = matchingName.split(",")[0] + "(" + matchingName.split(",")[1] + ")";
            } else {
                name = matchingName;
            }
            itemSet.add(name);
            if (!item.getSamplingInspection()) {
                if (ObjectUtil.isNotNull(item.getValue())) {
                    dataMap.put(name, String.format("%.2f", item.getValue()));
                } else {
                    dataMap.put(name, "暂无数据");
                }
            } else if (StrUtil.equals(itemMatchingName, matchingName)) {
                samplingInspectionBuilder
                        .append(";")
                        .append(name)
                        .append(":")
                        .append(String.format("%.2f", item.getValue()));
            }
        });
    }

    @Override
    public void standingBookColumnFillIn(String item, List<Map<String, Object>> columnMapList) {
        Map<String, Object> columnMap = new HashMap<>();
        if (item.split(",").length > 1) {
            columnMap.put("label", item.split(",")[0] + "(" + item.split(",")[1] + ")");
        } else {
            columnMap.put("label", item);
        }
        columnMapList.add(columnMap);
    }

    @Override
    public void itemDataFillIn(int id, List<Map<String, Object>> list) {
        CommonValue byId = itemService.getById(id);
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("label", byId.getMatchingName());
        dataMap.put("value", String.format("%.2f", byId.getValue()));
        list.add(dataMap);
        dataMap = new HashMap<>();
        dataMap.put("label", "检测人");
        dataMap.put("value", byId.getInspector());
        list.add(dataMap);
    }

    @Override
    public void recordCopy(String recordId, String itemName, StringBuilder reInspectionRecord) {
        reInspectionRecord.append(";").append(itemName).append(":");
        CommonValue byId = itemService.getById(recordId);
        CommonValue commonValue = BeanUtil.copyProperties(byId, CommonValue.class);
        commonValue.setId(null);
        itemService.save(commonValue);
        reInspectionRecord.append(commonValue.getId());
    }

    @Override
    public void samplingCopy(String[] samplingIds, String itemName, StringBuilder reInspectionRecord) {
        reInspectionRecord.append(";").append(itemName).append(":");
        for (String samplingId : samplingIds) {
            CommonValue byId = itemService.getById(samplingId);
            CommonValue commonValue = BeanUtil.copyProperties(byId, CommonValue.class);
            commonValue.setId(null);
            itemService.save(commonValue);
            reInspectionRecord.append(commonValue.getId()).append(",");
        }
        reInspectionRecord.deleteCharAt(reInspectionRecord.length() - 1);
    }

    @Override
    public void getOutboundReportItem(OutboundReportItemDTO outboundReportItemDTO, String itemRequirement,
            boolean isGroup, boolean isPreDispersedCustomer, int itemRecordId) {
        CommonValue byId = itemService.getById(itemRecordId);
        outboundReportItemDTO.setItemName(byId.getName());
        outboundReportItemDTO.setRequirement(itemRequirement);
        outboundReportItemDTO.setData(String.format("%.2f", byId.getValue()));
    }

    @Override
    public void selectCollection(MPJLambdaWrapper<BasicInformation> wrapper) {
        wrapper.selectCollection(CommonValue.class, StandingBookDataDTO::getCommonValue);
    }

    @Override
    public void leftJoin(MPJLambdaWrapper<BasicInformation> wrapper) {
        wrapper.leftJoin(CommonValue.class, CommonValue::getUnSubmittedRecordId,
                BasicInformation::getNotSubmittedRecordId);
    }

    @Override
    public void outboundItemDataFillIn(List<Integer> notSubmittedRecordIdList,
            List<TransferRecordProductionAndQualityDataDTO> productionAndQualityDataList) {
        // 查询所有相关的通用值数据
        List<CommonValue> list = itemService.list(new LambdaQueryWrapper<CommonValue>()
                .in(CommonValue::getUnSubmittedRecordId, notSubmittedRecordIdList));

        // 遍历每条生产和质量数据记录
        productionAndQualityDataList.forEach(outboundInformationVO -> {
            outboundInformationVO.getQualityInspectionData().forEach(inspectionDataMap -> {
                Integer notSubmittedRecordId = (Integer) inspectionDataMap.get("notSubmittedRecordId");

                // 过滤出当前记录相关的通用值数据
                List<CommonValue> filteredItems = list.stream()
                        .filter(a -> a.getUnSubmittedRecordId().equals(notSubmittedRecordId))
                        .collect(Collectors.toList());

                // 处理非抽检数据
                filteredItems.stream()
                        .filter(item -> !item.getSamplingInspection())
                        .forEach(item -> inspectionDataMap.put(item.getMatchingName(),
                                formatValue(item.getValue())));

                // 处理抽样数据
                @SuppressWarnings("unchecked")
                final List<Map<String, String>> samplingData = (List<Map<String, String>>) inspectionDataMap
                        .computeIfAbsent("samplingData", k -> new ArrayList<>());

                // 添加抽检数据
                filteredItems.stream()
                        .filter(CommonValue::getSamplingInspection)
                        .forEach(item -> {
                            Map<String, String> samplingEntry = new HashMap<>();
                            samplingEntry.put("matchingName", item.getMatchingName());
                            samplingEntry.put("value", formatValue(item.getValue()));
                            samplingData.add(samplingEntry);
                        });
            });
        });
    }

    // 格式化值的辅助方法
    private String formatValue(Float value) {
        return ObjUtil.isNull(value) ? "暂无数据"
                : NumberUtil.round(NumberUtil.toBigDecimal(value), 2, RoundingMode.HALF_EVEN).toPlainString();
    }

    @Override
    public void recordDownloadInspectionDataFillIn(Map<Integer, Object> inspectionDataMap,
            List<Integer> unSubmiitedRecordIdList, String itemMatchingName) {
        List<CommonValue> list = itemService.list(new LambdaQueryWrapper<CommonValue>()
                .in(CommonValue::getUnSubmittedRecordId, unSubmiitedRecordIdList)
                .eq(CommonValue::getMatchingName, itemMatchingName)
                .orderByAsc(CommonValue::getSamplingInspection, CommonValue::getSamplingInspectionIndex));

        list.forEach(item -> {
            // 从检验数据映射中获取当前记录的检验数据
            @SuppressWarnings("unchecked")
            Map<String, Object> recordInspectionData = (Map<String, Object>) inspectionDataMap
                    .get(item.getUnSubmittedRecordId());

            // 如果记录检验数据为空,则初始化一个新的HashMap
            if (ObjUtil.isNull(recordInspectionData)) {
                recordInspectionData = new HashMap<>();
                inspectionDataMap.put(item.getUnSubmittedRecordId(), recordInspectionData);
            }

            // 获取检验数据列表(用于存储处理后的检验数据)
            @SuppressWarnings("unchecked")
            List<List<String>> inspectionDataList = (List<List<String>>) recordInspectionData.get("inspectionDataList");

            // 如果检验数据列表为空,则初始化一个新的ArrayList
            if (ObjUtil.isNull(inspectionDataList)) {
                inspectionDataList = new ArrayList<>();
                recordInspectionData.put("inspectionDataList", inspectionDataList);
            }

            // 获取抽样数据列表(用于存储处理后的抽样检验数据)
            @SuppressWarnings("unchecked")
            List<List<String>> samplingDataList = (List<List<String>>) recordInspectionData
                    .get("samplingDataList");

            // 如果抽样数据列表为空,则初始化一个新的ArrayList
            if (ObjUtil.isNull(samplingDataList)) {
                samplingDataList = new ArrayList<>();
                recordInspectionData.put("samplingDataList", samplingDataList);
            }

            // 获取原始检验数据列表(用于存储未经处理的检验原始数据)
            @SuppressWarnings("unchecked")
            List<List<List<String>>> rawInspectionDataList = (List<List<List<String>>>) recordInspectionData
                    .get("rawInspectionDataList");

            // 如果原始检验数据列表为空,则初始化一个新的ArrayList
            if (ObjUtil.isNull(rawInspectionDataList)) {
                rawInspectionDataList = new ArrayList<>();
                recordInspectionData.put("rawInspectionDataList", rawInspectionDataList);
            }

            // 获取原始抽样数据列表(用于存储未经处理的抽样原始数据)
            @SuppressWarnings("unchecked")
            List<List<List<String>>> rawSamplingDataList = (List<List<List<String>>>) recordInspectionData
                    .get("rawSamplingDataList");

            // 如果原始抽样数据列表为空,则初始化一个新的ArrayList
            if (ObjUtil.isNull(rawSamplingDataList)) {
                rawSamplingDataList = new ArrayList<>();
                recordInspectionData.put("rawSamplingDataList", rawSamplingDataList);
            }

            Map<String, Object> itemDataMap = new HashMap<>();

            List<String> inspectionData = new ArrayList<>();
            inspectionData.add(item.getMatchingName());
            inspectionData.add(item.getGeneralRequirement());
            if (StrUtil.contains(item.getInternalControlRequirement(), "暂无")) {
                inspectionData.add(item.getGroupRequirement());
            } else {
                inspectionData.add(item.getInternalControlRequirement());
            }
            inspectionData.add(ObjUtil.isNull(item.getValue()) ? "暂无数据"
                    : NumberUtil.round(NumberUtil.toBigDecimal(item.getValue()), 2, RoundingMode.HALF_EVEN)
                            .toPlainString());
            if (ObjUtil.isNotNull(item.getFinalResult())) {
                switch (item.getFinalResult()) {
                    case 0:
                        inspectionData.add("不合格");
                        break;
                    case 2:
                        inspectionData.add("不符合");
                        break;
                    default:
                        inspectionData.add("合格");
                        break;
                }
            } else {
                inspectionData.add("/");
            }
            inspectionData.add(StrUtil.isBlank(item.getInspector()) ? "/" : item.getInspector());
            itemDataMap.put("inspectionData", inspectionData);

            List<List<String>> rawInspectionData = new ArrayList<>();
            List<String> row1 = new ArrayList<>();
            row1.add(item.getMatchingName());
            row1.add(null);
            row1.add(ObjUtil.isNull(item.getValue()) ? "暂无数据"
                    : NumberUtil.round(NumberUtil.toBigDecimal(item.getValue()), 2, RoundingMode.HALF_EVEN)
                            .toPlainString());
            row1.add(null);
            row1.add(null);
            row1.add(item.getInspector());
            rawInspectionData.add(row1);

            if (item.getSamplingInspection()) {
                samplingDataList.add(inspectionData);
                rawSamplingDataList.add(rawInspectionData);
            } else {
                inspectionDataList.add(inspectionData);
                rawInspectionDataList.add(rawInspectionData);
            }
        });
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        QualityInspectionHandlerFactory.register("甲苯不溶物", this);
        QualityInspectionHandlerFactory.register("伯胺", this);
        QualityInspectionHandlerFactory.register("水溶性盐含量", this);
        QualityInspectionHandlerFactory.register("Fe", this);
        QualityInspectionHandlerFactory.register("铅含量", this);
        QualityInspectionHandlerFactory.register("硫含量(预分散体)", this);
        QualityInspectionHandlerFactory.register("DSC熔点", this);
        QualityInspectionHandlerFactory.register("DSC热稳定性", this);
        QualityInspectionHandlerFactory.register("锌含量", this);
        QualityInspectionHandlerFactory.register("有效含量", this);
        QualityInspectionHandlerFactory.register("气味强度", this);
        // 密度调整至比重
        // QualityInspectionHandlerFactory.register("密度", this);
    }
}
