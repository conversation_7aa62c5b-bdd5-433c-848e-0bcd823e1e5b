package com.haihang.model.Query.outbound;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Description:
 * @Author: zad
 * @Create: 2024/9/20 16:23
 */
@Data
@Accessors(chain = true)
public class TransferRecordQuery {
    private Integer current;
    private Integer size;
    private Integer total;

    private List<String> linkId;
    private String customerName;
    private String productName;
    private String productionBatch;
    private String qualityInspectionBatch;
    private Integer handleStatus;
    private Integer firstReviewStatus;
    private Integer secondReviewStatus;
    private Integer onlineAvailable;

    private String startDate;
    private String endDate;
}
